// Test Sender Email Service
// Run this to verify Sender integration

import axios from 'axios';

export async function testSenderEmail() {
  try {
    console.log('Testing Sender email service...');
    
    if (!process.env.SENDER_API_KEY) {
      console.error('❌ SENDER_API_KEY not found in environment variables');
      return;
    }
    
    const senderData = {
      from: {
        email: process.env.FROM_EMAIL || '<EMAIL>',
        name: 'WordWave Studio'
      },
      to: [
        {
          email: '<EMAIL>'
        }
      ],
      subject: 'Test Email from WordWave GDPR System - Sender',
      html: `
        <h1>Test Email via Sender</h1>
        <p>This is a test email to verify Sender integration.</p>
        <p><strong>Benefits of Sender:</strong></p>
        <ul>
          <li>15,000 free emails per month (vs 3,000 with Resend)</li>
          <li>Excellent deliverability</li>
          <li>Advanced analytics</li>
          <li>GDPR compliant</li>
        </ul>
        <p>Sent at: ${new Date().toISOString()}</p>
      `,
      text: 'This is a test email to verify Sender integration. Sender offers 15,000 free emails per month!'
    };
    
    const response = await axios.post(
      'https://api.sender.net/v2/email',
      senderData,
      {
        headers: {
          'Authorization': `Bearer ${process.env.SENDER_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    console.log('✅ Test email sent successfully via Sender:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Test email failed:', error);
    if (error.response) {
      console.error('❌ Sender API error response:', error.response.data);
    }
    throw error;
  }
}

// If running this file directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testSenderEmail()
    .then(() => console.log('✅ Sender test completed'))
    .catch(() => console.log('❌ Sender test failed'));
}
