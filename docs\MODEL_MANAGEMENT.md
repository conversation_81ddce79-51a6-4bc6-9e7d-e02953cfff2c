# Model Management System

This document describes the model management system that makes it easy to update Gemini and Gemma model IDs when new models become available.

## Overview

The system provides two ways to manage model configurations:

1. **JSON Configuration File** (default) - Static configuration that's fast and reliable
2. **Dynamic API Fetching** (optional) - Fetches latest models from Google's API

**Important**: The system only includes **Gemini** and **Gemma** models. Other model types (like Imagen, AQA, LearnLM, etc.) are automatically filtered out.

## Files Structure

```
src/
├── config/
│   └── models.json          # Static model configuration
├── services/
│   └── ModelService.ts      # Core model management logic
├── hooks/
│   └── useModels.tsx        # React hook for dynamic model loading
├── components/
│   └── ModelManager.tsx     # Admin component for managing models
└── lib/
    └── constants.ts         # Updated to use ModelService

scripts/
└── update-models.js         # Command-line script for updating models.json
```

## Usage

### 1. Static Configuration (Default)

The app uses the JSON configuration file by default:

```typescript
// constants.ts automatically loads from models.json
import { AVAILABLE_SCRIPT_MODELS, AVAILABLE_TTS_MODELS } from '../lib/constants';
```

### 2. Dynamic Model Loading

Use the React hook to load models dynamically:

```typescript
import { useModels } from '../hooks/useModels';

function MyComponent() {
  const { scriptModels, ttsModels, isLoading, error } = useModels({
    useAPI: false, // Use JSON config
    // useAPI: true, apiKey: 'your-api-key', // Use live API
  });

  // Use models in your component
}
```

### 3. Admin Interface

Include the ModelManager component for runtime model management:

```typescript
import ModelManager from '../components/ModelManager';

function AdminPage() {
  return (
    <div>
      <ModelManager apiKey="your-api-key" />
    </div>
  );
}
```

## Updating Models

### Method 1: Command Line Script (Recommended)

```bash
# Using API key as parameter
npm run update-models YOUR_API_KEY

# Or using environment variable
export GEMINI_API_KEY=your_api_key
npm run update-models
```

### Method 2: Manual JSON Update

Edit `src/config/models.json` directly:

```json
{
  "scriptModels": [
    {
      "name": "gemini-2.5-pro",
      "apiName": "gemini-2.5-pro",
      "description": "Enhanced reasoning and coding",
      "category": "premium"
    }
  ],
  "ttsModels": [
    {
      "name": "gemini-2.5-flash-preview-tts",
      "apiName": "gemini-2.5-flash-preview-tts",
      "description": "Fast text-to-speech",
      "category": "standard"
    }
  ],
  "lastUpdated": "2025-08-03T00:00:00Z",
  "source": "manual"
}
```

### Method 3: Browser Interface

1. Navigate to the ModelManager component
2. Enter your API key
3. Toggle "Use live API models" 
4. Click "Update from API"
5. Copy the JSON from the console
6. Update `src/config/models.json`
7. Restart the dev server

## Model Categories

Only **Gemini** and **Gemma** models are included in the system. All other model types (Imagen, AQA, LearnLM, etc.) are automatically filtered out.

Models are automatically categorized:

- **premium**: Models with "pro" in the name (e.g., gemini-2.5-pro)
- **lite**: Models with "lite" in the name (e.g., gemini-2.0-flash-lite)
- **standard**: All other models (e.g., gemini-2.5-flash, gemma-3-12b-it)

## API Reference

### ModelService

```typescript
// Get models from JSON config
const config = getModelsFromConfig();

// Get models with optional API fallback
const config = await getAvailableModels({
  useAPI: true,
  apiKey: 'your-key',
  forceRefresh: false
});

// Get specific model types
const scriptModels = await getScriptModels();
const ttsModels = await getTTSModels();
```

### useModels Hook

```typescript
const {
  scriptModels,    // Array of script models
  ttsModels,       // Array of TTS models
  isLoading,       // Loading state
  error,           // Error message if any
  lastUpdated,     // Last update timestamp
  refreshModels,   // Function to refresh models
} = useModels({
  useAPI: false,           // Use API instead of JSON config
  apiKey: undefined,       // API key for Google's API
  autoRefresh: false,      // Auto-refresh every 24 hours
});
```

## Migration from Old System

The old hardcoded constants in `constants.ts` have been replaced with dynamic loading from the JSON configuration. Your existing code should continue to work without changes.

## Security Notes

- API keys are only used client-side for admin functions
- The JSON configuration is the primary source for production
- API calls are cached for 24 hours to respect rate limits
- Always use environment variables for API keys in production
- For current pricing information, see: [Google Gemini API Pricing](https://ai.google.dev/gemini-api/docs/pricing)

## Troubleshooting

### Models not updating
1. Check that `models.json` was updated correctly
2. Restart the development server
3. Clear browser cache if needed

### API fetch errors
1. Verify your API key is valid
2. Check rate limits on your Google API account
3. Ensure network connectivity

### TypeScript errors
1. Models should have consistent interface (name, apiName, description, category)
2. Check that all imports are using the correct paths
