// Voice management hook for WordWave Studio
import { useState, useEffect, useCallback } from 'react';
import { getVoices, getVoicesFiltered, VoiceInfo } from '../services/ModelService';

interface UseVoicesOptions {
  useAPI?: boolean;
  apiKey?: string;
  autoRefresh?: boolean;
  filters?: {
    category?: string;
    gender?: string;
    accent?: string;
    ageRange?: string;
  };
}

interface UseVoicesReturn {
  voices: VoiceInfo[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
  refreshVoices: () => Promise<void>;
}

/**
 * Hook for managing voices with optional API integration
 */
export function useVoices(options: UseVoicesOptions = {}): UseVoicesReturn {
  const { useAPI = false, apiKey, autoRefresh = false, filters } = options;
  
  const [voices, setVoices] = useState<VoiceInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  const fetchVoices = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const fetchOptions = { useAPI, apiKey, forceRefresh: false };
      
      const fetchedVoices = filters 
        ? await getVoicesFiltered(filters, fetchOptions)
        : await getVoices(fetchOptions);
      
      setVoices(fetchedVoices);
      setLastUpdated(new Date().toISOString());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch voices';
      setError(errorMessage);
      console.error('Error fetching voices:', err);
    } finally {
      setIsLoading(false);
    }
  }, [useAPI, apiKey, filters]);

  const refreshVoices = useCallback(async () => {
    await fetchVoices();
  }, [fetchVoices]);

  // Initial fetch
  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchVoices();
    }, 24 * 60 * 60 * 1000); // 24 hours

    return () => clearInterval(interval);
  }, [autoRefresh, fetchVoices]);

  return {
    voices,
    isLoading,
    error,
    lastUpdated,
    refreshVoices,
  };
}

/**
 * Hook for getting voices by category
 */
export function useVoicesByCategory(category: string, options: Omit<UseVoicesOptions, 'filters'> = {}) {
  return useVoices({ ...options, filters: { category } });
}

/**
 * Hook for getting premium voices only
 */
export function usePremiumVoices(options: Omit<UseVoicesOptions, 'filters'> = {}) {
  return useVoices({ ...options, filters: { category: 'premium' } });
}

/**
 * Hook for getting standard voices only
 */
export function useStandardVoices(options: Omit<UseVoicesOptions, 'filters'> = {}) {
  return useVoices({ ...options, filters: { category: 'standard' } });
}
