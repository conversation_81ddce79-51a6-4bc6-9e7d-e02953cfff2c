/* Footer */
.footer {
    background: rgba(27, 38, 59, 0.8);
    border-top: 1px solid rgba(128, 152, 172, 0.2);
    padding: 60px 0 30px;
    margin-top: 80px; /* Add more space between content and footer */
    position: relative;
    z-index: 1;
}



.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand h3 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.5rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 12px;
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.footer-brand p {
    color: #B0C4DE;
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-column h4 {
    color: #CFD8DC;
    margin-bottom: 16px;
    font-weight: 600;
}

.footer-column a {
    display: block;
    color: #90A4AE;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #82AAE3;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(128, 152, 172, 0.2);
    color: #90A4AE;
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}
