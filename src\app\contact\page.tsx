'use client';

import React, { useState } from 'react';
import PageLayout from '@/components/PageLayout';
import { useContactForm, ContactFormData } from '@/hooks/useContactForm';

export default function ContactPage() {
  const { isLoading, isSuccess, error, submitForm, resetForm } = useContactForm();
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      return;
    }

    const result = await submitForm(formData);
    
    if (result.success) {
      // Reset form on success
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
      });
    }
  };

  const handleReset = () => {
    resetForm();
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: '',
    });
  };
  return (
    <PageLayout>
      <div className="container mx-auto px-4 pt-32 pb-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            Contact Us
          </h1>
          <p className="text-xl text-gray-300 text-center mb-12">
            Get in touch with our team. We're here to help!
          </p>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Send us a message</h2>
              
              {/* Success Message */}
              {isSuccess && (
                <div className="mb-6 p-4 bg-green-600/20 border border-green-500/50 rounded-lg">
                  <div className="flex items-center">
                    <div className="text-green-400 mr-3">✅</div>
                    <div>
                      <h3 className="text-green-300 font-semibold">Message Sent Successfully!</h3>
                      <p className="text-green-200 text-sm mt-1">
                        We'll get back to you within 24 hours during business days.
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={handleReset}
                    className="mt-3 text-green-300 hover:text-green-200 text-sm underline"
                  >
                    Send another message
                  </button>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="mb-6 p-4 bg-red-600/20 border border-red-500/50 rounded-lg">
                  <div className="flex items-center">
                    <div className="text-red-400 mr-3">❌</div>
                    <div>
                      <h3 className="text-red-300 font-semibold">Error Sending Message</h3>
                      <p className="text-red-200 text-sm mt-1">{error}</p>
                    </div>
                  </div>
                  <button
                    onClick={resetForm}
                    className="mt-3 text-red-300 hover:text-red-200 text-sm underline"
                  >
                    Try again
                  </button>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading || isSuccess}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Your full name"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading || isSuccess}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    disabled={isLoading || isSuccess}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed [&>option]:bg-gray-800 [&>option]:text-white [&>option]:border-none"
                  >
                    <option value="" className="bg-gray-800 text-white">Select a subject</option>
                    <option value="general" className="bg-gray-800 text-white">General Inquiry</option>
                    <option value="technical" className="bg-gray-800 text-white">Technical Support</option>
                    <option value="urgent" className="bg-gray-800 text-white">🚨 Urgent Technical Support</option>
                    <option value="billing" className="bg-gray-800 text-white">Billing Question</option>
                    <option value="feature" className="bg-gray-800 text-white">Feature Request</option>
                    <option value="partnership" className="bg-gray-800 text-white">Partnership</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={6}
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading || isSuccess}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Tell us how we can help you..."
                  ></textarea>
                </div>
                
                <button
                  type="submit"
                  disabled={isLoading || isSuccess || !formData.name.trim() || !formData.email.trim() || !formData.message.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending Message...
                    </>
                  ) : isSuccess ? (
                    '✅ Message Sent!'
                  ) : (
                    'Send Message'
                  )}
                </button>
              </form>
            </div>
            
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Other ways to reach us</h2>
              
              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-lg font-semibold text-white mb-2">General Support</h3>
                  <p className="text-gray-300">
                    For general inquiries, technical support, and assistance with your account, please use the contact form. We'll route your message to the right team member.
                  </p>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-lg font-semibold text-white mb-2">Business Partnerships</h3>
                  <p className="text-gray-300">
                    For enterprise partnerships, bulk licensing, and business collaborations, please use the contact form and select "Partnership" as your subject.
                  </p>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-lg font-semibold text-white mb-2">Response Time</h3>
                  <p className="text-gray-300">
                    We typically respond within 24 hours during business days. 
                    For urgent technical issues, please include "URGENT" in your subject line.
                  </p>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-lg font-semibold text-white mb-2">Business Hours</h3>
                  <p className="text-gray-300">
                    Monday - Friday: 9:00 AM - 6:00 PM (PST)<br />
                    Saturday - Sunday: Limited support
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
