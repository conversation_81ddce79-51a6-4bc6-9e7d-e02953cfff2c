import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
        audioId: string;
    }>
}

// DELETE /api/projects/[projectId]/audio/[audioId] - Delete audio file
export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId, audioId } = await context.params;

    console.log(`🗑️ Deleting audio ${audioId} for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // Delete the audio from cloud storage
    await cloudService.deleteAudio(audioId, projectId, userId);

    return NextResponse.json({
      success: true,
      data: { message: 'Audio deleted successfully' },
      meta: {
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error: any) {
    console.error('❌ Failed to delete audio:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          message: error.message || 'Failed to delete audio',
          code: error.code || 'DELETE_ERROR',
          details: error.details,
        },
      },
      { status: error.statusCode || 500 }
    );
  }
}
