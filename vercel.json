{"rewrites": [{"source": "/api/stripe/webhook", "destination": "/api/stripe/webhook"}], "redirects": [{"source": "/(.*)", "destination": "https://frontend-api.clerk.services/$1", "permanent": false, "has": [{"type": "host", "value": "clerk.wordwave.studio"}]}, {"source": "/(.*)", "destination": "https://accounts.clerk.services/$1", "permanent": false, "has": [{"type": "host", "value": "accounts.wordwave.studio"}]}], "headers": [{"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=********; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}]}]}