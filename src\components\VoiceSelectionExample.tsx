// Example component showing how to use the new voice management system
import React from 'react';
import { useVoices, usePremiumVoices, useStandardVoices } from '../hooks/useVoices';

export const VoiceSelectionExample: React.FC = () => {
  // Get all voices
  const { voices: allVoices, isLoading, error } = useVoices();
  
  // Get premium voices only
  const { voices: premiumVoices } = usePremiumVoices();
  
  // Get standard voices only
  const { voices: standardVoices } = useStandardVoices();
  
  // Get voices with specific filters
  const { voices: matureVoices } = useVoices({
    filters: { ageRange: 'mature', category: 'premium' }
  });

  if (isLoading) return <div>Loading voices...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="voice-selection-example">
      <h2>Voice Management Example</h2>
      
      <section>
        <h3>All Voices ({allVoices.length})</h3>
        <div className="voice-grid">
          {allVoices.map(voice => (
            <div key={voice.apiName} className="voice-card">
              <h4>{voice.name}</h4>
              <p>{voice.description}</p>
              <div className="voice-meta">
                <span className={`category ${voice.category}`}>{voice.category}</span>
                <span className="gender">{voice.gender}</span>
                <span className="age">{voice.ageRange}</span>
              </div>
            </div>
          ))}
        </div>
      </section>

      <section>
        <h3>Premium Voices ({premiumVoices.length})</h3>
        <select>
          <option value="">Select a premium voice...</option>
          {premiumVoices.map(voice => (
            <option key={voice.apiName} value={voice.apiName}>
              {voice.name} - {voice.description}
            </option>
          ))}
        </select>
      </section>

      <section>
        <h3>Standard Voices ({standardVoices.length})</h3>
        <select>
          <option value="">Select a standard voice...</option>
          {standardVoices.map(voice => (
            <option key={voice.apiName} value={voice.apiName}>
              {voice.name} - {voice.description}
            </option>
          ))}
        </select>
      </section>

      <section>
        <h3>Mature Premium Voices ({matureVoices.length})</h3>
        <select>
          <option value="">Select a mature premium voice...</option>
          {matureVoices.map(voice => (
            <option key={voice.apiName} value={voice.apiName}>
              {voice.name} - {voice.description}
            </option>
          ))}
        </select>
      </section>
    </div>
  );
};

export default VoiceSelectionExample;
