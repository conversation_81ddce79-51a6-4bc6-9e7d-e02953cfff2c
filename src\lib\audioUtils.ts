// Audio processing utilities for WordWave Studio

import { WavConversionOptions } from './types';

// Helper function to convert Base64 to ArrayBuffer
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = window.atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

// Helper to write string to DataView
export function writeString(view: DataView, offset: number, str: string) {
  for (let i = 0; i < str.length; i++) {
    view.setUint8(offset + i, str.charCodeAt(i));
  }
}

// Parses MimeType string to get audio parameters
export function parseMimeType(mimeType: string): WavConversionOptions {
  const [fileType, ...params] = mimeType.split(';').map(s => s.trim());
  const [_mediaType, format] = fileType.split('/');

  const options: Partial<WavConversionOptions> = {
    numChannels: 1,
    bitsPerSample: 16,
  };

  if (format && format.toUpperCase().startsWith('L')) {
    const bits = parseInt(format.substring(1), 10);
    if (!isNaN(bits)) {
      options.bitsPerSample = bits;
    }
  }

  for (const param of params) {
    const [key, value] = param.split('=').map(s => s.trim().toLowerCase());
    if (key === 'rate' || key === 'samplerate') {
      options.sampleRate = parseInt(value, 10);
    } else if (key === 'channels') {
      options.numChannels = parseInt(value, 10);
    }
  }

  if (!options.sampleRate) {
    console.warn(`Sample rate not found in mime type "${mimeType}", defaulting to 24000.`);
    options.sampleRate = 24000;
  }
  if (!options.numChannels) options.numChannels = 1;
  if (!options.bitsPerSample) options.bitsPerSample = 16;

  return options as WavConversionOptions;
}

// Creates a WAV header ArrayBuffer
export function createWavHeader(dataLength: number, options: WavConversionOptions): ArrayBuffer {
  const { numChannels, sampleRate, bitsPerSample } = options;
  const byteRate = sampleRate * numChannels * bitsPerSample / 8;
  const blockAlign = numChannels * bitsPerSample / 8;
  const buffer = new ArrayBuffer(44);
  const view = new DataView(buffer);

  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + dataLength, true);
  writeString(view, 8, 'WAVE');
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);
  writeString(view, 36, 'data');
  view.setUint32(40, dataLength, true);

  return buffer;
}

// Converts raw audio data (base64) to WAV ArrayBuffer
export function convertToWav(base64RawData: string, mimeType: string): { data: ArrayBuffer, mimeType: string } {
  const options = parseMimeType(mimeType);
  const rawDataBuffer = base64ToArrayBuffer(base64RawData);
  const header = createWavHeader(rawDataBuffer.byteLength, options);

  const wavArray = new Uint8Array(header.byteLength + rawDataBuffer.byteLength);
  wavArray.set(new Uint8Array(header), 0);
  wavArray.set(new Uint8Array(rawDataBuffer), header.byteLength);

  return { data: wavArray.buffer, mimeType: 'audio/wav' };
}

// Helper function to convert AudioBuffer to WAV ArrayBuffer
export function audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
  const numOfChan = buffer.numberOfChannels;
  const length = buffer.length * numOfChan * 2 + 44; // 2 bytes per sample (16-bit)
  const wavBuffer = new ArrayBuffer(length);
  const view = new DataView(wavBuffer);
  const channels = [];
  let i, sample;
  let offset = 0;
  let pos = 0;

  // Write WAV container
  writeString(view, pos, 'RIFF'); pos += 4;
  view.setUint32(pos, length - 8, true); pos += 4;
  writeString(view, pos, 'WAVE'); pos += 4;
  writeString(view, pos, 'fmt '); pos += 4;
  view.setUint32(pos, 16, true); pos += 4; // Subchunk1Size (16 for PCM)
  view.setUint16(pos, 1, true); pos += 2; // AudioFormat (1 for PCM)
  view.setUint16(pos, numOfChan, true); pos += 2;
  view.setUint32(pos, buffer.sampleRate, true); pos += 4;
  view.setUint32(pos, buffer.sampleRate * 2 * numOfChan, true); pos += 4; // ByteRate
  view.setUint16(pos, numOfChan * 2, true); pos += 2; // BlockAlign
  view.setUint16(pos, 16, true); pos += 2; // BitsPerSample
  writeString(view, pos, 'data'); pos += 4;
  view.setUint32(pos, length - pos - 4, true); pos += 4;

  // Write PCM samples
  for (i = 0; i < numOfChan; i++) {
    channels.push(buffer.getChannelData(i));
  }

  while (pos < length) {
    for (i = 0; i < numOfChan; i++) {
      sample = Math.max(-1, Math.min(1, channels[i][offset])); // Clamp
      sample = (sample < 0 ? sample * 0x8000 : sample * 0x7FFF); // Scale to 16-bit signed int
      view.setInt16(pos, sample, true);
      pos += 2;
    }
    offset++;
  }
  return wavBuffer;
}

// Parse script content to extract utterances
export function parseScript(scriptContent: string, synthesisMode: 'podcast' | 'monologue'): Array<{ speakerIndex: number; text: string }> {
  if (synthesisMode === 'monologue') {
    return [{ speakerIndex: 0, text: scriptContent.trim() }];
  }

  const utterances: Array<{ speakerIndex: number; text: string }> = [];
  const lines = scriptContent.split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Match "Speaker 1:" or "Speaker 2:" patterns
    const speakerMatch = trimmedLine.match(/^Speaker\s+(\d+):\s*(.*)$/i);
    if (speakerMatch) {
      const speakerNumber = parseInt(speakerMatch[1], 10);
      const text = speakerMatch[2].trim();
      
      if (text && speakerNumber >= 1 && speakerNumber <= 2) {
        utterances.push({
          speakerIndex: speakerNumber - 1, // Convert to 0-based index
          text: text
        });
      }
    }
  }

  return utterances;
}

// Generate a unique ID for projects
export function generateProjectId(): string {
  return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
