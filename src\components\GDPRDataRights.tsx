'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Download, Trash2, Shield, Eye, Settings, CheckCircle, AlertCircle } from 'lucide-react';
import { Modal } from './Modal';
import InfoBanner from './InfoBanner';

interface DataRightRequest {
  id: string;
  type: 'export' | 'delete' | 'rectification';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedAt: string;
  completedAt?: string;
  downloadUrl?: string;
  expiresAt?: string;
}

const GDPRDataRights: React.FC = () => {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState<'overview' | 'requests' | 'preferences'>('overview');
  const [requests, setRequests] = useState<DataRightRequest[]>([]);
  const [isExportLoading, setIsExportLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null);
  
  // Modal states for confirmations
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    step: 'initial' | 'final';
  }>({ isOpen: false, step: 'initial' });
  
  const [cookieResetModal, setCookieResetModal] = useState(false);

  // Debug modal state changes
  useEffect(() => {
    console.log('🔍 Modal state changed:', deleteConfirmModal);
  }, [deleteConfirmModal]);

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  // Fetch user's GDPR requests on component mount
  useEffect(() => {
    if (user && activeTab === 'requests') {
      // Load requests from localStorage on component mount
      const storageKey = `gdpr-requests-${user.id}`;
      const storedRequests = localStorage.getItem(storageKey);
      if (storedRequests) {
        try {
          const parsedRequests = JSON.parse(storedRequests);
          console.log('📋 Loaded requests from localStorage:', parsedRequests);
          setRequests(parsedRequests);
        } catch (error) {
          console.error('Error parsing stored requests:', error);
        }
      }
      
      fetchUserRequests();
    }
  }, [user, activeTab]);

  // Save requests to localStorage whenever they change
  useEffect(() => {
    if (user && requests.length > 0) {
      const storageKey = `gdpr-requests-${user.id}`;
      localStorage.setItem(storageKey, JSON.stringify(requests));
      console.log('💾 Saved requests to localStorage:', requests);
    }
  }, [user, requests]);

  // Poll for status updates when there are active requests
  useEffect(() => {
    if (requests.some(req => req.status === 'pending' || req.status === 'processing')) {
      const interval = setInterval(() => {
        fetchUserRequests();
      }, 10000); // Poll every 10 seconds

      return () => clearInterval(interval);
    }
  }, [requests]);

  const fetchUserRequests = async () => {
    try {
      console.log('📋 Fetching user requests from API...');
      const response = await fetch('/api/gdpr/status');
      if (response.ok) {
        const data = await response.json();
        console.log('📋 API response:', data);
        
        // Since we're no longer using background jobs, the API returns empty jobs
        // We should preserve locally stored completed requests
        if (data.success && data.jobs && data.jobs.length > 0) {
          const formattedRequests = data.jobs.map((job: any) => ({
            id: job.id,
            type: job.type === 'data-export' ? 'export' : job.type === 'account-deletion' ? 'delete' : 'rectification',
            status: job.status === 'queued' || job.status === 'processing' ? 'processing' : 
                    job.status === 'completed' ? 'completed' : 'failed',
            requestedAt: job.createdAt,
            completedAt: job.completedAt
          }));
          setRequests(formattedRequests);
        } else {
          console.log('📋 No jobs from API, keeping current requests in state');
          // Don't clear existing requests if API returns empty - preserve local state
        }
      }
    } catch (error) {
      console.error('Error fetching user requests:', error);
    }
  };

  const addRequestToHistory = (newRequest: DataRightRequest) => {
    console.log('➕ Adding request to history:', newRequest);
    setRequests(prev => {
      // Check if request already exists to avoid duplicates
      const exists = prev.some(req => req.id === newRequest.id);
      if (exists) {
        console.log('⚠️ Request already exists, not adding duplicate');
        return prev;
      }
      
      const updatedRequests = [newRequest, ...prev];
      console.log('📋 Updated requests list:', updatedRequests);
      return updatedRequests;
    });
  };

  const handleDataExport = async (event?: React.MouseEvent) => {
    event?.preventDefault();
    event?.stopPropagation();
    
    if (!user) return;
    
    console.log('🔄 Data export button clicked for user:', user.id);
    
    setIsExportLoading(true);
    try {
      console.log('📤 Sending request to /api/gdpr/export...');
      
      // Call API to process export immediately
      const response = await fetch('/api/gdpr/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id }),
      });
      
      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));
      
      const responseText = await response.text();
      console.log('📥 Raw response text:', responseText);
      
      if (response.ok) {
        let data;
        try {
          data = JSON.parse(responseText);
          console.log('✅ Parsed response data:', data);
        } catch (parseError) {
          console.error('❌ Failed to parse response as JSON:', parseError);
          throw new Error('Invalid response format');
        }
        
        if (!data.success) {
          console.error('❌ API returned success=false:', data);
          throw new Error(data.error || 'API returned failure');
        }
        
        if (!data.exportData) {
          console.error('❌ No exportData in response:', data);
          throw new Error('No export data received');
        }
        
        console.log('📋 Creating completed request object...', data.exportData);
        
        // Create completed request object
        const completedRequest: DataRightRequest = {
          id: data.exportData.requestId,
          type: 'export',
          status: 'completed',
          requestedAt: data.exportData.requestedAt,
          completedAt: data.exportData.completedAt,
          downloadUrl: data.exportData.downloadUrl,
          expiresAt: data.exportData.expiresAt
        };
        
        console.log('➕ Adding request to list:', completedRequest);
        addRequestToHistory(completedRequest);
        
        showNotification('success', 'Data export completed! Check your email for the download link.');
      } else {
        let errorData;
        try {
          errorData = JSON.parse(responseText);
        } catch {
          errorData = { error: responseText || 'Unknown error' };
        }
        console.error('❌ HTTP error response:', errorData);
        throw new Error(errorData.error || `HTTP ${response.status} error`);
      }
    } catch (error) {
      console.error('💥 Export error:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      showNotification('error', `Failed to process data export: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExportLoading(false);
    }
  };

  const handleAccountDeletion = async (event?: React.MouseEvent) => {
    event?.preventDefault();
    event?.stopPropagation();
    
    if (!user) return;
    
    console.log('🗑️ Account deletion button clicked for user:', user.id);
    
    // Start with initial confirmation modal
    setDeleteConfirmModal({ isOpen: true, step: 'initial' });
  };

  const handleDeleteConfirmation = () => {
    console.log('🔍 handleDeleteConfirmation called, current step:', deleteConfirmModal.step);
    if (deleteConfirmModal.step === 'initial') {
      // Move to final confirmation - prevent modal from closing
      console.log('🔄 Moving to final confirmation step');
      setDeleteConfirmModal({ isOpen: true, step: 'final' });
      return false; // Prevent modal from closing
    } else {
      // Actually proceed with deletion - allow modal to close
      console.log('✅ Proceeding with account deletion');
      proceedWithAccountDeletion();
      return true; // Allow modal to close (will be closed by onClose in Modal)
    }
  };

  const proceedWithAccountDeletion = async () => {
    setIsDeleteLoading(true);
    try {
      const requestId = `delete_${Date.now()}`;
      const newRequest: DataRightRequest = {
        id: requestId,
        type: 'delete',
        status: 'pending',
        requestedAt: new Date().toISOString(),
      };
      
      setRequests(prev => [newRequest, ...prev]);
      
      const response = await fetch('/api/gdpr/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user?.id }),
      });
      
      if (response.ok) {
        showNotification('info', 'Account deletion request submitted successfully! An email has been sent to our admin team for manual review. You will be contacted shortly to confirm this request.');
        setRequests(prev => prev.map(req => 
          req.id === requestId ? { ...req, status: 'processing' } : req
        ));
      } else {
        throw new Error('Failed to submit deletion request');
      }
    } catch (error) {
      showNotification('error', 'Failed to submit deletion request. Please try again.');
      setRequests(prev => prev.filter(req => req.id !== `delete_${Date.now()}`));
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleCookiePreferences = () => {
    setCookieResetModal(true);
  };

  const handleCookieReset = () => {
    setCookieResetModal(false);
    // Reset cookie consent to show the banner again
    const { clearCookieConsent } = require('@/utils/gdprUtils');
    clearCookieConsent();
    window.location.reload();
  };

  const getStatusIcon = (status: DataRightRequest['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
    }
  };

  const getStatusText = (status: DataRightRequest['status']) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'processing': return 'Processing';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
    }
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">Please sign in to manage your data rights.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Notification */}
      {notification && (
        <InfoBanner
          type={notification.type}
          message={notification.message}
          className="mb-6"
          icon={
            notification.type === 'success' ? '✅' :
            notification.type === 'error' ? '❌' :
            'ℹ️'
          }
        />
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-8 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview', icon: Shield },
          { id: 'requests', label: 'Data Requests', icon: Eye },
          { id: 'preferences', label: 'Privacy Settings', icon: Settings },
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <Icon className="w-4 h-4" />
            {label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold text-white mb-4">Your Data Rights</h2>
            <p className="text-gray-300 mb-6">
              Under GDPR, you have several rights regarding your personal data. You can exercise these rights at any time.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Right to Access */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-4">
                <Download className="w-6 h-6 text-blue-400" />
                <h3 className="text-xl font-semibold text-white">Right to Access</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Request a copy of all personal data we have about you, including your projects, usage data, and account information.
              </p>
              <button
                onClick={handleDataExport}
                disabled={isExportLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                {isExportLoading ? 'Processing...' : 'Request Data Export'}
              </button>
            </div>

            {/* Right to Erasure */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-4">
                <Trash2 className="w-6 h-6 text-red-400" />
                <h3 className="text-xl font-semibold text-white">Right to Erasure</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Request complete deletion of your account and all associated data. This action is irreversible.
              </p>
              <button
                onClick={handleAccountDeletion}
                disabled={isDeleteLoading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                {isDeleteLoading ? 'Processing...' : 'Delete My Account'}
              </button>
            </div>

            {/* Right to Rectification */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-4">
                <Settings className="w-6 h-6 text-yellow-400" />
                <h3 className="text-xl font-semibold text-white">Right to Rectification</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Update or correct your personal information in your account settings.
              </p>
              <button
                onClick={() => {
                  // Redirect to Clerk's user profile page or open in new tab
                  const clerkProfileUrl = `/user-profile`;
                  window.location.href = clerkProfileUrl;
                }}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                Update Profile
              </button>
            </div>

            {/* Cookie Preferences */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-4">
                <Settings className="w-6 h-6 text-green-400" />
                <h3 className="text-xl font-semibold text-white">Cookie Preferences</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Manage your cookie consent and tracking preferences.
              </p>
              <button
                onClick={handleCookiePreferences}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                Manage Cookies
              </button>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'requests' && (
        <div>
          <h2 className="text-2xl font-semibold text-white mb-6">Data Request History</h2>
          
          {requests.length === 0 ? (
            <div className="text-center py-8">
              <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">No data requests yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {requests.map((request) => (
                <div key={request.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(request.status)}
                      <div>
                        <h3 className="font-medium text-white capitalize">
                          {request.type === 'export' ? 'Data Export' : 
                           request.type === 'delete' ? 'Account Deletion' : 
                           'Data Rectification'}
                        </h3>
                        <p className="text-sm text-gray-400">
                          Requested: {new Date(request.requestedAt).toLocaleDateString()}
                          {request.completedAt && (
                            <> • Completed: {new Date(request.completedAt).toLocaleDateString()}</>
                          )}
                        </p>
                        {request.expiresAt && (
                          <p className="text-xs text-orange-400">
                            Download expires: {new Date(request.expiresAt).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {request.status === 'completed' && request.downloadUrl && request.type === 'export' && (
                        <a
                          href={request.downloadUrl}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors inline-flex items-center gap-1"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Download className="w-4 h-4" />
                          Download
                        </a>
                      )}
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        request.status === 'completed' ? 'bg-green-900/50 text-green-300' :
                        request.status === 'failed' ? 'bg-red-900/50 text-red-300' :
                        'bg-blue-900/50 text-blue-300'
                      }`}>
                        {getStatusText(request.status)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'preferences' && (
        <div>
          <h2 className="text-2xl font-semibold text-white mb-6">Privacy Settings</h2>
          
          <div className="space-y-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">Data Processing Consent</h3>
              <p className="text-gray-300 mb-4">
                We process your data based on your consent and our legitimate interests. You can withdraw consent at any time.
              </p>
              <div className="space-y-3">
                <label className="flex items-center gap-3">
                  <input type="checkbox" defaultChecked className="rounded" />
                  <span className="text-gray-300">Analytics and performance tracking</span>
                </label>
                <label className="flex items-center gap-3">
                  <input type="checkbox" defaultChecked className="rounded" />
                  <span className="text-gray-300">Product updates and announcements</span>
                </label>
                <label className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-gray-300">Marketing and promotional content</span>
                </label>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">Data Retention</h3>
              <p className="text-gray-300 mb-4">
                We retain your data only as long as necessary to provide our services. 
                You can request deletion at any time from the overview tab.
              </p>
              <div className="text-sm text-gray-400">
                <p>• Account data: Retained until account deletion</p>
                <p>• Project files: Retained until manual deletion or account deletion</p>
                <p>• Usage logs: Retained for 24 months for analytics</p>
                <p>• Support communications: Retained for 36 months</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Account Deletion Confirmation Modals */}
      <Modal
        isOpen={deleteConfirmModal.isOpen}
        onClose={() => setDeleteConfirmModal({ isOpen: false, step: 'initial' })}
        title={deleteConfirmModal.step === 'initial' ? 'Delete Account' : 'Final Confirmation'}
        message={
          deleteConfirmModal.step === 'initial'
            ? 'Are you sure you want to delete your account? This action is irreversible and will delete all your data, projects, and subscription information.'
            : 'This is your final warning. Clicking "Delete Account" will permanently delete your account and all associated data. This cannot be undone.'
        }
        type="warning"
        confirmText={deleteConfirmModal.step === 'initial' ? 'Continue' : 'Delete Account'}
        onConfirm={handleDeleteConfirmation}
      />

      {/* Cookie Reset Confirmation Modal */}
      <Modal
        isOpen={cookieResetModal}
        onClose={() => setCookieResetModal(false)}
        title="Reset Cookie Preferences"
        message="This will reset your cookie preferences and reload the page to show the cookie consent banner again. Continue?"
        type="info"
        confirmText="Reset Preferences"
        onConfirm={handleCookieReset}
      />
    </div>
  );
};

export default GDPRDataRights;
