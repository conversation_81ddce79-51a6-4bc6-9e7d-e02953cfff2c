import { NextRequest, NextResponse } from 'next/server';
import { EmailService } from '@/lib/EmailService';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    console.log('📬 Contact form submission received');
    
    const body: ContactFormData = await request.json();
    const { name, email, subject, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, and message are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Create email service instance
    const emailService = EmailService.getInstance();

    // Prepare email content
    const subjectLine = subject || 'New Contact Form Submission';
    const emailContent = `
      <h2>New Contact Form Submission</h2>
      <p><strong>From:</strong> ${name} (${email})</p>
      <p><strong>Subject:</strong> ${subjectLine}</p>
      <p><strong>Message:</strong></p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 10px;">
        ${message.replace(/\n/g, '<br>')}
      </div>
      <hr>
      <p><small>This message was sent via the WordWave Studio contact form.</small></p>
    `;

    // Send notification email to admin
    const adminEmailSuccess = await emailService.sendEmailPublic({
      to: process.env.ADMIN_EMAIL || '<EMAIL>', // Use configured admin email
      subject: `Contact Form: ${subjectLine}`,
      html: emailContent
    });

    // Send confirmation email to user
    const confirmationContent = `
      <h2>Thank you for contacting WordWave Studio!</h2>
      <p>Hi ${name},</p>
      <p>We've received your message and will get back to you within 24 hours during business days.</p>
      
      <h3>Your Message:</h3>
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;">
        <p><strong>Subject:</strong> ${subjectLine}</p>
        <p><strong>Message:</strong></p>
        <p>${message.replace(/\n/g, '<br>')}</p>
      </div>
      
      <p>If you have an urgent technical issue, please include "URGENT" in your subject line for faster response.</p>
      
      <p>Best regards,<br>
      The WordWave Studio Team</p>
      
      <hr>
      <p><small>This is an automated confirmation email. Please do not reply to this message.</small></p>
    `;

    const userEmailSuccess = await emailService.sendEmailPublic({
      to: email,
      subject: 'Thank you for contacting WordWave Studio',
      html: confirmationContent
    });

    if (adminEmailSuccess) {
      console.log('✅ Contact form email sent successfully');
      return NextResponse.json(
        { 
          message: 'Message sent successfully! We\'ll get back to you within 24 hours.',
          adminEmailSent: adminEmailSuccess,
          confirmationEmailSent: userEmailSuccess
        },
        { status: 200 }
      );
    } else {
      console.error('❌ Failed to send contact form email');
      return NextResponse.json(
        { error: 'Failed to send message. Please try again or contact us <NAME_EMAIL>' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Contact form API error:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}
