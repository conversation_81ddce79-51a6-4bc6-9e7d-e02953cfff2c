import React from 'react';
import PageLayout from '@/components/PageLayout';

export default function HelpCenterPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            Help Center
          </h1>
          <p className="text-xl text-gray-300 text-center mb-12">
            Find answers to frequently asked questions and get the help you need
          </p>
          
          <div className="space-y-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-xl font-semibold text-white mb-4">Frequently Asked Questions</h2>
              
              <div className="space-y-4">
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">How do I get started with WordWave Studio?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    Simply sign up for a free account, choose your content type (podcast or monologue), 
                    define your topic and style, then let our AI generate your script and convert it to audio.
                  </div>
                </details>
                
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">What AI models do you use for script generation?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    We use Google's advanced Gemini language models to generate high-quality, 
                    engaging scripts tailored to your specific requirements and style preferences.
                  </div>
                </details>
                
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">How many voices are available?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    We offer a variety of high-quality voices across different languages and accents. 
                    All users have access to premium voices with your subscription plan.
                  </div>
                </details>
                
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">Can I create multi-speaker conversations?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    Yes! All users can create realistic conversations between multiple speakers 
                    with distinct voices and personalities, perfect for interview-style podcasts.
                  </div>
                </details>
                
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">What audio formats can I export?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    All users have access to both MP3 and high-quality WAV formats for professional use.
                  </div>
                </details>
                
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer text-white hover:text-blue-300 transition-colors">
                    <span className="font-medium">Is there a limit on audio generation?</span>
                    <span className="ml-2 group-open:rotate-180 transition-transform">▼</span>
                  </summary>
                  <div className="mt-3 text-gray-300 pl-4">
                    With our BYOK (Bring Your Own Key) model, usage is tracked but not artificially limited. 
                    Your usage depends on your Google AI API quota and subscription plan.
                  </div>
                </details>
              </div>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-xl font-semibold text-white mb-4">Contact Support</h3>
                <p className="text-gray-300 mb-4">
                  Need personalized help? Our support team is ready to assist you.
                </p>
                <a 
                  href="/contact" 
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
                >
                  Contact Us
                </a>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-xl font-semibold text-white mb-4">Documentation</h3>
                <p className="text-gray-300 mb-4">
                  Explore our comprehensive guides and tutorials.
                </p>
                <a 
                  href="/documentation" 
                  className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
                >
                  View Docs
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
