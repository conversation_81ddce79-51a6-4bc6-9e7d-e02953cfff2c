'use client';

import { UserProfile } from '@clerk/nextjs';

export default function UserProfilePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-4">User Profile</h1>
            <p className="text-gray-300">Manage your account information and preferences</p>
          </div>
          
          <div className="flex justify-center">
            <UserProfile 
              path="/user-profile"
              routing="path"
              appearance={{
                baseTheme: undefined,
                variables: {
                  colorPrimary: '#3b82f6',
                  colorBackground: 'rgba(15, 23, 42, 0.8)',
                  colorInputBackground: 'rgba(255, 255, 255, 0.1)',
                  colorInputText: '#ffffff',
                  colorText: '#ffffff',
                  colorTextSecondary: '#cbd5e1',
                  borderRadius: '0.5rem',
                },
                elements: {
                  rootBox: {
                    backgroundColor: 'rgba(15, 23, 42, 0.8)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '0.75rem',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                  },
                  card: {
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none',
                  },
                  headerTitle: {
                    color: '#ffffff',
                  },
                  headerSubtitle: {
                    color: '#cbd5e1',
                  },
                }
              }}
            />
          </div>
          
          <div className="text-center mt-8">
            <a 
              href="/privacy-dashboard" 
              className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
            >
              ← Back to Privacy Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
