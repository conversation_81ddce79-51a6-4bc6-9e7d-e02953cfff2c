<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordWave Studio - AI-Powered Podcast Creation</title>
    <meta name="description" content="Create engaging podcast scripts and professional audio with AI. Generate realistic conversations, monologues, and speech synthesis in minutes.">
    <meta name="keywords" content="AI podcast, script generation, text to speech, voice synthesis, podcast creation, audio content">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:title" content="WordWave Studio - AI-Powered Podcast Creation">
    <meta property="og:description" content="Create engaging podcast scripts and professional audio with AI. Generate realistic conversations, monologues, and speech synthesis in minutes.">
    <meta property="og:image" content="">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="">
    <meta property="twitter:title" content="WordWave Studio - AI-Powered Podcast Creation">
    <meta property="twitter:description" content="Create engaging podcast scripts and professional audio with AI. Generate realistic conversations, monologues, and speech synthesis in minutes.">
    <meta property="twitter:image" content="">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Import Map for React and modules -->
    <script type="importmap">
    {
      "imports": {
        "react": "https://esm.sh/react@18.2.0",
        "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
        "react/": "https://esm.sh/react@18.2.0/",
        "react-dom/": "https://esm.sh/react-dom@18.2.0/",
        "framer-motion": "https://esm.sh/framer-motion@11.15.0"
      }
    }
    </script>

    <!-- Styles -->
    <link rel="stylesheet" href="landing.css">
    <link rel="stylesheet" href="Waves.css">
    <link rel="stylesheet" href="Noise.css">
    <link rel="stylesheet" href="RotatingText.css">
</head>
<body>
    <!-- Background Elements - waves removed -->

    <!-- React Landing Page Root -->
    <div id="landing-root"></div>

    <!-- Scripts -->
    <script type="module" src="landing.js"></script>
    <script type="module">
        // Import React and components
        import React from 'react';
        import ReactDOM from 'react-dom/client';
        import Waves from './Waves.js';
        import Noise from './Noise.js';
        import LandingPage from './LandingPage.js';

        // Initialize React landing page
        try {
            const landingRoot = document.getElementById('landing-root');
            if (landingRoot) {
                const reactRoot = ReactDOM.createRoot(landingRoot);
                reactRoot.render(
                    React.createElement(React.StrictMode, null,
                        React.createElement(LandingPage)
                    )
                );
            }
        } catch (error) {
            console.error('Failed to mount React landing page:', error);
        }

        // Background components removed - waves and noise disabled
    </script>
</body>
</html>
