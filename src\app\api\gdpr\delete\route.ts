import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { BackgroundJobQueue } from '@/lib/BackgroundJobQueue';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId: requestUserId } = await req.json();
    
    // Verify user can only delete their own account
    if (userId !== requestUserId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    console.log(`🗑️ Account deletion requested for user: ${userId}`);

    const jobQueue = BackgroundJobQueue.getInstance();
    const requestId = `delete_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    // Queue the account deletion job
    const job = await jobQueue.queueAccountDeletion(userId, requestId);

    console.log(`📋 Queued account deletion job: ${job.id} for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Account deletion request submitted successfully',
      requestId,
      jobId: job.id,
      status: job.status,
      notice: 'Your account deletion request has been queued for processing. This action is irreversible and will delete all your data, projects, and subscription information.',
      warning: 'Account deletion is permanent and cannot be undone'
    });

  } catch (error) {
    console.error('Error processing account deletion request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
