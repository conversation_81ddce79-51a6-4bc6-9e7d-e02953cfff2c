import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GDPRDataExportService } from '@/lib/GDPRDataExportService';

export async function POST(req: NextRequest) {
  try {
    console.log('🚀 GDPR Export API - Starting request processing');
    
    const { userId } = await auth();
    console.log('🔐 Auth result - userId:', userId);
    
    if (!userId) {
      console.log('❌ No userId found - returning unauthorized');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    console.log('📋 Request body:', body);
    
    const { userId: requestUserId } = body;
    console.log('🔍 Requested userId:', requestUserId, 'Auth userId:', userId);
    
    // Verify user can only request their own data
    if (userId !== requestUserId) {
      console.log('❌ User ID mismatch - returning forbidden');
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    console.log(`📋 Data export requested for user: ${userId}`);
    
    // Generate unique request ID
    const requestId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log('🆔 Generated request ID:', requestId);
    
    // Check environment variables
    console.log('🔧 Environment check:');
    console.log('- RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'Present' : 'Missing');
    console.log('- FROM_EMAIL:', process.env.FROM_EMAIL ? 'Present' : 'Missing');
    console.log('- ADMIN_EMAIL:', process.env.ADMIN_EMAIL ? 'Present' : 'Missing');
    console.log('- NEXT_PUBLIC_APP_URL:', process.env.NEXT_PUBLIC_APP_URL || 'Missing');
    console.log('- B2_APPLICATION_KEY_ID:', process.env.B2_APPLICATION_KEY_ID ? 'Present' : 'Missing');
    console.log('- B2_APPLICATION_KEY:', process.env.B2_APPLICATION_KEY ? 'Present' : 'Missing');
    console.log('- B2_BUCKET_NAME:', process.env.B2_BUCKET_NAME || 'Missing');
    
    console.log('🏗️ Creating GDPR export service...');
    
    // Process export directly (Vercel-compatible approach)
    const exportService = new GDPRDataExportService();
    console.log('✅ Export service created, starting processing...');
    
    const result = await exportService.processDataExport(userId, requestId);
    console.log('📊 Export processing result:', {
      status: result.status,
      id: result.id,
      hasDownloadUrl: !!result.downloadUrl,
      hasErrorMessage: !!result.errorMessage
    });
    
    if (result.status === 'failed') {
      console.error(`❌ Data export failed for user: ${userId}`, result.errorMessage);
      return NextResponse.json({
        success: false,
        error: result.errorMessage || 'Export processing failed'
      }, { status: 500 });
    }
    
    const exportData = {
      requestId: result.id,
      userId,
      requestedAt: result.requestedAt,
      completedAt: result.completedAt,
      status: result.status,
      downloadUrl: result.downloadUrl,
      expiresAt: result.expiresAt,
      dataTypes: [
        'Account information',
        'Profile data',
        'Project files and metadata',
        'Usage analytics',
        'Subscription information',
        'Support communications'
      ]
    };

    console.log(`✅ Data export completed successfully for user: ${userId}`);
    console.log('📤 Sending response with export data:', {
      requestId: exportData.requestId,
      status: exportData.status,
      hasDownloadUrl: !!exportData.downloadUrl
    });

    return NextResponse.json({
      success: true,
      message: 'Data export completed successfully. You will receive an email with the download link.',
      exportData
    });

  } catch (error) {
    console.error('💥 CRITICAL ERROR in GDPR export API:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('Error message:', error instanceof Error ? error.message : String(error));
    
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// GET endpoint to check export status - now simplified since exports are immediate
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Since exports are now processed immediately, we can return a simple status
    return NextResponse.json({
      success: true,
      message: 'GDPR exports are now processed immediately. No background jobs to check.',
      jobs: []
    });

  } catch (error) {
    console.error('Error getting export status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
