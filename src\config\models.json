{"scriptModels": [{"name": "models/gemini-1.5-flash", "apiName": "models/gemini-1.5-flash", "description": "Alias that points to the most recent stable version of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks.", "category": "standard"}, {"name": "models/gemini-1.5-flash-002", "apiName": "models/gemini-1.5-flash-002", "description": "Stable version of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks, released in September of 2024.", "category": "standard"}, {"name": "models/gemini-1.5-flash-8b", "apiName": "models/gemini-1.5-flash-8b", "description": "Stable version of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "category": "standard"}, {"name": "models/gemini-1.5-flash-8b-001", "apiName": "models/gemini-1.5-flash-8b-001", "description": "Stable version of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "category": "standard"}, {"name": "models/gemini-1.5-flash-8b-latest", "apiName": "models/gemini-1.5-flash-8b-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "category": "standard"}, {"name": "models/gemini-1.5-flash-latest", "apiName": "models/gemini-1.5-flash-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks.", "category": "standard"}, {"name": "models/gemini-1.5-pro", "apiName": "models/gemini-1.5-pro", "description": "Stable version of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens, released in May of 2024.", "category": "premium"}, {"name": "models/gemini-1.5-pro-002", "apiName": "models/gemini-1.5-pro-002", "description": "Stable version of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens, released in September of 2024.", "category": "premium"}, {"name": "models/gemini-1.5-pro-latest", "apiName": "models/gemini-1.5-pro-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens.", "category": "premium"}, {"name": "models/gemini-2.0-flash", "apiName": "models/gemini-2.0-flash", "description": "Gemini 2.0 Flash", "category": "standard"}, {"name": "models/gemini-2.0-flash-001", "apiName": "models/gemini-2.0-flash-001", "description": "Stable version of Gemini 2.0 Flash, our fast and versatile multimodal model for scaling across diverse tasks, released in January of 2025.", "category": "standard"}, {"name": "models/gemini-2.0-flash-exp", "apiName": "models/gemini-2.0-flash-exp", "description": "Gemini 2.0 Flash Experimental", "category": "standard"}, {"name": "models/gemini-2.0-flash-lite", "apiName": "models/gemini-2.0-flash-lite", "description": "Gemini 2.0 Flash-Lite", "category": "lite"}, {"name": "models/gemini-2.0-flash-lite-001", "apiName": "models/gemini-2.0-flash-lite-001", "description": "Stable version of Gemini 2.0 Flash-Lite", "category": "lite"}, {"name": "models/gemini-2.0-flash-lite-preview", "apiName": "models/gemini-2.0-flash-lite-preview", "description": "Preview release (February 5th, 2025) of Gemini 2.0 Flash-Lite", "category": "lite"}, {"name": "models/gemini-2.0-flash-lite-preview-02-05", "apiName": "models/gemini-2.0-flash-lite-preview-02-05", "description": "Preview release (February 5th, 2025) of Gemini 2.0 Flash-Lite", "category": "lite"}, {"name": "models/gemini-2.0-flash-thinking-exp", "apiName": "models/gemini-2.0-flash-thinking-exp", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "category": "standard"}, {"name": "models/gemini-2.0-flash-thinking-exp-01-21", "apiName": "models/gemini-2.0-flash-thinking-exp-01-21", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "category": "standard"}, {"name": "models/gemini-2.0-flash-thinking-exp-1219", "apiName": "models/gemini-2.0-flash-thinking-exp-1219", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "category": "standard"}, {"name": "models/gemini-2.0-pro-exp", "apiName": "models/gemini-2.0-pro-exp", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "category": "premium"}, {"name": "models/gemini-2.0-pro-exp-02-05", "apiName": "models/gemini-2.0-pro-exp-02-05", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "category": "premium"}, {"name": "models/gemini-2.5-flash", "apiName": "models/gemini-2.5-flash", "description": "Stable version of Gemini 2.5 Flash, our mid-size multimodal model that supports up to 1 million tokens, released in June of 2025.", "category": "standard"}, {"name": "models/gemini-2.5-flash-lite", "apiName": "models/gemini-2.5-flash-lite", "description": "Stable verion of Gemini 2.5 Flash-Lite, released in July of 2025", "category": "lite"}, {"name": "models/gemini-2.5-flash-lite-preview-06-17", "apiName": "models/gemini-2.5-flash-lite-preview-06-17", "description": "Preview release (June 11th, 2025) of Gemini 2.5 Flash-Lite", "category": "lite"}, {"name": "models/gemini-2.5-flash-preview-05-20", "apiName": "models/gemini-2.5-flash-preview-05-20", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "category": "standard"}, {"name": "models/gemini-2.5-pro", "apiName": "models/gemini-2.5-pro", "description": "Stable release (June 17th, 2025) of Gemini 2.5 Pro", "category": "premium"}, {"name": "models/gemini-2.5-pro-preview-03-25", "apiName": "models/gemini-2.5-pro-preview-03-25", "description": "Gemini 2.5 Pro Preview 03-25", "category": "premium"}, {"name": "models/gemini-2.5-pro-preview-05-06", "apiName": "models/gemini-2.5-pro-preview-05-06", "description": "Preview release (May 6th, 2025) of Gemini 2.5 Pro", "category": "premium"}, {"name": "models/gemini-2.5-pro-preview-06-05", "apiName": "models/gemini-2.5-pro-preview-06-05", "description": "Preview release (June 5th, 2025) of Gemini 2.5 Pro", "category": "premium"}, {"name": "models/gemini-exp-1206", "apiName": "models/gemini-exp-1206", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "category": "standard"}, {"name": "models/gemma-3-12b-it", "apiName": "models/gemma-3-12b-it", "description": "", "category": "standard"}, {"name": "models/gemma-3-1b-it", "apiName": "models/gemma-3-1b-it", "description": "", "category": "standard"}, {"name": "models/gemma-3-27b-it", "apiName": "models/gemma-3-27b-it", "description": "", "category": "standard"}, {"name": "models/gemma-3-4b-it", "apiName": "models/gemma-3-4b-it", "description": "", "category": "standard"}, {"name": "models/gemma-3n-e2b-it", "apiName": "models/gemma-3n-e2b-it", "description": "", "category": "standard"}, {"name": "models/gemma-3n-e4b-it", "apiName": "models/gemma-3n-e4b-it", "description": "", "category": "standard"}], "ttsModels": [{"name": "models/gemini-2.5-flash-preview-tts", "apiName": "models/gemini-2.5-flash-preview-tts", "description": "Gemini 2.5 Flash Preview TTS", "category": "standard"}, {"name": "models/gemini-2.5-pro-preview-tts", "apiName": "models/gemini-2.5-pro-preview-tts", "description": "Gemini 2.5 Pro Preview TTS", "category": "premium"}], "voices": [{"name": "<PERSON><PERSON><PERSON>", "apiName": "acherna<PERSON>", "description": "<PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON>", "apiName": "achird", "description": "Achird voice for text-to-speech", "category": "standard"}, {"name": "Algenib", "apiName": "algenib", "description": "<PERSON>gen<PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Algieba", "apiName": "algieba", "description": "Algieba voice for text-to-speech", "category": "standard"}, {"name": "Al<PERSON><PERSON>", "apiName": "alnilam", "description": "<PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Aoede", "apiName": "aoede", "description": "<PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Autonoe", "apiName": "autonoe", "description": "Autonoe voice for text-to-speech", "category": "standard"}, {"name": "Callirrhoe", "apiName": "call<PERSON>r<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>", "apiName": "charon", "description": "<PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>", "apiName": "despina", "description": "<PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Encelad<PERSON>", "apiName": "enceladus", "description": "Enceladus voice for text-to-speech", "category": "standard"}, {"name": "Erin<PERSON>", "apiName": "erinome", "description": "Erinome voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON>", "apiName": "fenrir", "description": "<PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Gacrux", "apiName": "gac<PERSON>x", "description": "Gacrux voice for text-to-speech", "category": "standard"}, {"name": "Iapetus", "apiName": "iapetus", "description": "Iapetus voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>", "apiName": "kore", "description": "<PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "apiName": "laomedeia", "description": "<PERSON><PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>", "apiName": "leda", "description": "<PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Orus", "apiName": "orus", "description": "Orus voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>", "apiName": "puck", "description": "Puck voice for text-to-speech", "category": "standard"}, {"name": "Pulcherrima", "apiName": "pulcherrima", "description": "<PERSON><PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "apiName": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Sadachbia", "apiName": "sad<PERSON><PERSON>", "description": "Sadachbia voice for text-to-speech", "category": "standard"}, {"name": "Sadaltager", "apiName": "sadal<PERSON>r", "description": "Sadaltager voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON>", "apiName": "schedar", "description": "<PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}, {"name": "Sulafat", "apiName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sulafat voice for text-to-speech", "category": "standard"}, {"name": "Umbriel", "apiName": "umbriel", "description": "Umbriel voice for text-to-speech", "category": "standard"}, {"name": "Vindemiatrix", "apiName": "vindemiatrix", "description": "Vindemiatrix voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "apiName": "zephyr", "description": "<PERSON><PERSON><PERSON>r voice for text-to-speech", "category": "standard"}, {"name": "<PERSON><PERSON>elgenubi", "apiName": "<PERSON>benelgenubi", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> voice for text-to-speech", "category": "standard"}], "lastUpdated": "2025-08-04T22:50:25.265Z", "source": "api"}