// Enhanced Project Save/Load Component for WordWave Studio
import React, { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';

import { ProjectData, ProjectConfiguration, AudioFile } from '../lib/types';
import { createProjectConfig, createScript, createAudio, generateScriptName, generateAudioName } from '../utils/SeparatedProjectUtils';
import { useSeparatedApiClient } from '../services/SeparatedApiClient';
import { generateProjectId } from '../lib/audioUtils';
import Modal from './Modal';
import './ProjectSaveLoad.css';

interface ProjectVersion {
  id: string;
  version: number;
  timestamp: string;
  scriptContent?: string;
  audioFileCount: number;
  description: string;
}

interface ProjectSaveLoadProps {
  // Current project state
  projectName: string;
  topic: string;
  scriptLinks: string;
  synthesisMode: 'monologue' | 'podcast';
  selectedScriptModel: string;
  selectedTtsModel: string;
  voice1: string;
  voice2: string;
  speaker1Name: string;
  speaker2Name: string;
  projectStyle: string;
  wordCount: number;
  inputText: string;
  audioFiles: AudioFile[];
  
  // Callbacks
  onProjectLoaded?: (projectData: {
    projectName: string;
    topic: string;
    scriptLinks: string;
    synthesisMode: 'monologue' | 'podcast';
    selectedScriptModel: string;
    selectedTtsModel: string;
    voice1: string;
    voice2: string;
    speaker1Name: string;
    speaker2Name: string;
    projectStyle: string;
    wordCount: number;
    inputText: string;
    audioFiles: AudioFile[];
  }) => void;
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
  onProjectSaved?: (projectId: string, projectName: string) => void;
  onUsageIncrement?: (feature: keyof import('@/hooks/useSubscription').UsageStats, amount: number) => Promise<void>;
  disabled?: boolean;
  className?: string;
  projectId?: string | null;
  // New: allow parent to open the Load dialog programmatically
  openLoadDialogExternal?: boolean;
  onLoadDialogClosed?: () => void;
  // New: control whether to show the Load Project button
  showLoadButton?: boolean;
}

export const ProjectSaveLoad: React.FC<ProjectSaveLoadProps> = ({
  projectName,
  topic,
  scriptLinks,
  synthesisMode,
  selectedScriptModel,
  selectedTtsModel,
  voice1,
  voice2,
  speaker1Name,
  speaker2Name,
  projectStyle,
  wordCount,
  inputText,
  audioFiles,
  onProjectLoaded,
  onError,
  onSuccess,
  onProjectSaved,
  onUsageIncrement,
  disabled = false,
  className = '',
  projectId: loadedProjectId,
  openLoadDialogExternal,
  onLoadDialogClosed,
  showLoadButton = true
}) => {
  const { user, isSignedIn } = useUser();
  const separatedApiClient = useSeparatedApiClient();
  
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [projectVersions, setProjectVersions] = useState<ProjectVersion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);
  const [showVersionDialog, setShowVersionDialog] = useState(false);
  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);
  const [saveDescription, setSaveDescription] = useState('');
  const [deleteConfirmModal, setDeleteConfirmModal] = useState({
    isOpen: false,
    projectId: '',
    projectName: ''
  });

  // Skip health check to avoid rate limiting - assume healthy if signed in
  // Add a small delay to ensure authentication token is ready
  useEffect(() => {
    if (isSignedIn) {
      // Small delay to ensure Clerk auth token is available
      const timer = setTimeout(() => {
        setIsHealthy(true);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setIsHealthy(false);
    }
  }, [isSignedIn]);

  // Load projects list (new separated approach)
  const loadProjects = useCallback(async () => {
    if (!isSignedIn || !isHealthy) return;

    setIsLoading(true);
    try {
      console.log(`🔄 Frontend: Loading projects list...`);
      const projects = await separatedApiClient.listProjects();
      console.log(`✅ Frontend: Loaded ${projects?.length || 0} projects:`, projects?.map(p => ({ id: p.projectId, name: p.projectName })));
      setProjects(projects || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load projects';
      console.error(`❌ Frontend: Failed to load projects:`, err);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [isSignedIn, isHealthy, separatedApiClient, onError]);

  // Load projects when service is ready - only run once when isHealthy becomes true
  useEffect(() => {
    if (isHealthy && isSignedIn) {
      loadProjects();
    }
  }, [isHealthy, isSignedIn]); // Removed loadProjects from dependencies to prevent infinite loop

  // Generate version description
  const generateVersionDescription = (hasScript: boolean, audioCount: number): string => {
    const parts = [];
    if (hasScript) parts.push('Script');
    if (audioCount > 0) parts.push(`${audioCount} audio file${audioCount > 1 ? 's' : ''}`);
    return parts.length > 0 ? parts.join(' + ') : 'Configuration only';
  };

  // Save project configuration only (new separated approach)
  const handleSaveProject = async () => {
    if (!isSignedIn || !user || !projectName.trim() || !isHealthy) {
      onError?.('Please ensure you have a project name, are signed in, and cloud storage is available');
      return;
    }

    setIsSaving(true);
    try {
      // Refresh projects list to check for duplicates
      await loadProjects();

      // Check for duplicate project names
      const trimmedProjectName = projectName.trim();
      const existingProject = projects.find(project =>
        project.projectName.toLowerCase() === trimmedProjectName.toLowerCase()
      );

      if (existingProject) {
        onError?.(`A project named "${trimmedProjectName}" already exists. Please choose a different name.`);
        setIsSaving(false);
        return;
      }
      // Create project configuration only
      const projectData = createProjectConfig(
        projectName,
        topic,
        scriptLinks,
        synthesisMode,
        selectedScriptModel,
        selectedTtsModel,
        voice1,
        voice2,
        speaker1Name,
        speaker2Name,
        projectStyle,
        wordCount,
        user.id
      );

      // Add version description if provided
      if (saveDescription.trim()) {
        projectData.metadata.description = saveDescription.trim();
      } else {
        projectData.metadata.description = `Project: ${projectName.trim()}`;
      }

      // Save project configuration
      const projectResult = await separatedApiClient.saveProject(projectData);
      const savedProjectId = projectResult.data.projectId;

      // Save script separately if available
      if (inputText.trim()) {
        const scriptData = createScript(
          savedProjectId,
          generateScriptName(projectName),
          inputText,
          selectedScriptModel,
          user.id
        );
        await separatedApiClient.saveScript(savedProjectId, scriptData);
      }

      // Save audio separately if available and has valid data
      if (audioFiles.length > 0) {
        // Only save NEW audio files (with blob URLs), not existing ones loaded from storage
        const newAudioFiles = audioFiles.filter(file =>
          file.url && file.url.startsWith('blob:') && file.size && file.size > 0
        );

        if (newAudioFiles.length > 0) {
          console.log(`🔄 Saving ${newAudioFiles.length} new audio files (out of ${audioFiles.length} total)`);
          const audioData = createAudio(
            savedProjectId,
            generateAudioName(projectName, synthesisMode, voice1, voice2),
            newAudioFiles, // Only save new audio files
            selectedTtsModel,
            voice1,
            voice2,
            synthesisMode,
            user.id
          );
          await separatedApiClient.saveAudio(savedProjectId, audioData);
        } else {
          console.log(`ℹ️ No new audio files to save (${audioFiles.length} existing files loaded from storage)`);
        }
      }

      onSuccess?.(`Project "${projectName}" saved successfully! ${inputText.trim() ? 'Script saved. ' : ''}${audioFiles.length > 0 ? 'Audio saved.' : ''}`);
      onProjectSaved?.(savedProjectId, projectName.trim());
      setSaveDescription('');
      await loadProjects(); // Refresh the list
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save project';
      onError?.(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateProject = async () => {
    if (!isSignedIn || !user || !loadedProjectId || !isHealthy) {
      onError?.('Please ensure you are signed in, a project is loaded, and cloud storage is available');
      return;
    }

    setIsUpdating(true);
    try {
      const projectData = createProjectConfig(
        projectName,
        topic,
        scriptLinks,
        synthesisMode,
        selectedScriptModel,
        selectedTtsModel,
        voice1,
        voice2,
        speaker1Name,
        speaker2Name,
        projectStyle,
        wordCount,
        user.id
      );

      if (saveDescription.trim()) {
        projectData.metadata.description = saveDescription.trim();
      }

      // Use the separatedApiClient for updating the project configuration
      await separatedApiClient.updateProject(loadedProjectId, projectData);

      // Still use separated client for scripts and audio as per existing logic
      if (inputText.trim()) {
        const scriptData = createScript(
          loadedProjectId,
          generateScriptName(projectName),
          inputText,
          selectedScriptModel,
          user.id
        );
        await separatedApiClient.saveScript(loadedProjectId, scriptData);
      }

      if (audioFiles.length > 0) {
        // Only save NEW audio files (with blob URLs), not existing ones loaded from storage
        const newAudioFiles = audioFiles.filter(file =>
          file.url && file.url.startsWith('blob:') && file.size && file.size > 0
        );

        if (newAudioFiles.length > 0) {
          console.log(`🔄 Saving ${newAudioFiles.length} new audio files (out of ${audioFiles.length} total)`);
          const audioData = createAudio(
            loadedProjectId,
            generateAudioName(projectName, synthesisMode, voice1, voice2),
            newAudioFiles, // Only save new audio files
            selectedTtsModel,
            voice1,
            voice2,
            synthesisMode,
            user.id
          );
          console.log(`📊 Preparing to save ${newAudioFiles.length} new audio files for project update`);
          console.log(`🔍 Audio files details:`, newAudioFiles.map(f => ({ 
            name: f.name, 
            hasUrl: !!f.url, 
            isBlob: f.url?.startsWith('blob:'), 
            size: f.size 
          })));
          
          await separatedApiClient.saveAudio(loadedProjectId, audioData);
        } else {
          console.log(`ℹ️ No new audio files to save (${audioFiles.length} existing files loaded from storage)`);
        }
      }

      onSuccess?.(`Project "${projectName}" updated successfully!`);
      setSaveDescription('');
    } catch (err) {
      console.error('❌ Project update failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update project';
      onError?.(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  // Load project configuration and all associated audio files
  const handleLoadProject = async (projectId: string) => {
    if (!isSignedIn) return;

    console.log(`🚀 Starting handleLoadProject for: ${projectId}`);
    setIsLoading(true);
    try {
      // Load project configuration
      console.log(`📋 Loading project configuration for: ${projectId}`);
      const projectData = await separatedApiClient.loadProject(projectId);
      console.log(`✅ Project configuration loaded:`, projectData.configuration.projectName);

      // Load all audio files for this project
      let allAudioFiles: any[] = [];
      try {
        console.log(`🎵 Loading audio files for project ${projectId}...`);
        const audioList = await separatedApiClient.listAudio(projectId);
        console.log(`📁 Found ${audioList.length} audio files for project ${projectId}`);
        console.log(`📋 Raw audio list:`, audioList);

        // Convert audio metadata to the format expected by the UI
        allAudioFiles = audioList.map((audio: any) => {
          // Calculate total size from individual files if available
          const totalSize = audio.files?.reduce((sum: number, file: any) => sum + (file.size || 0), 0) || audio.totalSize || 0;

          // Only use duration if it's a valid, finite number
          const validDuration = audio.duration && isFinite(audio.duration) && audio.duration > 0 ? audio.duration : undefined;

          const audioFile = {
            id: audio.id,
            name: audio.name,
            url: `/api/projects/${projectId}/audio/${audio.id}/download?t=${Date.now()}`, // Add timestamp to prevent caching issues
            processedMimeType: 'audio/wav',
            size: totalSize, // Use calculated size from files or metadata
            duration: validDuration, // Only set duration if it's valid
            createdAt: audio.createdAt,
            voiceConfig: audio.voiceConfig,
            scriptContent: audio.scriptContent,
            scriptTopic: audio.scriptTopic,
            generationId: audio.id,
            ttsModel: audio.ttsModel,
            synthesisMode: audio.synthesisMode,
          };
          console.log(`🎵 Processed audio file: ${audioFile.name} (${audioFile.size} bytes)`);
          return audioFile;
        });
        
        console.log(`✅ Successfully processed ${allAudioFiles.length} audio files for project ${projectId}`);
      } catch (audioError) {
        console.error('❌ Failed to load audio files for project:', audioError);
        console.warn('📝 Project will load without audio files. This may be normal if no audio has been generated yet.');
        // Continue without audio files if loading fails
      }

      // Convert ProjectData to the format expected by the parent component
      const deserializedProject = {
        id: projectId, // Include project ID
        projectName: projectData.configuration.projectName,
        topic: projectData.configuration.topic,
        scriptLinks: projectData.configuration.scriptLinks,
        synthesisMode: projectData.configuration.synthesisMode,
        selectedScriptModel: projectData.configuration.selectedScriptModel,
        selectedTtsModel: projectData.configuration.selectedTtsModel,
        voice1: projectData.configuration.defaultVoice1,
        voice2: projectData.configuration.defaultVoice2,
        speaker1Name: projectData.configuration.speaker1Name,
        speaker2Name: projectData.configuration.speaker2Name,
        projectStyle: projectData.configuration.projectStyle,
        wordCount: projectData.configuration.wordCount,
        inputText: '', // Scripts are loaded separately now
        audioFiles: allAudioFiles // Load all audio files generated for this project
      };

      console.log(`🎯 Final project data being sent to parent:`, {
        projectName: deserializedProject.projectName,
        audioFilesCount: deserializedProject.audioFiles.length,
        audioFileNames: deserializedProject.audioFiles.map(f => f.name)
      });

      onProjectLoaded?.(deserializedProject);
      onSuccess?.(`Project "${deserializedProject.projectName}" loaded successfully! Found ${allAudioFiles.length} audio files.`);
      setShowLoadDialog(false);
      setShowVersionDialog(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load project';
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Delete project
  const handleDeleteProject = async (projectId: string, projectName: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the load action

    if (!isSignedIn) return;

    // Open the confirmation modal instead of using browser confirm
    setDeleteConfirmModal({
      isOpen: true,
      projectId,
      projectName
    });
  };

  const confirmDeleteProject = () => {
    if (!isSignedIn || !separatedApiClient) return;
    
    const { projectId, projectName } = deleteConfirmModal;

    // Start the deletion process asynchronously
    (async () => {
      setIsLoading(true);
      try {
        console.log(`🗑️ Frontend: Starting deletion of project ${projectId} (${projectName})`);
        await separatedApiClient.deleteProject(projectId);
        console.log(`✅ Frontend: Project ${projectId} deleted successfully`);
        onSuccess?.(`Project "${projectName}" deleted successfully.`);

        // Add a longer delay to ensure backend processing is complete and avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Refresh the projects list
        console.log(`🔄 Frontend: Refreshing projects list after deletion`);
        await loadProjects();
        console.log(`✅ Frontend: Projects list refreshed`);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to delete project';
        console.error(`❌ Frontend: Failed to delete project ${projectId}:`, err);
        onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    })();
    
    // Close the modal immediately
    setDeleteConfirmModal({
      isOpen: false,
      projectId: '',
      projectName: ''
    });
  };

  const canSave = isSignedIn && isHealthy && projectName.trim() && !disabled;

  // Open/close load dialog from parent trigger
  useEffect(() => {
    if (openLoadDialogExternal) {
      setShowLoadDialog(true);
    }
  }, [openLoadDialogExternal]);

  // When dialog closes, notify parent
  useEffect(() => {
    if (!showLoadDialog && openLoadDialogExternal && onLoadDialogClosed) {
      onLoadDialogClosed();
    }
  }, [showLoadDialog]);

  if (!isSignedIn) {
    return (
      <div className={`project-save-load ${className}`}>
        <div className="save-load-buttons">
          <button type="button" disabled className="save-button">
            🔒 Sign in to Save/Load
          </button>
        </div>
      </div>
    );
  }

  if (isHealthy === false) {
    return (
      <div className={`project-save-load ${className}`}>
        <div className="save-load-buttons">
          <button type="button" disabled className="save-button">
            ⚠️ Cloud Storage Unavailable
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`project-save-load ${className}`}>
      <div className="save-load-buttons">
        {loadedProjectId ? (
          <button
            type="button"
            onClick={handleUpdateProject}
            disabled={!canSave || isUpdating}
            className="save-button"
          >
            {isUpdating ? '☁️ Updating...' : '💾 Update Project'}
          </button>
        ) : (
          <button
            type="button"
            onClick={handleSaveProject}
            disabled={!canSave || isSaving}
            className="save-button"
          >
            {isSaving ? '☁️ Saving...' : '💾 Save Project'}
          </button>
        )}
        
        {showLoadButton && (
          <button
            type="button"
            onClick={() => setShowLoadDialog(true)}
            disabled={isLoading || projects.length === 0}
            className="load-button"
          >
            📥 Load Project
          </button>
        )}
      </div>

      {/* Optional save description */}
      {canSave && (
        <div className="save-description">
          <input
            type="text"
            value={saveDescription}
            onChange={(e) => setSaveDescription(e.target.value)}
            placeholder="Optional: Describe this version..."
            className="description-input"
          />
        </div>
      )}

      {/* Load Project Dialog */}
      {showLoadDialog && (
        <div className="dialog-overlay">
          <div className="dialog">
            <div className="dialog-header">
              <h3>Load Project</h3>
              <button type="button" onClick={() => setShowLoadDialog(false)} className="close-button">×</button>
            </div>
            <div className="dialog-content">
              {isLoading ? (
                <div className="loading">Loading projects...</div>
              ) : projects.length === 0 ? (
                <div className="empty-state">No saved projects found</div>
              ) : (
                <div className="projects-list">
                  {projects.map((project) => (
                    <div
                      key={project.projectId}
                      className="project-item"
                    >
                      <div
                        className="project-info"
                        onClick={() => handleLoadProject(project.projectId)}
                      >
                        <div className="project-name">{project.projectName}</div>
                        <div className="project-meta">
                          Updated: {new Date(project.updatedAt).toLocaleDateString()}
                        </div>
                        <div className="project-description">
                          {project.description || 'No description'}
                        </div>
                      </div>
                      <div className="project-actions">
                        <button
                          type="button"
                          onClick={() => handleLoadProject(project.projectId)}
                          disabled={isLoading}
                          className="load-action-btn"
                          title="Load project"
                        >
                          📥
                        </button>
                        <button
                          type="button"
                          onClick={(e) => handleDeleteProject(project.projectId, project.projectName, e)}
                          disabled={isLoading}
                          className="delete-action-btn"
                          title="Delete project"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteConfirmModal.isOpen}
        onClose={() => setDeleteConfirmModal({ isOpen: false, projectId: '', projectName: '' })}
        title="Delete Project"
        message={`Are you sure you want to delete the project "${deleteConfirmModal.projectName}"? This will delete all associated scripts and audio files. This action cannot be undone.`}
        type="warning"
        confirmText="Delete Project"
        onConfirm={confirmDeleteProject}
      />
    </div>
  );
};
