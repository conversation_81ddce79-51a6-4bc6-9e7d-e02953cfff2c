// Utilities for the new separated project/script/audio structure
import { ProjectData, ScriptData, AudioData, AudioFile } from '@/lib/types';
import { generateProjectId } from '@/lib/audioUtils';

/**
 * Generate a unique script ID
 */
export function generateScriptId(): string {
  return `script_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a unique audio ID
 */
export function generateAudioId(): string {
  return `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create a new project configuration (no scripts or audio)
 */
export function createProjectConfig(
  projectName: string,
  topic: string,
  scriptLinks: string,
  synthesisMode: 'monologue' | 'podcast',
  selectedScriptModel: string,
  selectedTtsModel: string,
  defaultVoice1: string,
  defaultVoice2: string,
  speaker1Name: string,
  speaker2Name: string,
  projectStyle: string,
  wordCount: number,
  userId: string
): ProjectData {
  const now = new Date().toISOString();
  
  return {
    id: generateProjectId(),
    configuration: {
      projectName: projectName.trim(),
      topic: topic.trim(),
      scriptLinks: scriptLinks.trim(),
      synthesisMode,
      selectedScriptModel,
      selectedTtsModel,
      voice1: defaultVoice1,
      voice2: defaultVoice2,
      defaultVoice1,
      defaultVoice2,
      speaker1Name: speaker1Name.trim(),
      speaker2Name: speaker2Name.trim(),
      projectStyle: projectStyle.trim(),
      wordCount,
      createdAt: now,
      updatedAt: now,
    },
    metadata: {
      version: '2.0.0', // New version for separated structure
      createdAt: now,
      updatedAt: now,
      userId,
      totalSize: 0,
      fileCount: 0,
      description: `Project: ${projectName.trim()}`,
    },
  };
}

/**
 * Create a new script for a project
 */
export function createScript(
  projectId: string,
  name: string,
  content: string,
  model: string,
  userId: string,
  tokenCount?: {
    prompt?: number;
    candidates?: number;
    total?: number;
  }
): ScriptData {
  const now = new Date().toISOString();
  
  return {
    id: generateScriptId(),
    projectId,
    name: name.trim(),
    content: content.trim(),
    generatedAt: now,
    model,
    tokenCount,
    metadata: {
      version: '1.0.0',
      createdAt: now,
      updatedAt: now,
      userId,
      size: content.length,
      description: `Script: ${name.trim()}`,
      wordCount: content.trim().split(/\s+/).length,
    },
  };
}

/**
 * Create a new audio generation for a project
 */
export function createAudio(
  projectId: string,
  name: string,
  files: AudioFile[],
  ttsModel: string,
  voice1: string,
  voice2: string,
  synthesisMode: 'monologue' | 'podcast',
  userId: string,
  scriptId?: string
): AudioData {
  const now = new Date().toISOString();
  const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);

  // Use actual duration from files if available, otherwise don't estimate
  const totalDuration = files.reduce((sum, file) => {
    // Only use actual duration from the file, don't estimate from size
    return sum + (file.duration || 0);
  }, 0);

  // Extract script content and topic from the first file (they should be the same across all files)
  const firstFile = files[0];
  const scriptContent = firstFile?.scriptContent;
  const scriptTopic = firstFile?.scriptTopic;
  const generationId = firstFile?.generationId; // Extract generation ID from files

  const audioId = generateAudioId();

  return {
    id: audioId,
    projectId,
    scriptId,
    name: name.trim(),
    files,
    generatedAt: now,
    ttsModel,
    voiceConfig: {
      voice1,
      voice2,
      synthesisMode,
    },
    // Include script content and topic at the audio level
    scriptContent,
    scriptTopic,
    // Include generation ID for deduplication
    generationId: generationId || audioId, // Use file's generation ID or fallback to audio ID
    metadata: {
      version: '1.0.0',
      createdAt: now,
      updatedAt: now,
      userId,
      totalSize,
      fileCount: files.length,
      description: `Audio: ${name.trim()}`,
      duration: totalDuration,
    },
  };
}

/**
 * Generate a default script name based on project and timestamp
 */
export function generateScriptName(projectName: string, index?: number): string {
  const timestamp = new Date().toLocaleString();
  const suffix = index ? ` v${index}` : '';
  return `${projectName} Script${suffix} - ${timestamp}`;
}

/**
 * Generate a default audio name based on project and voice config
 */
export function generateAudioName(
  projectName: string, 
  synthesisMode: 'monologue' | 'podcast',
  voice1: string,
  voice2?: string,
  index?: number
): string {
  const timestamp = new Date().toLocaleString();
  const suffix = index ? ` v${index}` : '';
  const voiceInfo = synthesisMode === 'podcast' 
    ? `${voice1} & ${voice2}` 
    : voice1;
  return `${projectName} Audio${suffix} (${voiceInfo}) - ${timestamp}`;
}

/**
 * Convert legacy project data to separated structure
 */
export function migrateLegacyProject(legacyProject: any): {
  project: ProjectData;
  script?: ScriptData;
  audio?: AudioData;
} {
  const now = new Date().toISOString();
  
  // Create project configuration
  const project: ProjectData = {
    id: legacyProject.id,
    configuration: {
      projectName: legacyProject.configuration.projectName,
      topic: legacyProject.configuration.topic,
      scriptLinks: legacyProject.configuration.scriptLinks,
      synthesisMode: legacyProject.configuration.synthesisMode,
      selectedScriptModel: legacyProject.configuration.selectedScriptModel,
      selectedTtsModel: legacyProject.configuration.selectedTtsModel,
      voice1: legacyProject.configuration.voice1 || legacyProject.configuration.defaultVoice1,
      voice2: legacyProject.configuration.voice2 || legacyProject.configuration.defaultVoice2,
      defaultVoice1: legacyProject.configuration.voice1 || legacyProject.configuration.defaultVoice1,
      defaultVoice2: legacyProject.configuration.voice2 || legacyProject.configuration.defaultVoice2,
      speaker1Name: legacyProject.configuration.speaker1Name,
      speaker2Name: legacyProject.configuration.speaker2Name,
      projectStyle: legacyProject.configuration.projectStyle,
      wordCount: legacyProject.configuration.wordCount,
      createdAt: legacyProject.configuration.createdAt || now,
      updatedAt: now,
    },
    metadata: {
      ...legacyProject.metadata,
      version: '2.0.0',
      updatedAt: now,
    },
  };

  // Create script if exists
  let script: ScriptData | undefined;
  if (legacyProject.script?.content) {
    script = createScript(
      project.id,
      generateScriptName(project.configuration.projectName),
      legacyProject.script.content,
      legacyProject.script.model || project.configuration.selectedScriptModel,
      legacyProject.metadata.userId,
      legacyProject.script.tokenCount
    );
  }

  // Create audio if exists
  let audio: AudioData | undefined;
  if (legacyProject.audioFiles?.length > 0) {
    audio = createAudio(
      project.id,
      generateAudioName(
        project.configuration.projectName,
        project.configuration.synthesisMode,
        project.configuration.defaultVoice1,
        project.configuration.defaultVoice2
      ),
      legacyProject.audioFiles,
      project.configuration.selectedTtsModel,
      project.configuration.defaultVoice1,
      project.configuration.defaultVoice2,
      project.configuration.synthesisMode,
      legacyProject.metadata.userId,
      script?.id
    );
  }

  return { project, script, audio };
}
