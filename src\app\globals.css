@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Custom font families */
  --font-fredoka: 'Fredoka One', sans-serif;
  --font-roboto: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  /* Wave background colors */
  --wave-line-color: #1e293b;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.roboto-font {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Global heading styles - All headings use Fredoka One font */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Fredoka One', sans-serif;
  color: #E0E7FF; /* Bright white color for headings */
}

/* Utility classes for fonts */
.font-fredoka {
  font-family: 'Fredoka One', sans-serif;
}

.font-roboto {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Progress bar styles */
.progress-bar {
  width: var(--progress-width, 0%);
}

/* Custom responsive utilities */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline;
  }

  .xs\:hidden {
    display: none;
  }
}
