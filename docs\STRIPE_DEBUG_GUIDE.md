# Vercel Stripe Configuration Debug Guide

## Step 1: Check Stripe Health Endpoint

After deploying, visit this URL to check your Stripe configuration:
```
https://www.wordwave.studio/api/stripe/health
```

This will tell you:
- Which environment variables are missing
- If your Stripe API key is valid
- Whether you're using test or live mode
- Your configured price IDs

## Step 2: Required Environment Variables in Vercel

Go to https://vercel.com/dashboard → Your Project → Settings → Environment Variables

### Critical Variables (Set for "Production" environment):

```bash
# Clerk Configuration
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_PRODUCTION_KEY
CLERK_SECRET_KEY=sk_live_YOUR_PRODUCTION_SECRET

# Stripe Configuration (MUST BE LIVE KEYS FOR PRODUCTION)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY

# Stripe Price IDs (MUST BE LIVE PRICE IDS)
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_YOUR_LIVE_MONTHLY_PRICE
NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID=price_YOUR_LIVE_LIFETIME_PRICE

# Webhook Secret (MUST BE FROM LIVE WEBHOOK)
STRIPE_WEBHOOK_SECRET=whsec_YOUR_LIVE_WEBHOOK_SECRET

# Customer Portal URL
NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/28E28q5652vEaoI5tUa7C00

# Other Configuration
B2_APPLICATION_KEY_ID=your_b2_key_id
B2_APPLICATION_KEY=your_b2_key
B2_BUCKET_NAME=your_bucket_name
ADMIN_EMAIL=your_admin_email
RESEND_API_KEY=your_resend_key
FROM_EMAIL=your_from_email
NEXT_PUBLIC_APP_URL=https://www.wordwave.studio
```

## Step 3: Verify Stripe Keys

### Check if you're using test vs live keys:
- **Test keys** start with `pk_test_` and `sk_test_`
- **Live keys** start with `pk_live_` and `sk_live_`

For production, you MUST use live keys!

### Get your live keys:
1. Go to https://dashboard.stripe.com
2. Make sure you're in "Live" mode (toggle in top left)
3. Go to Developers → API keys
4. Copy your live keys

## Step 4: Create Live Price IDs

1. Go to Stripe Dashboard → Products
2. Create or find your Monthly and Lifetime products
3. Make sure you're in **Live mode**
4. Copy the price IDs (they start with `price_`)

## Step 5: Set Up Live Webhook

1. Go to Stripe Dashboard → Developers → Webhooks
2. Make sure you're in **Live mode**
3. Click "Add endpoint"
4. URL: `https://www.wordwave.studio/api/stripe/webhook`
5. Select these events:
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
6. Copy the webhook secret (starts with `whsec_`)

## Step 6: Debug Steps

1. **Check the health endpoint**: Visit `/api/stripe/health`
2. **Check Vercel Function Logs**:
   - Go to Vercel Dashboard → Your Project → Functions
   - Look for `/api/stripe/create-checkout` logs
   - Check for specific error messages
3. **Verify environment variables are set correctly**
4. **Redeploy after setting variables**

## Common Issues and Solutions

### Issue: "Missing environment variables"
- **Solution**: Set all required variables in Vercel dashboard

### Issue: "Invalid API key"
- **Solution**: Make sure you're using live keys (not test keys) for production

### Issue: "Unknown price ID"
- **Solution**: Create price IDs in live mode and update environment variables

### Issue: Still getting 500 errors
- **Solution**: Check the enhanced logs in create-checkout endpoint

## Step 7: Test the Configuration

After setting all variables and redeploying:

1. Visit: `https://www.wordwave.studio/api/stripe/health`
2. Should return: `{"status": "healthy", "environment": "live", ...}`
3. Try the checkout flow on your pricing page
4. Monitor Vercel function logs for detailed error information

## Emergency Checklist

- [ ] Environment variables set in Vercel (Production environment)
- [ ] Using live Stripe keys (not test keys)
- [ ] Live price IDs created and set
- [ ] Live webhook created and secret set
- [ ] Application redeployed after environment variable changes
- [ ] Health endpoint returns "healthy" status
