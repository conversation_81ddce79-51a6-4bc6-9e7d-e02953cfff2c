import React from 'react';
import './ProjectStatusIndicator.css';

interface ProjectContext {
  mode: 'new' | 'loaded';
  projectId: string | null;
  projectName: string;
  isAutoSaveEnabled: boolean;
  lastSaved?: string;
}

interface ProjectStatusIndicatorProps {
  projectContext: ProjectContext;
  isAutoSaving?: boolean;
  className?: string;
  hasUnsavedContent?: boolean;
}

const ProjectStatusIndicator: React.FC<ProjectStatusIndicatorProps> = ({
  projectContext,
  isAutoSaving = false,
  className = '',
  hasUnsavedContent = false
}) => {
  const formatLastSaved = (timestamp?: string) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getStatusIcon = () => {
    if (isAutoSaving) return '💾';
    if (projectContext.mode === 'loaded') return '📂';
    return '🆕';
  };

  const getStatusText = () => {
    if (isAutoSaving) return 'Auto-saving...';
    if (projectContext.mode === 'loaded') {
      return `Project: ${projectContext.projectName}`;
    }
    return 'New Project';
  };

  const getAutoSaveStatus = () => {
    if (isAutoSaving) return 'Saving...';
    if (projectContext.isAutoSaveEnabled) {
      const lastSaved = formatLastSaved(projectContext.lastSaved);
      return lastSaved ? `Auto-save enabled • Last saved ${lastSaved}` : 'Auto-save enabled';
    }
    return 'Manual save required';
  };

  return (
    <div className={`project-status-indicator ${projectContext.mode} ${className}`}>
      <div className="status-main">
        <span className="status-icon">{getStatusIcon()}</span>
        <div className="status-content">
          <div className="status-title">{getStatusText()}</div>
          <div className="status-subtitle">{getAutoSaveStatus()}</div>
        </div>
      </div>
      
      {projectContext.mode === 'loaded' && (
        <div className="project-id">
          <span className="project-id-label">ID:</span>
          <span className="project-id-value">{projectContext.projectId?.slice(-8)}</span>
        </div>
      )}
    </div>
  );
};

export default ProjectStatusIndicator;
