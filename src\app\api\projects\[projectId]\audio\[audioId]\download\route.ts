import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
        audioId: string;
    }>
}

// GET /api/projects/[projectId]/audio/[audioId]/download - Download audio file
export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId, audioId } = await context.params;

    console.log(`🎵 Downloading audio ${audioId} for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // First, get the audio metadata to find the actual filename
    const audioPath = `users/${userId}/projects/${projectId}/audio/${audioId}`;
    const audioMetadataPath = `${audioPath}/audio.json`;

    try {
      // Download and parse the audio metadata
      console.log(`📋 Attempting to download metadata from: ${audioMetadataPath}`);
      const metadataBuffer = await (cloudService as any).downloadFile(audioMetadataPath);
      const audioMetadata = JSON.parse(metadataBuffer.toString('utf-8'));

      console.log(`📋 Audio metadata loaded:`, {
        id: audioMetadata.id,
        name: audioMetadata.name,
        fileCount: audioMetadata.files?.length || 0,
        files: audioMetadata.files?.map((f: any) => ({ name: f.name, size: f.size })) || []
      });

      // Get the first audio file from the metadata
      if (!audioMetadata.files || audioMetadata.files.length === 0) {
        console.log(`❌ No audio files found in metadata for ${audioId}`);
        return NextResponse.json(
          { success: false, error: { message: 'No audio files found in metadata', code: 'NOT_FOUND' } },
          { status: 404 }
        );
      }

      // Try to download the first available audio file
      let audioBuffer: Buffer | null = null;
      let downloadedFileName: string | null = null;

      for (const audioFile of audioMetadata.files) {
        const audioFilePath = `${audioPath}/${audioFile.name}`;

        try {
          console.log(`📥 Attempting to download audio file: ${audioFilePath}`);
          audioBuffer = await (cloudService as any).downloadFile(audioFilePath);
          downloadedFileName = audioFile.name;
          console.log(`✅ Successfully downloaded audio file: ${audioFilePath}`);
          break; // Successfully downloaded, exit loop
        } catch (fileError: any) {
          console.warn(`⚠️ Failed to download ${audioFilePath}, trying next file...`, fileError.message);
          continue; // Try next file
        }
      }

      if (!audioBuffer || !downloadedFileName) {
        console.log(`❌ No downloadable audio files found for ${audioId}`);
        return NextResponse.json(
          { success: false, error: { message: 'No downloadable audio files found', code: 'NOT_FOUND' } },
          { status: 404 }
        );
      }

      // Find the file metadata for the downloaded file
      const downloadedFileMetadata = audioMetadata.files.find((f: any) => f.name === downloadedFileName);

      // Return the audio file as a response
      return new NextResponse(audioBuffer, {
        status: 200,
        headers: {
          'Content-Type': downloadedFileMetadata?.processedMimeType || 'audio/wav',
          'Content-Disposition': `inline; filename="${downloadedFileName}"`, // Use inline for browser playback
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        },
      });

    } catch (error) {
      console.error(`❌ Failed to download audio:`, error);

      return NextResponse.json(
        { success: false, error: { message: 'Audio file not found', code: 'NOT_FOUND' } },
        { status: 404 }
      );
    }
  } catch (error: any) {
    console.error('❌ Failed to download audio:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          message: error.message || 'Failed to download audio',
          code: error.code || 'DOWNLOAD_ERROR',
          details: error.details,
        },
      },
      { status: error.statusCode || 500 }
    );
  }
}
