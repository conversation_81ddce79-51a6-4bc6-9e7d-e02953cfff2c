# Vercel Environment Variables Setup

## Required Environment Variables for Production

To fix the Stripe checkout session errors, ensure these environment variables are set in your Vercel dashboard:

### 1. Go to Vercel Dashboard
- Visit your project: https://vercel.com/dashboard
- Navigate to your WordWaveNextJs project
- Go to Settings → Environment Variables

### 2. Set Production Environment Variables

#### Clerk Configuration
```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_PRODUCTION_KEY
CLERK_SECRET_KEY=sk_live_YOUR_PRODUCTION_SECRET
```

#### Stripe Configuration (PRODUCTION KEYS - NOT TEST KEYS)
```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_PRODUCTION_PUBLISHABLE_KEY
STRIPE_SECRET_KEY=sk_live_YOUR_PRODUCTION_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_PRODUCTION_WEBHOOK_SECRET
```

#### Stripe Price IDs (Production)
```
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_YOUR_PRODUCTION_MONTHLY_PRICE
NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID=price_YOUR_PRODUCTION_LIFETIME_PRICE
```

#### Customer Portal URL (PRODUCTION)
```
NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/28E28q5652vEaoI5tUa7C00
```

#### Other Required Variables
```
B2_APPLICATION_KEY_ID=your_production_b2_key_id
B2_APPLICATION_KEY=your_production_b2_key
B2_BUCKET_NAME=your_production_bucket_name
ADMIN_EMAIL=your_admin_email
RESEND_API_KEY=your_production_resend_key
FROM_EMAIL=your_production_from_email
NEXT_PUBLIC_APP_URL=https://your-production-domain.vercel.app
```

### 3. Stripe Webhook Setup for Production

1. **Create Production Webhook in Stripe Dashboard:**
   - Go to Stripe Dashboard → Developers → Webhooks
   - Click "Add endpoint"
   - URL: `https://your-production-domain.vercel.app/api/stripe/webhook`
   - Events to select:
     - `checkout.session.completed`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`

2. **Get the webhook secret:**
   - After creating the webhook, click on it
   - In the "Signing secret" section, reveal and copy the secret
   - Add it to Vercel as `STRIPE_WEBHOOK_SECRET`

### 4. Key Changes Made to Fix Issues

#### Fixed Stripe API Version Issue
- Removed the invalid `'2025-07-30.basil'` API version
- Now using default/latest Stripe API version
- Updated in all files:
  - `/api/stripe/create-checkout/route.ts`
  - `/api/stripe/webhook/route.ts`
  - `/api/stripe/sync-status/route.ts`
  - `/api/stripe/customer-portal/route.ts`
  - `lib/GDPRDataExportService.ts`
  - `lib/GDPRAccountDeletionService.ts`

#### Environment Variable Documentation
- Added comments to distinguish between test and production URLs
- Updated customer portal URL structure

### 5. Testing the Fix

After updating the environment variables in Vercel:

1. **Redeploy your application** (or trigger a new deployment)
2. **Test the checkout flow** on your production URL
3. **Check Vercel Function Logs** if issues persist:
   - Go to Vercel Dashboard → Your Project → Functions tab
   - Look for `/api/stripe/create-checkout` function logs
   - Check for any specific error messages

### 6. Common Issues and Solutions

#### Issue: "Failed to create checkout session"
- **Cause:** Missing or incorrect Stripe environment variables
- **Solution:** Verify all Stripe keys are production keys and properly set

#### Issue: Webhook events not being processed
- **Cause:** Incorrect webhook secret or URL
- **Solution:** Ensure webhook URL matches your production domain and secret is correct

#### Issue: Customer portal redirect fails
- **Cause:** Wrong customer portal URL
- **Solution:** Verify the portal URL is for your production Stripe account

### 7. Verification Checklist

- [ ] All environment variables set in Vercel
- [ ] Using production Stripe keys (not test keys)
- [ ] Webhook URL points to production domain
- [ ] Customer portal URL is correct for production
- [ ] Application redeployed after environment variable changes

## Next Steps

1. Set the environment variables in Vercel
2. Redeploy the application
3. Test the checkout flow
4. Monitor the function logs for any remaining issues

The main issue causing the 500 errors was the invalid Stripe API version. This has been fixed, but you also need to ensure your production environment variables are properly configured in Vercel.
