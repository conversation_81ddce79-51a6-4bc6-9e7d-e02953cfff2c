Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2F5D0000 ntdll.dll
7FFF2D850000 KERNEL32.DLL
7FFF2CB20000 KERNELBASE.dll
7FFF2E0F0000 USER32.dll
7FFF2C8E0000 win32u.dll
7FFF2E0C0000 GDI32.dll
7FFF2CFD0000 gdi32full.dll
7FFF2D0F0000 msvcp_win.dll
7FFF2C910000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF2D9F0000 advapi32.dll
7FFF2D770000 msvcrt.dll
7FFF2DB30000 sechost.dll
7FFF2CAF0000 bcrypt.dll
7FFF2E2C0000 RPCRT4.dll
7FFF2C170000 CRYPTBASE.DLL
7FFF2CF50000 bcryptPrimitives.dll
7FFF2DEF0000 IMM32.DLL
