// Server-side types for WordWave Studio Next.js API

export interface AudioFile {
  id: string;
  name: string;
  url?: string;
  originalMimeType?: string;
  processedMimeType: string;
  speaker?: string;
  cloudPath?: string;
  cloudFileId?: string;
  size?: number;
  uploadedAt?: string;
  duration?: number;
  createdAt?: string;
  voiceConfig?: {
    voice1?: string;
    voice2?: string;
  };
  // Enhanced metadata for better file management
  scriptContent?: string;
  scriptTopic?: string;
  generationId?: string; // Unique identifier for this generation
  ttsModel?: string;
  synthesisMode?: 'monologue' | 'podcast';
  // Base64 data for server-side upload (alternative to blob URLs)
  base64Data?: string;
}

export interface ProjectConfiguration {
  projectName: string;
  topic: string;
  scriptLinks: string;
  synthesisMode: 'monologue' | 'podcast';
  selectedScriptModel: string;
  selectedTtsModel: string;
  // Voice assignments (support both naming conventions for compatibility)
  voice1: string;
  voice2: string;
  defaultVoice1: string;
  defaultVoice2: string;
  // Speaker/persona names
  speaker1Name: string;
  speaker2Name: string;
  // Project style and settings
  projectStyle: string;
  wordCount: number;
  createdAt: string;
  updatedAt: string;
}

// Legacy ProjectScript interface (for backward compatibility)
export interface ProjectScript {
  content: string;
  generatedAt: string;
  model: string;
  tokenCount?: {
    prompt?: number;
    candidates?: number;
    total?: number;
  };
}

export interface ProjectMetadata {
  version: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  totalSize: number;
  fileCount: number;
  tags?: string[];
  description?: string;
}

// New separated data structures
export interface ScriptMetadata {
  version: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  size: number;
  tags?: string[];
  description?: string;
  wordCount?: number;
}

export interface ScriptData {
  id: string;
  projectId: string;
  name: string;
  content: string;
  generatedAt: string;
  model: string;
  tokenCount?: {
    prompt?: number;
    candidates?: number;
    total?: number;
  };
  metadata: ScriptMetadata;
}

export interface AudioMetadata {
  version: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  totalSize: number;
  fileCount: number;
  tags?: string[];
  description?: string;
  duration?: number;
}

export interface AudioData {
  id: string;
  projectId: string;
  scriptId?: string; // Optional link to script
  name: string;
  files: AudioFile[];
  generatedAt: string;
  ttsModel: string;
  voiceConfig: {
    voice1: string;
    voice2: string;
    synthesisMode: 'monologue' | 'podcast';
  };
  // Include script content and topic for display in gallery
  scriptContent?: string;
  scriptTopic?: string;
  // Generation ID for deduplication
  generationId?: string;
  metadata: AudioMetadata;
}

// Updated ProjectData (configuration only)
export interface ProjectData {
  id: string;
  configuration: ProjectConfiguration;
  metadata: ProjectMetadata;
}

// Legacy ProjectData interface (for backward compatibility)
export interface LegacyProjectData {
  id: string;
  configuration: ProjectConfiguration;
  script?: ProjectScript;
  audioFiles: AudioFile[];
  metadata: ProjectMetadata;
}

export interface CloudStorageFile {
  fileName: string;
  filePath: string;
  fileId?: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
  sha1Hash?: string;
}

export interface CloudProject {
  projectId: string;
  projectName: string;
  userId: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  files: CloudStorageFile[];
  metadata: ProjectMetadata;
  cloudPath: string;
}

export interface ProjectSummary {
  id: string; // Add id property for compatibility
  projectId: string;
  name: string; // Add name property for compatibility
  projectName: string;
  updatedAt: string;
  fileCount: number;
  totalSize: number;
  tags?: string[];
  description?: string;
}

export interface UploadProgress {
  fileName: string;
  bytesUploaded: number;
  totalBytes: number;
  percentage: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
  };
}

export interface SaveProjectRequest {
  projectData: ProjectData;
  options?: {
    compress?: boolean;
    includeAudio?: boolean;
    tags?: string[];
    description?: string;
  };
}

export interface LoadProjectRequest {
  projectId: string;
  includeAudio?: boolean;
}

export interface ListProjectsRequest {
  limit?: number;
  offset?: number;
  tags?: string[];
  search?: string;
}

export interface DeleteProjectRequest {
  projectId: string;
  confirmDelete?: boolean;
}

// Error classes for Next.js API routes
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_ERROR',
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class CloudStorageError extends ApiError {
  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'CLOUD_STORAGE_ERROR',
    details?: any
  ) {
    super(message, statusCode, code, details);
    this.name = 'CloudStorageError';
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 404, 'NOT_FOUND', details);
    this.name = 'NotFoundError';
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 401, 'AUTHENTICATION_ERROR', details);
    this.name = 'AuthenticationError';
  }
}

// Client-side types
export interface Utterance {
  speakerIndex: number;
  text: string;
}

export interface ProcessedAudioChunk {
  speakerIndex: number;
  audioBuffer: AudioBuffer;
  duration: number;
  originalMimeType: string;
}

export type SynthesisMode = 'podcast' | 'monologue';

// Voice and Model configurations
export interface VoiceOption {
  displayName: string;
  apiName: string;
}

export interface ModelOption {
  name: string;
  apiName: string;
}

// Audio processing types
export interface WavConversionOptions {
  numChannels: number;
  sampleRate: number;
  bitsPerSample: number;
}

// Subscription and usage tracking types
export interface UsageStats {
  scripts: number;
  audioGeneration: number;
  tokens: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  limits: {
    scripts: number;
    audioGeneration: number;
    tokens: number;
  };
}
