"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface BannerConfig {
  enabled: boolean;
  type: 'info' | 'warning' | 'success' | 'error' | 'promo' | 'maintenance';
  title?: string;
  message: string;
  ctaText?: string;
  ctaLink?: string;
  dismissible: boolean;
  showUntil?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

const Banner: React.FC = () => {
  const [bannerConfig, setBannerConfig] = useState<BannerConfig | null>(null);
  const [isDismissed, setIsDismissed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();
  // Offset the banner when a fixed navbar is present so it's fully visible and centered
  const [topOffset, setTopOffset] = useState(0);

  useEffect(() => {
    const fetchBannerConfig = async () => {
      try {
        const response = await fetch('/banner-config.json');
        
        if (response.ok) {
          const config: BannerConfig = await response.json();
          
          // Check if banner should be shown based on date
          if (config.showUntil) {
            const showUntilDate = new Date(config.showUntil);
            const now = new Date();
            if (now > showUntilDate) {
              setIsLoading(false);
              return;
            }
          }
          
          // Check if user has dismissed this banner
          if (config.dismissible) {
            const dismissedBanners = JSON.parse(
              localStorage.getItem('dismissedBanners') || '[]'
            );
            const bannerKey = `${config.type}-${config.message.substring(0, 50)}`;
            if (dismissedBanners.includes(bannerKey)) {
              setIsLoading(false);
              return;
            }
          }
          
          setBannerConfig(config);
        }
      } catch (error) {
        console.error('Failed to fetch banner config:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBannerConfig();

    // Detect a fixed navbar (used on the landing page) and offset the banner below it
    try {
      const nav = document.querySelector('.navbar') as HTMLElement | null;
      if (nav) {
        const computed = getComputedStyle(nav);
        if (computed.position === 'fixed') {
          setTopOffset(nav.offsetHeight); // typically 70px from LandingPage.css
        }
      }
    } catch (_) {
      // no-op: safe to ignore in non-browser contexts
    }
  }, []);

  const handleDismiss = () => {
    if (bannerConfig?.dismissible) {
      const bannerKey = `${bannerConfig.type}-${bannerConfig.message.substring(0, 50)}`;
      const dismissedBanners = JSON.parse(
        localStorage.getItem('dismissedBanners') || '[]'
      );
      dismissedBanners.push(bannerKey);
      localStorage.setItem('dismissedBanners', JSON.stringify(dismissedBanners));
      setIsDismissed(true);
    }
  };

  if (isLoading || !bannerConfig || !bannerConfig.enabled || isDismissed) {
    return null;
  }

  const getBannerStyles = () => {
    // Single consistent style that matches the app's aesthetic
    const baseStyles = "relative z-40 w-full text-sm font-medium transition-all duration-300 ease-in-out rounded-xl backdrop-blur-sm border";

    switch (bannerConfig.type) {
      case 'info':
        return `${baseStyles} bg-blue-500/10 border-blue-500/20 text-blue-200`;
      case 'success':
        return `${baseStyles} bg-green-500/10 border-green-500/20 text-green-200`;
      case 'warning':
        return `${baseStyles} bg-yellow-500/10 border-yellow-500/20 text-yellow-200`;
      case 'error':
        return `${baseStyles} bg-red-500/10 border-red-500/20 text-red-200`;
      case 'promo':
        return `${baseStyles} bg-purple-500/10 border-purple-500/20 text-purple-200`;
      case 'maintenance':
        return `${baseStyles} bg-orange-500/10 border-orange-500/20 text-orange-200`;
      default:
        return `${baseStyles} bg-slate-500/10 border-slate-500/20 text-slate-200`;
    }
  };

  const getPriorityAnimation = () => {
    switch (bannerConfig.priority) {
      case 'urgent':
        return 'animate-pulse';
      case 'high':
        return 'animate-bounce';
      default:
        return '';
    }
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-50 w-full flex justify-center px-4 py-2" style={topOffset ? { top: topOffset + 'px' } : undefined}>
      {/* Banner container matching app's card style */}
      <div className={`${getBannerStyles()} ${getPriorityAnimation()} relative px-6 py-3 max-w-4xl w-full`}>
        
        {/* Mobile Layout */}
        <div className="block sm:hidden">
          <div className="flex flex-col items-center justify-center py-2 text-center space-y-2">
            {bannerConfig.title && (
              <h3 className="font-semibold text-base">{bannerConfig.title}</h3>
            )}
            <p className="text-sm leading-relaxed">{bannerConfig.message}</p>
            {bannerConfig.ctaText && bannerConfig.ctaLink && pathname !== '/contact' && (
              <Link
                href={bannerConfig.ctaLink}
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200"
              >
                {bannerConfig.ctaText}
              </Link>
            )}
            {bannerConfig.dismissible && (
              <button
                onClick={handleDismiss}
                className="absolute top-2 right-2 text-white hover:text-gray-200 transition-colors"
                aria-label="Dismiss banner"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        {/* Desktop Layout - Hidden on mobile */}
        <div className="hidden sm:block">
          <div className="flex flex-col items-center justify-center py-3 text-center space-y-3">
            {bannerConfig.title && (
              <h3 className="font-semibold text-base">{bannerConfig.title}</h3>
            )}
            <p className="text-sm leading-relaxed max-w-3xl">{bannerConfig.message}</p>
            {bannerConfig.ctaText && bannerConfig.ctaLink && pathname !== '/contact' && (
              <Link
                href={bannerConfig.ctaLink}
                className="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200"
              >
                {bannerConfig.ctaText}
              </Link>
            )}
            {bannerConfig.dismissible && (
              <button
                onClick={handleDismiss}
                className="absolute top-3 right-4 text-white hover:text-gray-200 transition-colors text-xl"
                aria-label="Dismiss banner"
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
