/* Cloud Storage Components Styles */

.cloud-storage-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.cloud-storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cloud-storage-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
}

.refresh-button {
  background: rgba(59, 130, 246, 0.8);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.refresh-button:hover:not(:disabled) {
  background: rgba(59, 130, 246, 1);
  transform: translateY(-1px);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  color: #fca5a5;
}

.loading-message {
  text-align: center;
  padding: 20px;
  color: #d1d5db;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
  font-style: italic;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.project-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

.project-info {
  flex: 1;
}

.project-info h4 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

.project-meta {
  margin: 4px 0;
  color: #9ca3af;
  font-size: 0.9rem;
}

.project-stats {
  margin: 4px 0;
  color: #6b7280;
  font-size: 0.85rem;
}

.project-actions {
  display: flex;
  gap: 8px;
}

.load-button, .delete-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.load-button {
  background: rgba(34, 197, 94, 0.8);
  color: white;
}

.load-button:hover:not(:disabled) {
  background: rgba(34, 197, 94, 1);
  transform: translateY(-1px);
}

.delete-button {
  background: rgba(239, 68, 68, 0.8);
  color: white;
}

.delete-button:hover:not(:disabled) {
  background: rgba(239, 68, 68, 1);
  transform: translateY(-1px);
}

.load-button:disabled, .delete-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Save Project Button Styles */
.save-project-container {
  margin: 16px 0;
}

.save-project-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.save-project-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.save-project-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cloud-storage-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .project-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .project-actions {
    justify-content: center;
  }
}
