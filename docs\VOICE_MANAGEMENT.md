# Voice Management System

This document describes the new API-based voice management system for WordWave Studio.

## Overview

The voice management system has been enhanced from a hardcoded list to a dynamic, configurable system that:

- ✅ Supports API-based voice discovery (when available)
- ✅ Includes enhanced voice metadata (category, gender, age range, descriptions)
- ✅ Provides filtering and search capabilities
- ✅ Follows the same architecture pattern as the existing model management
- ✅ Maintains backward compatibility

## Architecture

### 1. Configuration-Based System (`models.json`)

The `src/config/models.json` file now includes a `voices` array with enhanced metadata:

```json
{
  "voices": [
    {
      "name": "Zephyr",
      "apiName": "zephyr",
      "description": "A light, breezy voice perfect for casual conversations",
      "category": "premium",
      "gender": "neutral",
      "accent": "american",
      "ageRange": "adult"
    }
  ]
}
```

### 2. Service Layer (`ModelService.ts`)

Extended the existing `ModelService` with voice-specific functions:

- `getVoices()` - Get all available voices
- `getVoicesByCategory()` - Filter voices by category (standard/premium)
- `getVoicesFiltered()` - Advanced filtering by multiple criteria
- `fetchVoicesFromAPI()` - Future-ready for API voice discovery

### 3. React Hooks (`useVoices.ts`)

Provides React hooks for voice management:

- `useVoices()` - Main hook for accessing voices
- `useVoicesByCategory()` - Get voices by category
- `usePremiumVoices()` - Get premium voices only
- `useStandardVoices()` - Get standard voices only

### 4. UI Components

- `VoiceSelector.tsx` - Enhanced voice selection with search and filtering
- `VoiceSelectionExample.tsx` - Example implementation

## Usage Examples

### Basic Voice Selection

```tsx
import { useVoices } from '../hooks/useVoices';

function MyComponent() {
  const { voices, isLoading, error } = useVoices();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <select>
      {voices.map(voice => (
        <option key={voice.apiName} value={voice.apiName}>
          {voice.name} - {voice.description}
        </option>
      ))}
    </select>
  );
}
```

### Enhanced Voice Selector

```tsx
import VoiceSelector from '../components/VoiceSelector';

function ProjectSetup() {
  const [selectedVoice, setSelectedVoice] = useState('');

  return (
    <VoiceSelector
      value={selectedVoice}
      onChange={setSelectedVoice}
      showFilters={true}
      placeholder="Choose a voice for Speaker 1..."
    />
  );
}
```

### Filtered Voice Access

```tsx
import { useVoices } from '../hooks/useVoices';

function PremiumVoiceSelector() {
  const { voices } = useVoices({
    filters: { 
      category: 'premium',
      ageRange: 'adult'
    }
  });

  return (
    <div>
      <h3>Premium Adult Voices</h3>
      {voices.map(voice => (
        <div key={voice.apiName}>
          <strong>{voice.name}</strong>
          <p>{voice.description}</p>
        </div>
      ))}
    </div>
  );
}
```

## Voice Metadata

Each voice includes the following metadata:

| Field | Type | Description | Values |
|-------|------|-------------|---------|
| `name` | string | Display name | "Zephyr", "Puck", etc. |
| `apiName` | string | API identifier | "zephyr", "puck", etc. |
| `description` | string | Voice description | Detailed description of voice characteristics |
| `category` | string | Voice tier | "standard", "premium" |
| `gender` | string | Voice gender | "neutral", "male", "female" |
| `accent` | string | Voice accent | "american", "british", etc. |
| `ageRange` | string | Age range | "young", "adult", "mature" |

## Configuration Management

### Updating Voices

Run the update script to refresh the configuration:

```bash
node scripts/update-models.js [API_KEY]
```

This script now:
- ✅ Fetches latest models from Google's API
- ✅ Updates voice configurations with metadata
- ✅ Maintains voice descriptions and categories
- ✅ Preserves manual customizations

### Manual Voice Configuration

You can manually edit `src/config/models.json` to:
- Add custom voice descriptions
- Update voice categories
- Modify voice metadata
- Add new voices (when available)

## Migration from Legacy System

The system maintains backward compatibility:

1. **Existing Code**: The `AVAILABLE_VOICES` constant still works
2. **Gradual Migration**: You can migrate components one by one
3. **Fallback System**: Falls back to hardcoded values if configuration is missing

### Migration Steps

1. **Replace hardcoded dropdowns** with `VoiceSelector` component
2. **Use hooks** instead of importing constants directly
3. **Add filtering** where appropriate
4. **Update API calls** to use enhanced voice metadata

## Future Enhancements

### API-Based Voice Discovery

When Google provides a voices API endpoint:

```typescript
// Update fetchVoicesFromAPI in ModelService.ts
export async function fetchVoicesFromAPI(apiKey: string): Promise<VoiceInfo[]> {
  const response = await fetch('https://api.google.com/v1/voices', {
    headers: { 'x-goog-api-key': apiKey }
  });
  
  const data = await response.json();
  return data.voices.map(voice => ({
    name: voice.displayName,
    apiName: voice.name,
    description: voice.description,
    category: voice.tier,
    gender: voice.gender,
    accent: voice.locale,
    ageRange: voice.ageRange
  }));
}
```

### Additional Features

- 🔄 Real-time voice updates
- 🎵 Voice preview samples
- 🌍 Multi-language support
- 📊 Usage analytics
- ⭐ Voice rating system
- 🔍 Advanced search with tags
- 💾 User voice preferences

## Performance Considerations

- **Caching**: Voices are cached for 24 hours by default
- **Lazy Loading**: Only fetch voices when needed
- **Filtering**: Client-side filtering for responsiveness
- **Fallbacks**: Graceful degradation to hardcoded values

## Testing

Test the voice system:

```bash
# Test voice loading
npm run dev

# Check console for voice loading logs
# Verify dropdown contains all voices with metadata
# Test filtering functionality
```

## Contributing

When adding new voices:

1. Add to `src/config/models.json`
2. Include all metadata fields
3. Update tests if needed
4. Run update script to validate
5. Test in UI components

---

**Note**: This system is designed to be future-ready for when Google provides a voices API endpoint, while maintaining full functionality with the current configuration-based approach.
