"use client";

import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: 'info' | 'error' | 'warning' | 'success';
  confirmText?: string;
  onConfirm?: () => void | boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'info',
  confirmText = 'OK',
  onConfirm
}) => {
  if (!isOpen) return null;

  const handleConfirm = () => {
    if (onConfirm) {
      const result = onConfirm();
      // Only close if onConfirm doesn't return false
      if (result !== false) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const getIconAndColors = () => {
    switch (type) {
      case 'error':
        return {
          icon: '❌',
          borderColor: 'border-red-500/50',
          bgColor: 'bg-red-500/10',
          textColor: 'text-red-400',
          buttonColor: 'bg-red-600 hover:bg-red-700'
        };
      case 'warning':
        return {
          icon: '⚠️',
          borderColor: 'border-yellow-500/50',
          bgColor: 'bg-yellow-500/10',
          textColor: 'text-yellow-400',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700'
        };
      case 'success':
        return {
          icon: '✅',
          borderColor: 'border-green-500/50',
          bgColor: 'bg-green-500/10',
          textColor: 'text-green-400',
          buttonColor: 'bg-green-600 hover:bg-green-700'
        };
      default:
        return {
          icon: 'ℹ️',
          borderColor: 'border-blue-500/50',
          bgColor: 'bg-blue-500/10',
          textColor: 'text-blue-400',
          buttonColor: 'bg-blue-600 hover:bg-blue-700'
        };
    }
  };

  const { icon, borderColor, bgColor, textColor, buttonColor } = getIconAndColors();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className={`relative bg-slate-800/90 backdrop-blur-sm rounded-xl border ${borderColor} p-6 max-w-md w-full mx-4 shadow-2xl`}>
        <div className="flex items-start gap-4">
          <div className="text-2xl">{icon}</div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2 font-fredoka">
              {title}
            </h3>
            <div className={`${bgColor} ${borderColor} border rounded-lg p-3 mb-4`}>
              <p className={`text-sm ${textColor}`}>
                {message}
              </p>
            </div>
            <div className="flex justify-end gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className={`px-4 py-2 ${buttonColor} text-white rounded-lg transition-colors font-medium`}
              >
                {confirmText}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
