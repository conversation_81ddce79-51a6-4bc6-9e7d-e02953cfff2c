# WordWave Studio - Boilerplate Guide

Welcome to the WordWave Studio boilerplate guide! This document provides a comprehensive overview of the codebase to help you get started with building your own applications on top of this foundation.

## Table of Contents

1.  [Overall Architecture](#overall-architecture)
2.  [Stripe Integration](#stripe-integration)
3.  [Backblaze B2 Integration](#backblaze-b2-integration)
4.  [Clerk Integration](#clerk-integration)
5.  [Google Gemini Integration](#google-gemini-integration)
6.  [Email Services Integration](#email-services-integration)
7.  [UI Components](#ui-components)
8.  [Deployment](#deployment)
9.  [Getting Started](#getting-started)

---

## Stripe Integration

The project includes a complete integration with Stripe for handling payments and subscriptions.

### Key Features

*   **Subscription Management**: Handles both recurring monthly subscriptions and one-time lifetime payments.
*   **Stripe Checkout**: Uses Stripe Checkout for a secure and seamless payment experience.
*   **Customer Portal**: Allows users to manage their subscriptions, update payment methods, and view invoices.
*   **Webhooks**: A dedicated webhook endpoint (`/api/stripe/webhook`) processes events from Strip<PERSON> to keep subscription statuses in sync.
*   **Free Trial**: The monthly subscription includes a 7-day free trial.

For a detailed guide on the Stripe integration, including setup instructions, API endpoint documentation, and details on the free trial implementation, please see [STRIPE_INTEGRATION.md](./STRIPE_INTEGRATION.md).

---

## Backblaze B2 Integration

The application uses Backblaze B2 for secure and scalable cloud storage of user-generated audio files.

### Key Features

*   **File Uploads**: A dedicated API endpoint (`/api/cloud-storage/upload`) handles file uploads.
*   **Secure Downloads**: Files can be securely downloaded via a dedicated endpoint.
*   **File Management**: The integration includes logic for listing and deleting files.
*   **`CloudStorageService`**: A dedicated service class (`src/lib/CloudStorageService.ts`) abstracts all interactions with the Backblaze B2 API.

For a comprehensive guide on the Backblaze B2 integration, including account setup, API documentation, security best practices, and implementation details, please see [BACKBLAZE_B2_INTEGRATION.md](./BACKBLAZE_B2_INTEGRATION.md).

---

## Clerk Integration

The application uses [Clerk](https://clerk.com/) for user authentication and management. For more details, see [CLERK_INTEGRATION.md](./CLERK_INTEGRATION.md).

---

## Google Gemini Integration

The application uses the [Google Gemini API](https://ai.google.dev/) for AI-powered script generation. For more details, see [GOOGLE_GEMINI_INTEGRATION.md](./GOOGLE_GEMINI_INTEGRATION.md).

---

## Email Services Integration

The application is configured to use Sender, Resend, and SendGrid for sending transactional emails. For more details, see [EMAIL_INTEGRATION.md](./EMAIL_INTEGRATION.md).

---

## Overall Architecture

This project is a full-stack application built with Next.js, leveraging the App Router for a modern, server-centric architecture. Here's a breakdown of the key components:

### Tech Stack

*   **Framework**: [Next.js](https://nextjs.org/) (with App Router)
*   **Language**: [TypeScript](https://www.typescriptlang.org/)
*   **Styling**: [Tailwind CSS](https://tailwindcss.com/)
*   **Authentication**: [Clerk](https://clerk.com/)
*   **Payments**: [Stripe](https://stripe.com/)
*   **Cloud Storage**: [Backblaze B2](https://www.backblaze.com/b2/cloud-storage.html)
*   **AI Services**: [Google Gemini](https://ai.google.dev/)

### Project Structure

The codebase is organized into the following key directories:

*   `src/app`: Contains the pages and API routes of the application.
    *   `src/app/api`: All backend API endpoints are defined here as Next.js API Routes. These are serverless functions that handle the business logic.
    *   `src/app/(pages)`: The main pages of the application are located in directories like `about`, `contact`, `pricing`, etc.
*   `src/components`: Reusable React components are stored here.
*   `src/lib`: Core services and libraries, such as `CloudStorageService.ts` and `EmailService.ts`.
*   `src/hooks`: Custom React hooks for managing state and side effects.
*   `src/services`: Client-side services for interacting with the backend APIs.
*   `docs`: Contains all project documentation, including this guide.

### Architecture Diagram

This diagram illustrates the flow of data and interactions between the different parts of the system:

```
+-----------------+      +----------------------+      +--------------------+
|   User Browser  |----->|   Next.js Frontend   |----->|   Next.js Backend  |
+-----------------+      | (React Components)   |      |    (API Routes)    |
                         +----------------------+      +--------------------+
                                |         ^                   |         ^
                                |         |                   |         |
      +-------------------------+         +-------------------+         +-------------------+
      |                                                                                     |
      v                                                                                     v
+-----------------+      +----------------------+      +--------------------+      +--------------------+
|      Clerk      |<-----|                      |<-----|       Stripe       |<-----|   Backblaze B2     |
| (Authentication)|      |                      |      |     (Payments)     |      |   (File Storage)   |
+-----------------+      +----------------------+      +--------------------+      +--------------------+
```

---

## Stripe Integration

The project includes a complete integration with Stripe for handling payments and subscriptions.

### Key Features

*   **Subscription Management**: Handles both recurring monthly subscriptions and one-time lifetime payments.
*   **Stripe Checkout**: Uses Stripe Checkout for a secure and seamless payment experience.
*   **Customer Portal**: Allows users to manage their subscriptions, update payment methods, and view invoices.
*   **Webhooks**: A dedicated webhook endpoint (`/api/stripe/webhook`) processes events from Stripe to keep subscription statuses in sync.
*   **Free Trial**: The monthly subscription includes a 7-day free trial.

For a detailed guide on the Stripe integration, including setup instructions, API endpoint documentation, and details on the free trial implementation, please see [STRIPE_INTEGRATION.md](./STRIPE_INTEGRATION.md).

---

## Backblaze B2 Integration

The application uses Backblaze B2 for secure and scalable cloud storage of user-generated audio files.

### Key Features

*   **File Uploads**: A dedicated API endpoint (`/api/cloud-storage/upload`) handles file uploads.
*   **Secure Downloads**: Files can be securely downloaded via a dedicated endpoint.
*   **File Management**: The integration includes logic for listing and deleting files.
*   **`CloudStorageService`**: A dedicated service class (`src/lib/CloudStorageService.ts`) abstracts all interactions with the Backblaze B2 API.

For a comprehensive guide on the Backblaze B2 integration, including account setup, API documentation, security best practices, and implementation details, please see [BACKBLAZE_B2_INTEGRATION.md](./BACKBLAZE_B2_INTEGRATION.md).

---

## UI Components

The user interface is built with a collection of reusable React components located in the `src/components` directory. These components are styled with Tailwind CSS. Here's an overview of some of the most important components:

*   **`AccountMenu.tsx`**: A dropdown menu that provides users with access to their account information, subscription status, and API key configuration. It also displays usage statistics.

*   **`AudioGallery.tsx`**: A component for displaying a gallery of generated audio files. It includes features for playing, downloading, renaming, and deleting audio files, as well as viewing their metadata.

*   **`LandingPage.tsx`**: The main landing page for the application. It's a comprehensive component with multiple sections that can be easily customized.

*   **`PageHeader.tsx`**: The header for the main application pages. It includes the application logo, navigation links, and a call-to-action button.

*   **`VoiceSelector.tsx`**: A dropdown component that allows users to select from a list of available voices for text-to-speech generation. It includes filtering and search functionality.

*   **`Modal.tsx`**: A general-purpose modal component that can be used to display dialogs and other pop-up content.

These components can be customized by modifying their source code in the `src/components` directory. You can also create your own components and use them throughout the application.

---

## Deployment

This project is configured for easy deployment to [Vercel](https://vercel.com/). For a detailed guide on deploying the application, see [DEPLOYMENT.md](./DEPLOYMENT.md).

---

## Getting Started

This guide will help you get the project up and running on your local machine for development and testing purposes.

### Prerequisites

*   Node.js 18+
*   npm or yarn
*   A [Clerk](https://clerk.com/) account for authentication.
*   A [Stripe](https://stripe.com/) account for payment processing.
*   A [Backblaze B2](https://www.backblaze.com/b2/cloud-storage.html) account for cloud storage.
*   A [Google Gemini API key](https://ai.google.dev/) for AI services.

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/pjecuacion/WordWaveNextJs.git
    cd WordWaveNextJs
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Set up environment variables:**

    Copy the `.env.local.example` file to a new file named `.env.local`:

    ```bash
    cp .env.local.example .env.local
    ```

    Now, open `.env.local` and fill in your API keys and other configuration values. You will need to get these from the respective services (Clerk, Stripe, Backblaze B2, Google Gemini).

4.  **Run the development server:**

    ```bash
    npm run dev
    ```

    The application should now be running at [http://localhost:3000](http://localhost:3000).

### Customization

Once you have the project running, here are a few places you might want to start customizing:

*   **Branding**: Update the application name, logo, and color scheme. The main logo can be found in `src/components/PageHeader.tsx`. Colors and fonts can be configured in `tailwind.config.ts` and `src/app/globals.css`.
*   **UI Components**: Modify the existing components in `src/components` or create your own to change the look and feel of the application.
*   **Content**: Change the text and images on the landing page (`src/components/LandingPage.tsx`) and other pages in the `src/app` directory.
*   **Features**: Add or remove features by modifying the API routes in `src/app/api` and the corresponding frontend components.
