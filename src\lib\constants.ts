// Constants for WordWave Studio
import { getModelsFromConfig } from '../services/ModelService';

// Load models and voices from configuration
const modelsConfig = getModelsFromConfig();

// Available voice names for TTS - now sourced from configuration
export const SUPPORTED_VOICE_NAMES = modelsConfig.voices?.map(voice => voice.apiName) || [
  "achernar", "achird", "algenib", "algieba", "alnilam", "aoede", "autonoe",
  "callirrhoe", "charon", "despina", "enceladus", "erinome", "fenrir", "gacrux",
  "iapetus", "kore", "laomedeia", "leda", "orus", "puck", "pulcherrima",
  "rasalgethi", "sadachbia", "sadaltager", "schedar", "sulafat", "umbriel",
  "vindemiatrix", "zephyr", "zubenelgenubi"
];

export const AVAILABLE_VOICES = modelsConfig.voices?.map(voice => ({
  displayName: voice.name,
  apiName: voice.apiName,
  description: voice.description,
  category: voice.category,
  gender: voice.gender,
  accent: voice.accent,
  ageRange: voice.ageRange,
})) || SUPPORTED_VOICE_NAMES.map(name => ({
  displayName: name.charAt(0).toUpperCase() + name.slice(1),
  apiName: name,
  description: undefined,
  category: 'standard',
  gender: 'neutral',
  accent: 'american',
  ageRange: 'adult',
}));

export const AVAILABLE_SCRIPT_MODELS = modelsConfig.scriptModels.map(model => ({
  name: model.apiName,
  apiName: model.apiName,
  description: model.description,
  category: model.category,
}));

export const AVAILABLE_TTS_MODELS = modelsConfig.ttsModels.map(model => ({
  name: model.apiName,
  apiName: model.apiName,
  description: model.description,
  category: model.category,
}));

export const MAX_SPEAKERS_PODCAST = 2;
export const preferredDefaultVoiceApiNames = ['zephyr', 'puck'];

// Random content for project setup
export const RANDOM_PODCAST_NAMES = [
  "Tech Unfiltered", "The Daily Byte", "Future Forward", "Code & Coffee", "AI Ascent",
  "Digital Dialogues", "The Startup Story", "Market Movers", "Creative Sparks", "Wellness Waves",
  "The Curiosity Cabinet", "Echoes of Innovation", "Pixel Pioneers", "Quantum Leaps", "The Story Forge",
  "Quantum Quirks & Quarks", "The Existential Enchilada", "AI After Dark", "Gossip & Gravitons",
  "Conspiracy Cafe", "Zero Gravity Zen", "The Blockchain Babble", "Myths, Memes & Mayhem",
  "Code, Coffee & Chaos", "The Philosopher's Folly", "Secrets of the Silicon Sages",
  "Midnight Musings on Mars", "The Glitch in the Algorithm", "Robots Read Romance Novels",
  "Surreal Stories & Sonic Screwdrivers", "The Galactic Grocer", "Whispers from the Void",
  "The Cyborg's Symphony", "Time Travel Tea Time", "Chronicles of the Cosmic Chicken"
];

export const RANDOM_PERSONA_NAMES = [
  "The Optimist", "The Skeptic", "The Innovator", "The Pragmatist", "The Storyteller",
  "The Analyst", "The Enthusiast", "The Critic", "The Visionary", "The Realist",
  "The Explorer", "The Mentor", "The Jester", "The Sage", "The Maverick",
  "Captain Cryptic", "Professor Paradox", "The AI Whisperer", "Countess of Code", "Baron Von Byte",
  "The Quantum Jester", "Sergeant Sarcasm", "The Galactic Gossip", "Oracle of Obscurity",
  "The Data Diva", "Dr. Doomscroll", "The Ethical Enigma", "Agent of Absurdity",
  "The Conspiracy Curator", "The Llama Lord (who speaks only in riddles)", "The Sentient Toaster",
  "Brenda from HR (secretly a time lord)", "A Slightly Disgruntled Ghost", "Chadley the Overenthusiastic Intern",
  "The Alien Next Door", "A Sentient Potted Plant"
];

export const RANDOM_PODCAST_STYLES = [
  "Casual conversation", "In-depth interview", "News roundup", "Educational monologue",
  "Storytelling narrative", "Panel discussion", "Investigative report", "Q&A with listeners",
  "Humorous take on current events", "Expert analysis", "Debate format", "Historical deep-dive",
  "Inspirational speech", "Product review", "Personal anecdote",
  "Heated debate between fictional characters", "Socratic dialogue with a rogue AI",
  "Improvised comedy sketch", "ASMR philosophical rambling", "Beat poetry slam about technology",
  "Dramatic re-enactment of historical tech blunders", "Mockumentary investigation",
  "Gonzo journalism dive into subcultures", "Late-night call-in show for interdimensional beings",
  "Fireside chat with a time traveler", "Satirical news broadcast from the future",
  "A series of increasingly bizarre hypotheticals", "An aggressively positive self-help seminar",
  "A cooking show where nothing goes right", "Found footage analysis (of a mundane event)",
  "Two squirrels arguing about nuts"
];

export const RANDOM_TONES_HUMOR = [
  "Witty and sarcastic", "Serious and informative", "Lighthearted and funny", "Curious and inquisitive",
  "Empathetic and supportive", "Provocative and challenging", "Neutral and objective",
  "Enthusiastic and upbeat", "Dry and deadpan", "Inspiring and motivational", "Whimsical and playful",
  "Passionate and persuasive", "Calm and reassuring",
  "Manically enthusiastic and slightly unhinged", "Darkly humorous with a hint of existential dread",
  "Absurdist and nonsensical", "Overly dramatic and theatrical",
  "Deadpan irony so thick you can cut it with a knife", "Joyfully cynical", "Pedantically playful",
  "Charmingly clueless", "Conspiratorially whispered", "Bombastic and self-important (but actually hilarious)",
  "Calmly chaotic", "Sweetly subversive", "Unintentionally hilarious due to extreme seriousness",
  "Aggressively cheerful", "Like a nature documentary narrator on too much coffee"
];

export const RANDOM_PODCAST_TOPICS = [
  "The Impact of AI on Creative Industries", "Sustainable Living in Urban Environments",
  "The Future of Remote Work", "Exploring the Metaverse: Hype vs. Reality",
  "Mental Wellness in the Digital Age", "The Science of Sleep and Productivity",
  "Debunking Common Tech Myths", "Innovations in Renewable Energy Storage",
  "The History of Video Games: From Arcades to VR", "The Gig Economy: Pros, Cons, and Future",
  "Understanding Blockchain Beyond Cryptocurrency", "The Rise of Plant-Based Diets and their Global Impact",
  "The Ethics of Gene Editing", "Space Exploration: The Next Frontier", "The Psychology of Social Media",
  "A Beginner's Guide to Investing", "The Art of Effective Communication", "Learning a New Language: Tips and Tricks",
  "If Animals Could Text: A Sociological Study", "The Existential Crisis of a Self-Aware Toaster",
  "Why Do Socks Disappear in the Laundry? An Investigation.", "The Secret Lives of Garden Gnomes",
  "Surviving a Zombie Apocalypse with Only a Spatula", "Could Shakespeare Out-Tweet a Modern Influencer?",
  "The Philosophical Implications of Pineapple on Pizza", "Time Travel for Dummies (and the paradoxes they'll inevitably cause)",
  "Debating the Merits of Talking to Your Plants", "Alien Abduction Etiquette: What You Need to Know",
  "The Unspoken Rules of Interdimensional Elevator Rides", "If Cats Ruled the World: A Political Analysis",
  "The Art of Competitive Napping", "My Favorite Conspiracy Theory (and why it's probably true)",
  "The Day AI Achieved Sarcasm", "The Best Way to Fold a Fitted Sheet: A Definitive Guide",
  "What if Pigeons Ran a Global Espionage Network?", "The Secret History of the Spork",
  "Convincing Arguments for Why My Pet is a Genius"
];

// Helper function to get random item from array
export const getRandomItem = <T,>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)];
