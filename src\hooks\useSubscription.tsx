'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { syncUsageWithActualData, shouldSyncUsage } from '@/utils/syncUsageWithActualData';

export interface UsageStats {
  scripts: number;
  audioGeneration: number;
  tokens: number;
}

export interface SubscriptionHook {
  incrementUsage: (feature: keyof UsageStats, amount: number) => Promise<void>;
  incrementTokenUsage: (type: string, tokens: number) => Promise<void>;
  syncUsage: () => Promise<void>;
  usage: UsageStats;
  isLoading: boolean;
}

/**
 * Hook to manage subscription and usage tracking
 * For BYOK (Bring Your Own Key) model - tracks usage without artificial limits
 */
export const useSubscription = (): SubscriptionHook => {
  const { user, isSignedIn, isLoaded } = useUser();
  const [usage, setUsage] = useState<UsageStats>({
    scripts: 0,
    audioGeneration: 0,
    tokens: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load usage data from user metadata when user is loaded
  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      console.log('👤 Loading user usage data...');
      const userUsage = user.unsafeMetadata?.usage as UsageStats;
      console.log('📊 User metadata usage:', userUsage);

      if (userUsage) {
        console.log('✅ Setting usage from metadata:', userUsage);
        setUsage(userUsage);
        
        // Check if we need to sync usage with actual data
        if (shouldSyncUsage(user)) {
          console.log('🔄 Auto-syncing usage with actual data...');
          syncUsageWithActualData(user, setUsage).catch(error => {
            console.error('Failed to auto-sync usage:', error);
          });
        }
      } else {
        console.log('⚠️ No usage data in metadata, syncing with actual data...');
        // If no usage data exists, try to sync with actual data
        syncUsageWithActualData(user, setUsage).catch(error => {
          console.error('Failed to sync usage on first load:', error);
        });
      }

      setIsLoading(false);
    } else if (isLoaded && !isSignedIn) {
      console.log('❌ User not signed in, resetting usage');
      setUsage({ scripts: 0, audioGeneration: 0, tokens: 0 });
      setIsLoading(false);
    }
  }, [user, isSignedIn, isLoaded]);

  const incrementUsage = async (feature: keyof UsageStats, amount: number) => {
    if (!user) {
      console.log('❌ incrementUsage: No user found');
      return;
    }

    console.log(`📊 incrementUsage: ${feature} +${amount} (current: ${usage[feature]})`);

    // Get the latest usage data from user metadata to avoid race conditions
    const currentMetadata = user.unsafeMetadata?.usage as UsageStats || {
      scripts: 0,
      audioGeneration: 0,
      tokens: 0,
    };

    console.log('📊 Current metadata before increment:', currentMetadata);

    const newUsage = {
      ...currentMetadata,
      [feature]: currentMetadata[feature] + amount,
    };

    console.log(`📊 New usage will be:`, newUsage);

    try {
      // Update user metadata with new usage
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          usage: newUsage,
        },
      });
      console.log(`✅ Successfully updated ${feature} usage to ${newUsage[feature]}`);
      setUsage(newUsage);
    } catch (error) {
      console.error('❌ Failed to update usage:', error);
    }
  };

  const incrementTokenUsage = async (type: string, tokens: number) => {
    await incrementUsage('tokens', tokens);
    console.log(`Token usage updated: ${type} - ${tokens} tokens`);
  };

  const syncUsage = async () => {
    if (!user) {
      console.log('❌ syncUsage: No user found');
      return;
    }
    
    try {
      await syncUsageWithActualData(user, setUsage);
    } catch (error) {
      console.error('❌ Failed to sync usage:', error);
      throw error;
    }
  };

  return {
    incrementUsage,
    incrementTokenUsage,
    syncUsage,
    usage,
    isLoading,
  };
};
