'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';

export interface SubscriptionStatus {
  isActive: boolean;
  planType: 'monthly' | 'lifetime' | null;
  status: string | null;
  stripeCustomerId: string | null;
  subscriptionId: string | null;
  currentPeriodEnd: Date | null;
  lastPaymentDate: Date | null;
  trialEnd: Date | null;
  isTrialing: boolean;
  isLoading: boolean;
}

export const useSubscriptionStatus = (): SubscriptionStatus => {
  const { user, isLoaded } = useUser();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>({
    isActive: false,
    planType: null,
    status: null,
    stripeCustomerId: null,
    subscriptionId: null,
    currentPeriodEnd: null,
    lastPaymentDate: null,
    trialEnd: null,
    isTrialing: false,
    isLoading: true,
  });

  useEffect(() => {
    if (isLoaded && user) {
      const metadata = user.unsafeMetadata;
      const status = metadata?.subscriptionStatus as string;
      const planType = metadata?.planType as 'monthly' | 'lifetime';
      const stripeCustomerId = metadata?.stripeCustomerId as string;
      const subscriptionId = metadata?.subscriptionId as string;
      const currentPeriodEnd = metadata?.currentPeriodEnd ? new Date(metadata.currentPeriodEnd as string) : null;
      const lastPaymentDate = metadata?.lastPaymentDate ? new Date(metadata.lastPaymentDate as string) : null;
      const trialEnd = metadata?.trialEnd ? new Date(metadata.trialEnd as string) : null;

      // Determine if subscription is in trial
      const isTrialing = !!(status === 'trialing' && trialEnd && trialEnd > new Date());

      // Determine if subscription is active
      let isActive = false;
      if (planType === 'lifetime') {
        // Lifetime is always active once purchased
        isActive = status === 'active';
      } else if (planType === 'monthly') {
        // Monthly is active if status is active/trialing and not past due
        isActive = (status === 'active' || status === 'trialing') && 
                   (!currentPeriodEnd || currentPeriodEnd > new Date()) &&
                   (!trialEnd || trialEnd > new Date() || status === 'active');
      }

      setSubscriptionStatus({
        isActive,
        planType,
        status,
        stripeCustomerId,
        subscriptionId,
        currentPeriodEnd,
        lastPaymentDate,
        trialEnd,
        isTrialing,
        isLoading: false,
      });
    } else if (isLoaded) {
      // User not signed in
      setSubscriptionStatus({
        isActive: false,
        planType: null,
        status: null,
        stripeCustomerId: null,
        subscriptionId: null,
        currentPeriodEnd: null,
        lastPaymentDate: null,
        trialEnd: null,
        isTrialing: false,
        isLoading: false,
      });
    }
  }, [user, isLoaded]);

  return subscriptionStatus;
};

export const createCheckoutSession = async (priceId: string) => {
  try {
    const response = await fetch('/api/stripe/create-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ priceId }),
    });

    if (!response.ok) {
      throw new Error('Failed to create checkout session');
    }

    const { sessionId } = await response.json();
    
    // Redirect to Stripe Checkout
    const stripe = (await import('@stripe/stripe-js')).loadStripe(
      process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
    );
    
    const stripeInstance = await stripe;
    if (stripeInstance) {
      await stripeInstance.redirectToCheckout({ sessionId });
    }
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
};

export const openCustomerPortal = async (userEmail?: string) => {
  try {
    const response = await fetch('/api/stripe/customer-portal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to create customer portal session');
    }

    const { url } = await response.json();
    
    // Open the portal in a new tab
    window.open(url, '_blank');
  } catch (error) {
    console.error('Error opening customer portal:', error);
    
    // Fallback to the manual portal URL if API fails
    const portalUrl = process.env.NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL;
    if (portalUrl) {
      const urlWithEmail = userEmail 
        ? `${portalUrl}?email=${encodeURIComponent(userEmail)}`
        : portalUrl;
      window.open(urlWithEmail, '_blank');
    } else {
      alert('Unable to open customer portal. Please contact support.');
    }
  }
};

export const syncSubscriptionStatus = async () => {
  try {
    const response = await fetch('/api/stripe/sync-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to sync subscription status');
    }

    const result = await response.json();
    
    // Force page reload to refresh the subscription status
    window.location.reload();
    
    return result;
  } catch (error) {
    console.error('Error syncing subscription status:', error);
    throw error;
  }
};
