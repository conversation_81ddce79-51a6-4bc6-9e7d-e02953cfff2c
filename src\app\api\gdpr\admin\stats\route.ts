import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { BackgroundJobQueue } from '@/lib/BackgroundJobQueue';

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check
    // For now, any authenticated user can access this (you should restrict this to admins)
    // const isAdmin = await checkAdminRole(userId);
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    // }

    const jobQueue = BackgroundJobQueue.getInstance();
    
    // Get queue statistics
    const stats = jobQueue.getQueueStats();
    
    // Get recent jobs (last 50)
    const allJobs = Array.from((jobQueue as any).jobs.values()) as any[];
    const recentJobs = allJobs
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 50)
      .map((job: any) => ({
        id: job.id,
        type: job.type,
        status: job.status,
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        error: job.error,
        retryCount: job.retryCount
      }));

    // Clean up old jobs (runs periodically)
    jobQueue.cleanupOldJobs(30); // Clean jobs older than 30 days

    return NextResponse.json({
      success: true,
      stats,
      recentJobs,
      systemHealth: {
        queueProcessing: 'operational',
        emailService: process.env.RESEND_API_KEY || process.env.SENDGRID_API_KEY ? 'operational' : 'not_configured',
        storageService: process.env.B2_APPLICATION_KEY_ID ? 'operational' : 'not_configured'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting admin stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
