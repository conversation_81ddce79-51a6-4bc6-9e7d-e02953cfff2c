/**
 * GDPR Data Export Automation Service
 * Handles automated collection and export of user data from all sources
 */

import { auth, clerkClient } from '@clerk/nextjs/server';
import Strip<PERSON> from 'stripe';
import { ServerCloudStorageService } from './CloudStorageService';
import { EmailService } from './EmailService';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';

export interface DataExportRequest {
  id: string;
  userId: string;
  requestedAt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  completedAt?: string;
  downloadUrl?: string;
  filePath?: string; // B2 file path for secure downloads
  expiresAt?: string;
  errorMessage?: string;
}

export interface ExportedData {
  userAccount: any;
  projects: any[];
  subscriptionData: any;
  usageData: any;
  supportCommunications: any[];
  metadata: {
    exportedAt: string;
    dataTypes: string[];
    totalFiles: number;
    totalSize: number;
  };
}

export class GDPRDataExportService {
  private stripe: Stripe;
  private cloudStorage: ServerCloudStorageService;
  private emailService: EmailService;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

    this.cloudStorage = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
      applicationKey: process.env.B2_APPLICATION_KEY!,
      bucketName: process.env.B2_BUCKET_NAME!,
    });

    this.emailService = EmailService.getInstance();
  }

  /**
   * Process a data export request
   */
  async processDataExport(userId: string, requestId: string): Promise<DataExportRequest> {
    try {
      console.log(`🚀 GDPRDataExportService: Starting data export for user: ${userId}, request: ${requestId}`);

      // Update request status to processing
      const request: DataExportRequest = {
        id: requestId,
        userId,
        requestedAt: new Date().toISOString(),
        status: 'processing'
      };

      console.log('📊 Step 1: Collecting user data...');
      // Collect all user data
      const exportData = await this.collectUserData(userId);
      console.log('✅ Step 1 completed: User data collected');

      console.log('📦 Step 2: Creating ZIP archive...');
      // Create ZIP archive
      const zipBuffer = await this.createExportArchive(exportData, userId);
      console.log(`✅ Step 2 completed: ZIP archive created (${(zipBuffer.length / 1024 / 1024).toFixed(2)} MB)`);

      console.log('💾 Step 3: Storing export file...');
      // Upload to secure storage with expiration
      const storeResult = await this.storeExportFile(zipBuffer, userId, requestId);
      console.log('✅ Step 3 completed: File stored');

      // Update request with completion
      request.status = 'completed';
      request.completedAt = new Date().toISOString();
      request.downloadUrl = storeResult.downloadUrl;
      request.filePath = storeResult.filePath; // Store the actual file path
      request.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days

      console.log(`📧 Step 4: Sending email notification...`);
      console.log(`Download URL: ${storeResult.downloadUrl}`);
      console.log(`Expires at: ${request.expiresAt}`);

      // Send email notification
      await this.sendExportCompletionEmail(userId, storeResult.downloadUrl, request.expiresAt);
      console.log('✅ Step 4 completed: Email notification sent');

      console.log(`🎉 Data export fully completed for user: ${userId}`);
      return request;

    } catch (error) {
      console.error(`❌ Data export failed for user: ${userId}`, error);
      
      return {
        id: requestId,
        userId,
        requestedAt: new Date().toISOString(),
        status: 'failed',
        completedAt: new Date().toISOString(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Collect user data from all sources
   */
  private async collectUserData(userId: string): Promise<ExportedData> {
    console.log(`📊 Collecting user data for: ${userId}`);

    const [
      userAccount,
      projectsResult,
      subscriptionData,
      usageData,
      supportResult
    ] = await Promise.all([
      this.collectClerkUserData(userId),
      this.collectProjectData(userId),
      this.collectStripeData(userId),
      this.collectUsageData(userId),
      this.collectSupportData(userId)
    ]);

    // Ensure we have arrays for projects and support communications
    const projects = Array.isArray(projectsResult) ? projectsResult : [];
    const supportCommunications = Array.isArray(supportResult) ? supportResult : [];

    const exportData: ExportedData = {
      userAccount,
      projects,
      subscriptionData,
      usageData,
      supportCommunications,
      metadata: {
        exportedAt: new Date().toISOString(),
        dataTypes: [
          'Account information',
          'Profile data',
          'Project files and metadata',
          'Usage analytics',
          'Subscription information',
          'Support communications'
        ],
        totalFiles: Array.isArray(projects) ? projects.reduce((sum: number, p: any) => sum + (p.files?.length || 0), 0) : 0,
        totalSize: 0 // Will be calculated during ZIP creation
      }
    };

    return exportData;
  }

  /**
   * Collect user data from Clerk
   */
  private async collectClerkUserData(userId: string) {
    try {
      console.log(`👤 Collecting Clerk user data for: ${userId}`);
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);

      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        emailAddresses: user.emailAddresses.map(email => ({
          emailAddress: email.emailAddress,
          verification: email.verification,
          primary: user.primaryEmailAddressId === email.id
        })),
        phoneNumbers: user.phoneNumbers,
        imageUrl: user.imageUrl, // Fixed property name
        publicMetadata: user.publicMetadata,
        unsafeMetadata: user.unsafeMetadata,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastSignInAt: user.lastSignInAt,
        lastActiveAt: user.lastActiveAt
      };
    } catch (error) {
      console.error('Error collecting Clerk data:', error);
      return { error: 'Failed to collect account data' };
    }
  }

  /**
   * Collect project data and files from Backblaze B2
   */
  private async collectProjectData(userId: string) {
    try {
      console.log(`📁 Collecting project data for: ${userId}`);
      await this.cloudStorage.initialize();

      const projects = await this.cloudStorage.listProjects(userId);
      const detailedProjects = [];

      for (const projectSummary of projects) {
        try {
          // Get full project data
          const projectData = await this.cloudStorage.loadProject(projectSummary.id, userId);
          
          // Get scripts
          const scripts = await this.cloudStorage.listScripts(projectSummary.id, userId);
          
          // Get audio files
          const audioFiles = await this.cloudStorage.listAudio(projectSummary.id, userId);

          detailedProjects.push({
            ...projectData,
            scripts,
            audioFiles,
            summary: projectSummary
          });
        } catch (error) {
          console.warn(`Failed to load project ${projectSummary.id}:`, error);
          detailedProjects.push({
            ...projectSummary,
            error: 'Failed to load full project data'
          });
        }
      }

      return detailedProjects;
    } catch (error) {
      console.error('Error collecting project data:', error);
      return { error: 'Failed to collect project data' };
    }
  }

  /**
   * Collect subscription data from Stripe
   */
  private async collectStripeData(userId: string) {
    try {
      console.log(`💳 Collecting Stripe data for: ${userId}`);
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);
      const stripeCustomerId = user.unsafeMetadata?.stripeCustomerId as string;

      if (!stripeCustomerId) {
        return { message: 'No Stripe customer data found' };
      }

      const [customer, subscriptions, invoices, paymentMethods] = await Promise.all([
        this.stripe.customers.retrieve(stripeCustomerId),
        this.stripe.subscriptions.list({ customer: stripeCustomerId, limit: 100 }),
        this.stripe.invoices.list({ customer: stripeCustomerId, limit: 100 }),
        this.stripe.paymentMethods.list({ customer: stripeCustomerId, limit: 100 })
      ]);

      const customerData = customer as any; // Type assertion for Stripe customer

      return {
        customer: {
          id: customerData.id,
          email: customerData.email,
          created: customerData.created,
          metadata: customerData.metadata,
          // Remove sensitive payment details but keep transaction history
        },
        subscriptions: subscriptions.data.map(sub => ({
          id: sub.id,
          status: sub.status,
          created: sub.created,
          currentPeriodStart: (sub as any).current_period_start,
          currentPeriodEnd: (sub as any).current_period_end,
          canceledAt: sub.canceled_at,
          cancelAtPeriodEnd: sub.cancel_at_period_end,
          metadata: sub.metadata
        })),
        invoices: invoices.data.map(inv => ({
          id: inv.id,
          status: inv.status,
          created: inv.created,
          amountPaid: inv.amount_paid,
          currency: inv.currency,
          description: inv.description,
          metadata: inv.metadata
        })),
        paymentMethods: paymentMethods.data.map(pm => ({
          id: pm.id,
          type: pm.type,
          created: pm.created,
          // Remove sensitive card/bank details
          last4: (pm as any).card?.last4 || null,
          brand: (pm as any).card?.brand || null
        }))
      };
    } catch (error) {
      console.error('Error collecting Stripe data:', error);
      return { error: 'Failed to collect subscription data' };
    }
  }

  /**
   * Collect usage analytics data
   */
  private async collectUsageData(userId: string) {
    try {
      console.log(`📈 Collecting usage data for: ${userId}`);
      
      // This would typically come from your analytics database
      // For now, we'll return metadata from user projects and activities
      
      return {
        message: 'Usage analytics not yet implemented in export',
        placeholder: {
          totalProjects: 0,
          totalAudioGenerated: 0,
          lastActiveDate: new Date().toISOString(),
          accountAge: 'Calculated from Clerk data'
        }
      };
    } catch (error) {
      console.error('Error collecting usage data:', error);
      return { error: 'Failed to collect usage data' };
    }
  }

  /**
   * Collect support communications
   */
  private async collectSupportData(userId: string) {
    try {
      console.log(`🎧 Collecting support data for: ${userId}`);
      
      // This would typically come from your support ticket system
      // For now, return placeholder as array
      
      return [{
        message: 'Support communications not yet implemented in export',
        placeholder: {
          totalTickets: 0,
          lastContactDate: null,
          supportLevel: 'Standard'
        }
      }];
    } catch (error) {
      console.error('Error collecting support data:', error);
      return [{ error: 'Failed to collect support communications' }];
    }
  }

  /**
   * Create ZIP archive with all user data
   */
  private async createExportArchive(exportData: ExportedData, userId: string): Promise<Buffer> {
    console.log(`📦 Creating export archive for user: ${userId}`);

    const zip = new JSZip();

    // Add main data file
    zip.file('user-data.json', JSON.stringify(exportData, null, 2));

    // Add README
    const readme = `
WordWave Studio - Personal Data Export
======================================

This archive contains all your personal data from WordWave Studio, exported in compliance with GDPR Article 15 (Right of Access).

Contents:
- user-data.json: Complete data export in JSON format
- projects/: Individual project files with actual audio content
- README.txt: This file

Data included:
${exportData.metadata.dataTypes.map(type => `- ${type}`).join('\n')}

Project Structure:
Each project folder contains:
- project-data.json: Project metadata and configuration
- audio/: Folder containing your generated audio files (.wav)
- audio/audio-metadata.json: Audio file metadata and information

Export details:
- Exported on: ${exportData.metadata.exportedAt}
- Total files: ${exportData.metadata.totalFiles}
- Request ID: ${userId}

Note: This export includes your actual audio files. The file size may be large depending on how much audio content you've generated.

If you have questions about this data export, please contact our privacy <NAME_EMAIL>.

This download link will expire 7 days from the export date for security purposes.
    `.trim();

    zip.file('README.txt', readme);

    // Add project files including actual audio content
    if (exportData.projects && Array.isArray(exportData.projects)) {
      for (const project of exportData.projects) {
        if (project.id && !project.error) {
          const projectFolder = zip.folder(`projects/${project.id}`);
          if (projectFolder) {
            projectFolder.file('project-data.json', JSON.stringify(project, null, 2));
            
            // Include actual audio files in the export
            if (project.audioFiles && project.audioFiles.length > 0) {
              const audioFolder = projectFolder.folder('audio');
              if (audioFolder) {
                for (const audioItem of project.audioFiles) {
                  try {
                    console.log(`📥 Including audio files for ${audioItem.id}`);
                    
                    // Download each audio file and add to ZIP
                    for (const fileObj of audioItem.files) {
                      try {
                        // Extract filename from file object
                        const fileName = typeof fileObj === 'string' ? fileObj : fileObj.name;
                        const audioPath = `users/${userId}/projects/${project.id}/audio/${audioItem.id}/${fileName}`;
                        console.log(`📥 Downloading audio file: ${audioPath}`);
                        
                        const audioBuffer = await this.cloudStorage.downloadFilePublic(audioPath);
                        audioFolder.file(fileName, audioBuffer);
                        
                        console.log(`✅ Added audio file: ${fileName} (${(audioBuffer.length / 1024).toFixed(1)} KB)`);
                      } catch (audioError) {
                        console.warn(`⚠️ Failed to download audio file ${typeof fileObj === 'string' ? fileObj : fileObj.name}:`, audioError);
                        // Add error note instead of file
                        const errorMessage = audioError instanceof Error ? audioError.message : 'Unknown error';
                        const fileName = typeof fileObj === 'string' ? fileObj : fileObj.name;
                        audioFolder.file(`${fileName}.error.txt`, `Failed to download: ${errorMessage}`);
                      }
                    }
                  } catch (error) {
                    console.warn(`⚠️ Failed to process audio item ${audioItem.id}:`, error);
                  }
                }
                
                // Also include metadata for reference
                audioFolder.file('audio-metadata.json', JSON.stringify(project.audioFiles, null, 2));
              }
            }
          }
        }
      }
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    exportData.metadata.totalSize = zipBuffer.length;

    console.log(`✅ Archive created: ${(zipBuffer.length / 1024 / 1024).toFixed(2)} MB`);
    return zipBuffer;
  }

  /**
   * Store export file securely with expiration
   */
  private async storeExportFile(zipBuffer: Buffer, userId: string, requestId: string): Promise<{downloadUrl: string, filePath: string}> {
    console.log(`💾 Storing export file for user: ${userId}`);

    try {
      await this.cloudStorage.initialize();
      
      const fileName = `exports/${userId}/${requestId}/data-export-${Date.now()}.zip`;
      
      // Upload to B2 (you might want a separate bucket for exports)
      const uploadResult = await (this.cloudStorage as any).uploadFile(
        fileName,
        zipBuffer,
        'application/zip'
      );

      // Generate a secure download URL (this would need B2 signed URL implementation)
      const downloadUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/gdpr/download/${requestId}`;
      
      console.log(`✅ Export file stored: ${fileName}`);
      return {
        downloadUrl,
        filePath: fileName
      };
    } catch (error) {
      console.error('Error storing export file:', error);
      throw new Error('Failed to store export file');
    }
  }

  /**
   * Send email notification when export is ready
   */
  private async sendExportCompletionEmail(userId: string, downloadUrl: string, expiresAt: string): Promise<void> {
    console.log(`📧 GDPRDataExportService: Sending export completion email for user: ${userId}`);

    try {
      console.log('👤 Getting user data from Clerk...');
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);
      console.log('✅ User data retrieved from Clerk');
      
      const userEmail = user.emailAddresses.find(email => 
        email.id === user.primaryEmailAddressId
      )?.emailAddress;

      if (!userEmail) {
        console.warn('❌ No email address found for user');
        return;
      }

      console.log(`📧 Target email: ${userEmail}`);

      const userName = user.firstName || user.lastName ? 
        `${user.firstName || ''} ${user.lastName || ''}`.trim() : 
        'there';

      console.log(`👋 User name: ${userName}`);
      console.log(`🔗 Download URL: ${downloadUrl}`);
      console.log(`⏰ Expires at: ${expiresAt}`);

      console.log('📤 Calling email service...');
      console.log('📧 Email parameters:', {
        userEmail,
        userName,
        downloadUrl: downloadUrl.substring(0, 50) + '...',
        expiresAt
      });
      
      const success = await this.emailService.sendDataExportCompleteEmail(
        userEmail,
        userName,
        downloadUrl,
        expiresAt
      );

      console.log(`📧 Email service returned: ${success}`);
      
      if (success) {
        console.log(`✅ Export completion email sent successfully to: ${userEmail}`);
      } else {
        console.warn(`⚠️ Failed to send export completion email to: ${userEmail}`);
        console.warn('⚠️ Check email service configuration and API keys');
      }

    } catch (error) {
      console.error('💥 Error sending export completion email:', error);
      console.error('Email error stack:', error instanceof Error ? error.stack : 'No stack trace');
      // Don't throw error - export was successful even if email failed
    }
  }
}
