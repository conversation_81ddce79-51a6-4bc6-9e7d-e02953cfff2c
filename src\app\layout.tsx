import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import FontLoader from '@/components/FontLoader';
import CookieConsent from '@/components/CookieConsent';
import Banner from '@/components/Banner';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import "./globals.css";

export const metadata: Metadata = {
  title: "WordWave Studio - AI-Powered Audio Content Generation",
  description: "Create professional podcasts and monologues with AI-generated scripts and high-quality text-to-speech synthesis",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
  
  // During build time, environment variables might not be available
  // This prevents build failures while still requiring the key at runtime
  if (!publishableKey && process.env.NODE_ENV === 'production') {
    console.warn('Missing NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY in production');
  }

  return (
    <ClerkProvider publishableKey={publishableKey || ''}>
      <html lang="en">
        <body className="antialiased roboto-font">
          <FontLoader />
          <Banner />
          {children}
          <CookieConsent />
          <Analytics />
          <SpeedInsights />
        </body>
      </html>
    </ClerkProvider>
  );
}
