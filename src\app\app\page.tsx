"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GoogleGenAI } from '@google/genai';

import Waves from "../../components/Waves";
import Noise from "../../components/Noise";
import ShinyText from "@/components/ShinyText";
import { AuthSection } from "@/components/AuthSection";
import { AccountMenu } from "@/components/AccountMenu";
import Modal from "@/components/Modal";
import Footer from "@/components/Footer";
import { useApiKey } from "@/hooks/useApiKey";
import { useSubscription } from "@/hooks/useSubscription";
import { useSubscriptionStatus, syncSubscriptionStatus } from "@/hooks/useSubscriptionStatus";

import { ProjectSaveLoad } from "@/components/ProjectSaveLoad";
import AudioGallery from "@/components/AudioGallery";
import "@/components/AudioGallery.css";
import ProjectStatusIndicator from "@/components/ProjectStatusIndicator";
import { ApiKeyCard } from "@/components/ApiKeyCard";
import { useSeparatedApiClient } from "@/services/SeparatedApiClient";
import { createAudio, createProjectConfig, createScript } from "@/utils/SeparatedProjectUtils";
import { useUser } from "@clerk/nextjs";
import Notification, { useNotification } from "@/components/Notification";
import ProjectPickerModal from "@/components/ProjectPickerModal";
import ScriptEffectsGuide from "@/components/ScriptEffectsGuide";

import {
  AVAILABLE_SCRIPT_MODELS,
  AVAILABLE_TTS_MODELS,
  AVAILABLE_VOICES,
  RANDOM_PODCAST_NAMES,
  RANDOM_PERSONA_NAMES,
  RANDOM_PODCAST_STYLES,
  RANDOM_PODCAST_TOPICS,
  getRandomItem,
  preferredDefaultVoiceApiNames
} from "@/lib/constants";

import { SynthesisMode, AudioFile } from "@/lib/types";
import { generateProjectId, base64ToArrayBuffer, convertToWav, audioBufferToWav } from "@/lib/audioUtils";
import { generateAudioFilename, generateGenerationId } from "@/utils/audioFileNaming";

// Additional interfaces for TTS functionality
interface Utterance {
  speakerIndex: number;
  text: string;
}

interface ProcessedAudioChunk {
  speakerIndex: number;
  audioBuffer: AudioBuffer;
  duration: number;
  originalMimeType: string;
}

// Script generation function
const generateScript = async (
  apiKey: string,
  topic: string,
  synthesisMode: SynthesisMode,
  speaker1Name: string,
  speaker2Name: string,
  projectStyle: string,
  scriptLinks: string,
  wordCount: number,
  selectedModel: string
): Promise<{ content: string; tokenCount?: any }> => {
  console.log('🔧 generateScript called with:', {
    hasApiKey: !!apiKey,
    apiKeyLength: apiKey?.length || 0,
    apiKeyTrimmed: apiKey?.trim().length || 0,
    selectedModel
  });

  if (!apiKey || apiKey.trim() === '') {
    console.error('❌ API key validation failed:', { apiKey: apiKey || 'undefined' });
    throw new Error('API key is required for script generation');
  }

  const trimmedApiKey = apiKey.trim();
  console.log('✅ Creating GoogleGenAI client with key length:', trimmedApiKey.length);

  if (trimmedApiKey.length === 0) {
    throw new Error('API key is empty after trimming');
  }

  const genAI = new GoogleGenerativeAI(trimmedApiKey);
  console.log('✅ GoogleGenerativeAI client created, getting model...');
  const model = genAI.getGenerativeModel({ model: selectedModel });

  let scriptTypeSpecifics = '';
  if (synthesisMode === 'podcast') {
    scriptTypeSpecifics = `Podcast Title: "${topic || 'Untitled Podcast'}"
Speakers:
  - Speaker 1 Persona: "${speaker1Name || 'Host'}" (Script lines must use "Speaker 1:")
  - Speaker 2 Persona: "${speaker2Name || 'Guest'}" (Script lines must use "Speaker 2:")
Instructions:
- CRITICAL: Clearly label each speaker's lines using "Speaker 1: [dialogue]" and "Speaker 2: [dialogue]". This format is essential for downstream processing.
- The output should ONLY be the script itself, ready to be used. Start directly with "Speaker 1:". Do not include any preambles, titles, or introductions.`;
  } else { // monologue
    scriptTypeSpecifics = `Project Title: "${topic || 'Untitled Monologue'}"
Speaker Persona: "${speaker1Name || 'Narrator'}"
Instructions:
- The output should ONLY be the script for a single speaker.
- Do NOT use "Speaker 1:" or any other speaker labels. Start directly with the text of the monologue/speech.
- Do not include any preambles, titles, or introductions.`;
  }

  // Enhanced TTS Guidelines based on Google Gemini TTS Sound Effects Rules
  const ttsGuidelines = `
## VOICE EFFECTS & EXPRESSIVENESS GUIDELINES ##

This script will be converted to speech using Google Gemini 2.5 TTS, which supports natural language voice effects. 
To make the audio engaging and expressive, include parenthetical vocal cues throughout the script.

### SUPPORTED VOCAL EFFECTS:

**Emotions & Tones:**
- Positive: (cheerfully), (excitedly), (joyfully), (happily), (enthusiastically), (warmly)
- Negative: (sadly), (angrily), (frustrated), (disappointingly), (coldly), (grumpily)
- Neutral/Thoughtful: (calmly), (seriously), (thoughtfully), (solemnly), (gently), (tenderly)
- Playful: (playfully), (mischievously), (sarcastically), (teasingly)
- Mysterious: (mysteriously), (conspiratorially), (suspiciously), (ominously)

**Vocal Modulations:**
- Volume: (whispering), (quietly), (loudly), (shouting), (booming)
- Pace: (quickly), (slowly), (rushed), (deliberately), (hesitantly)
- Style: (dramatically), (emphatically), (monotone), (sing-song), (drawling)

**Sound Effects:**
- Natural sounds: (laughing), (giggling), (sighing vocally), (gasping), (coughing)
- Pauses: (pauses), (brief pause), (long pause)
- Speech patterns: (muttering), (stammering), (clears throat)

### USAGE RULES:
1. Place vocal cues at the beginning of dialogue: "(excitedly) This is amazing!"
2. Use multiple effects when appropriate: "(whispering quietly) It's a secret"
3. Make effects match the emotional context and speaker personality
4. Include 3-5 vocal effects per 100 words for engaging delivery
5. Vary effects to create dynamic, natural-sounding conversations
`;

  const prompt = `You are an expert script writer specializing in audio content creation.
Your task is to generate an engaging, expressive script optimized for text-to-speech conversion.

${scriptTypeSpecifics}

Overall Style: "${projectStyle || 'General conversation'}"

Topic for this script: "${topic}"
Target Word Count: Approximately ${wordCount} words. Aim to be close to this length.

${(scriptLinks && scriptLinks.trim()) ? `Please consider the following sources for information and inspiration. Incorporate relevant details or perspectives from these links naturally into the content. Do not simply summarize the links.
Relevant Links (one per line):
${scriptLinks.trim()}` : ''}

${ttsGuidelines}

### SCRIPT REQUIREMENTS:
- Create engaging, natural dialogue that sounds great when spoken aloud
- Include appropriate vocal cues throughout to guide emotional delivery
- Content should reflect the specified speaker persona(s), style, and tone
- Ensure logical flow and adequate topic coverage within the target word count
- Make conversations feel realistic and dynamic with varied emotional tones
- Use vocal effects to enhance storytelling, emphasize key points, and maintain listener engagement
- If no links are provided, generate content based on your knowledge of the topic

IMPORTANT: Focus ONLY on vocal performance cues (how something is said). Avoid non-vocal sounds or physical actions.
`;

  const result = await model.generateContent(prompt);
  const response = await result.response;
  const content = response.text();

  // Get usage metadata if available
  const usage = response.usageMetadata;
  const tokenCount = usage ? {
    prompt: usage.promptTokenCount || null,
    candidates: usage.candidatesTokenCount || null,
    total: usage.totalTokenCount || null
  } : {
    prompt: prompt.length,
    candidates: content.length,
    total: prompt.length + content.length
  };

  return {
    content: content.trim(),
    tokenCount
  };
};

// Project context modes
type ProjectMode = 'new' | 'loaded';

// Project context state
interface ProjectContext {
  mode: ProjectMode;
  projectId: string | null;
  projectName: string;
  isAutoSaveEnabled: boolean;
  lastSaved?: string;
}

// Interface for project state
interface ProjectState {
  id: string;
  projectName: string;
  speaker1Name: string;
  speaker2Name: string;
  projectStyle: string;
  topic: string;
  scriptLinks: string;
  wordCount: number;
  synthesisMode: SynthesisMode;
  selectedScriptModel: string;
  selectedTtsModel: string;
  defaultVoice1: string;
  defaultVoice2: string;
  script?: string;
  audioFiles: AudioFile[];
  isGeneratingScript: boolean;
  isGeneratingAudio: boolean;
}

export default function Home() {
  const { apiKey, isSignedIn } = useApiKey();
  const { incrementUsage, incrementTokenUsage } = useSubscription();
  const subscriptionStatus = useSubscriptionStatus();
  const { user } = useUser();
  const separatedApiClient = useSeparatedApiClient();

  // Project context state - replaces the old loadedProjectId approach
  const [projectContext, setProjectContext] = useState<ProjectContext>({
    mode: 'new',
    projectId: null,
    projectName: '',
    isAutoSaveEnabled: false,
  });

  // Script and Audio management state
  const [currentScriptId, setCurrentScriptId] = useState<string | null>(null);
  const [currentAudioId, setCurrentAudioId] = useState<string | null>(null);
  const [savedScripts, setSavedScripts] = useState<any[]>([]);
  const [savedAudio, setSavedAudio] = useState<any[]>([]);
  const [isAutoSaving, setIsAutoSaving] = useState<boolean>(false); // Track auto-save status
  const [existingProjects, setExistingProjects] = useState<any[]>([]); // Track existing projects for duplicate checking

  // Additional state for script input and TTS
  const [inputText, setInputText] = useState<string>(
    "Speaker 1: (insanely excited) Welcome back to Quantum Quirks! Today, we're diving deep into the mysteries of the universe.\nSpeaker 2: (whispering very quietly) And I have a secret theory about black holes... (chuckles softly) it's a bit wild.\nSpeaker 1: (laughing) Oh, I can't wait to hear this! But first, a quick word from our sponsors... (clears throat)\nSpeaker 2: (mock serious tone) Indeed. Don't touch that dial, or... or the cosmic hamsters will get you!"
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<string>('');

  // Notification system
  const { notification, showNotification, clearNotification } = useNotification();

  // Modal state
  const [modal, setModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    type: 'info' | 'error' | 'warning' | 'success';
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'info'
  });

  // First-visit project picker state
  const [pickerOpen, setPickerOpen] = useState<boolean>(false);
  // Bridge state to open the internal Load dialog from the picker
  const [openLoadDialog, setOpenLoadDialog] = useState<boolean>(false);

  // Additional state for TTS processing
  const [ttsPromptTokenCount, setTtsPromptTokenCount] = useState<number | null>(null);
  const [ttsCandidatesTokenCount, setTtsCandidatesTokenCount] = useState<number | null>(null);
  const [ttsTotalTokenCount, setTtsTotalTokenCount] = useState<number | null>(null);
  const [aiClient, setAiClient] = useState<GoogleGenAI | null>(null);

  const audioContextRef = useRef<AudioContext | null>(null);

  // Helper function to show modal
  const showModal = (title: string, message: string, type: 'info' | 'error' | 'warning' | 'success' = 'info') => {
    setModal({
      isOpen: true,
      title,
      message,
      type
    });
  };

  const handleLoadScript = (scriptContent: string) => {
    setInputText(scriptContent);
    showModal('Script Loaded', 'The script has been loaded into the script editor.', 'success');
  };

  const closeModal = () => {
    setModal(prev => ({ ...prev, isOpen: false }));
  };

  // Project state
  const [project, setProject] = useState<ProjectState>({
    id: generateProjectId(),
    projectName: '',
    speaker1Name: '',
    speaker2Name: '',
    projectStyle: '',
    topic: '',
    scriptLinks: '',
    wordCount: 500,
    synthesisMode: 'podcast',
    selectedScriptModel: AVAILABLE_SCRIPT_MODELS.find(m => m.apiName.includes('gemini-2.5-flash-lite'))?.apiName || AVAILABLE_SCRIPT_MODELS[0]?.apiName || 'gemini-2.5-flash-lite',
    selectedTtsModel: AVAILABLE_TTS_MODELS[0].apiName, // Use Flash TTS as default (now index 0)
    defaultVoice1: (preferredDefaultVoiceApiNames[0] && AVAILABLE_VOICES.some(v => v.apiName === preferredDefaultVoiceApiNames[0]))
      ? preferredDefaultVoiceApiNames[0]
      : AVAILABLE_VOICES[0]?.apiName || 'zephyr',
    defaultVoice2: (preferredDefaultVoiceApiNames[1] && AVAILABLE_VOICES.some(v => v.apiName === preferredDefaultVoiceApiNames[1]))
      ? preferredDefaultVoiceApiNames[1]
      : AVAILABLE_VOICES[1]?.apiName || 'puck',
    audioFiles: [],
    isGeneratingScript: false,
    isGeneratingAudio: false,
  });

  // Current audio generation voice settings (can override project defaults)
  const [currentVoice1, setCurrentVoice1] = useState<string>('');
  const [currentVoice2, setCurrentVoice2] = useState<string>('');

  // Initialize current voices from project defaults when project changes
  useEffect(() => {
    if (project.defaultVoice1 && !currentVoice1) {
      setCurrentVoice1(project.defaultVoice1);
    }
    if (project.defaultVoice2 && !currentVoice2) {
      setCurrentVoice2(project.defaultVoice2);
    }
  }, [project.defaultVoice1, project.defaultVoice2, currentVoice1, currentVoice2]);

  // Initialize audio context and AI client
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
  }, []);

  

  // Initialize AI client when API key changes
  useEffect(() => {
    try {
      if (!apiKey) {
        if (isSignedIn) {
          const apiKeyError = "Please configure your Gemini API key in the Account section to use the app.";
          setError(apiKeyError);
          setStatusMessage('');
        } else {
          const authError = "Please sign in and configure your API key to use the app.";
          setError(authError);
          setStatusMessage('');
        }
        return;
      }
      const client = new GoogleGenAI({ apiKey });
      setAiClient(client);
      setError(null);
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
    } catch (e: any) {
       console.error("Failed to initialize GoogleGenAI or AudioContext:", e.message);
       const initError = `Failed to initialize: ${e.message}`;
       setError(initError);
       setStatusMessage('');
    }
  }, [apiKey, isSignedIn]);

  // Update inputText when script is generated
  useEffect(() => {
    if (project.script) {
      setInputText(project.script);
    }
  }, [project.script]);

  // Update inputText based on synthesis mode
  useEffect(() => {
    if (!project.script) {
      if (project.synthesisMode === 'monologue') {
        setInputText("Enter your monologue or speech text here.");
      } else {
        setInputText("Speaker 1: Hello! We're excited to show you our native speech capabilities.\nSpeaker 2: Where you can direct a voice and create realistic dialog.");
      }
    }
  }, [project.synthesisMode, project.script]);

  // Randomize project setup
  const randomizeSetup = useCallback(() => {
    setProject(prev => ({
      ...prev,
      projectName: getRandomItem(RANDOM_PODCAST_NAMES),
      speaker1Name: getRandomItem(RANDOM_PERSONA_NAMES),
      speaker2Name: getRandomItem(RANDOM_PERSONA_NAMES),
      projectStyle: getRandomItem(RANDOM_PODCAST_STYLES),
    }));
    // Switch to new project mode when making changes
    setProjectContext({
      mode: 'new',
      projectId: null,
      projectName: 'New Project',
      isAutoSaveEnabled: false,
    });
  }, []);

  // Randomize topic
  const randomizeTopic = useCallback(() => {
    setProject(prev => ({
      ...prev,
      topic: getRandomItem(RANDOM_PODCAST_TOPICS),
    }));
  }, []);

  // Project context management functions
  const startNewProject = useCallback(() => {
    setProjectContext({
      mode: 'new',
      projectId: null,
      projectName: project.projectName || 'New Project',
      isAutoSaveEnabled: false,
    });
    console.log('🆕 Started new project mode');
  }, [project.projectName]);

  const loadProject = useCallback((projectId: string, projectName: string) => {
    setProjectContext({
      mode: 'loaded',
      projectId,
      projectName,
      isAutoSaveEnabled: true,
      lastSaved: new Date().toISOString(),
    });
    console.log(`📂 Loaded project: ${projectName} (${projectId}) - Auto-save enabled`);
  }, []);

  const updateProjectContext = useCallback((updates: Partial<ProjectContext>) => {
    setProjectContext(prev => ({ ...prev, ...updates }));
  }, []);

  // Initialize project context on mount
  useEffect(() => {
    setProjectContext({
      mode: 'new',
      projectId: null,
      projectName: 'New Project',
      isAutoSaveEnabled: false,
    });
  }, []);

  // Load existing projects for duplicate checking
  const loadExistingProjects = useCallback(async () => {
    if (!user) return;

    try {
      const projects = await separatedApiClient.listProjects();
      setExistingProjects(projects || []);
    } catch (error) {
      console.warn('Failed to load existing projects for duplicate checking:', error);
    }
  }, [user, separatedApiClient]);

  // Check if project name already exists (excluding currently loaded project)
  const isProjectNameTaken = useCallback((projectName: string | undefined) => {
    if (!projectName || !projectName.trim()) return false;
    return existingProjects.some(project => {
      // Exclude the currently loaded project from duplicate check
      if (projectContext.mode === 'loaded' && project.id === projectContext.projectId) {
        return false;
      }
      return project.projectName && project.projectName.toLowerCase() === projectName.trim().toLowerCase();
    });
  }, [existingProjects, projectContext.mode, projectContext.projectId]);

  // Load existing projects when user changes
  useEffect(() => {
    if (user && isSignedIn) {
      // Small delay to ensure authentication token is ready
      const timer = setTimeout(() => {
        loadExistingProjects();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [user, isSignedIn, loadExistingProjects]);

  // Handle script generation
  const handleGenerateScript = useCallback(async () => {
    console.log('🔑 API Key check:', {
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      apiKeyStart: apiKey?.substring(0, 10) || 'none'
    });

    // Check subscription status
    if (!subscriptionStatus.isActive) {
      showModal(
        'Subscription Required', 
        'You need an active subscription to generate scripts. Choose a plan to get started!', 
        'warning'
      );
      return;
    }

    if (!apiKey || apiKey.trim() === '') {
      showModal('API Key Required', 'Please configure your Google Gemini API key first.', 'warning');
      return;
    }

    if (!project.topic || !project.topic.trim()) {
      showModal('Topic Required', 'Please enter a topic for your script.', 'warning');
      return;
    }

    setProject(prev => ({ ...prev, isGeneratingScript: true }));

    try {
      console.log('🚀 Starting script generation with:', {
        topic: project.topic,
        mode: project.synthesisMode,
        model: project.selectedScriptModel
      });

      const result = await generateScript(
        apiKey,
        project.topic,
        project.synthesisMode,
        project.speaker1Name,
        project.speaker2Name,
        project.projectStyle,
        project.scriptLinks,
        project.wordCount,
        project.selectedScriptModel
      );

      setProject(prev => ({
        ...prev,
        script: result.content,
        isGeneratingScript: false
      }));

      // Update usage - script was successfully generated
      await incrementUsage('scripts', 1);
      if (result.tokenCount) {
        await incrementTokenUsage('script_generation', result.tokenCount.total);
      }

    } catch (error) {
      console.error('Script generation failed:', error);
      showModal('Script Generation Failed', `${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
      setProject(prev => ({ ...prev, isGeneratingScript: false }));
    }
  }, [apiKey, project, incrementUsage, incrementTokenUsage, subscriptionStatus.isActive]);

  // Utility function for retrying API calls with exponential backoff
  const retryWithBackoff = async (fn: () => Promise<any>, maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        const isRetryable = error.code === 429 ||
                           (error.message && (error.message.includes('429') ||
                                            error.message.includes('quota') ||
                                            error.message.includes('rate limit') ||
                                            error.message.includes('RESOURCE_EXHAUSTED')));

        if (!isRetryable || attempt === maxRetries) {
          throw error;
        }

        const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
        setStatusMessage(`Rate limit hit. Retrying in ${Math.round(delay / 1000)} seconds... (Attempt ${attempt}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  };

  // Handle speech generation
  const handleGenerateSpeech = useCallback(async () => {
    // Check subscription status
    if (!subscriptionStatus.isActive) {
      showModal(
        'Subscription Required', 
        'You need an active subscription to generate audio. Choose a plan to get started!', 
        'warning'
      );
      return;
    }

    if (!aiClient || !audioContextRef.current) {
      setError("Gemini API client or AudioContext is not initialized. Check API_KEY setup or console for errors.");
      setIsLoading(false);
      setStatusMessage('');
      return;
    }

    // Temporarily disabled for testing
    // if (!canUseFeature('audioGeneration')) {
    //   setError("You've reached your audio generation limit for this month. Please upgrade your plan to continue.");
    //   setIsLoading(false);
    //   setStatusMessage('');
    //   return;
    // }
    if (!inputText || !inputText.trim()) {
      setError("Please enter text to synthesize (or generate a script first).");
      setStatusMessage('');
      return;
    }

    setIsLoading(true);
    setError(null);
    setProject(prev => ({ ...prev, isGeneratingAudio: true }));
    setStatusMessage('Initializing audio generation...');
    setTtsPromptTokenCount(null);
    setTtsCandidatesTokenCount(null);
    setTtsTotalTokenCount(null);

    const audioContext = audioContextRef.current;
    const parsedUtterances: Utterance[] = [];

    if (project.synthesisMode === 'podcast') {
        const lines = inputText.split('\n').filter(line => line.trim() !== '');
        let currentSpeakerLineParsing: Utterance | null = null;

        for (const line of lines) {
            const speaker1Match = line.match(/^Speaker\s*1:/i);
            const speaker2Match = line.match(/^Speaker\s*2:/i);
            let speakerIndex = -1;
            let textContent = "";

            if (speaker1Match) {
                speakerIndex = 0;
                textContent = line.substring(speaker1Match[0].length).trim();
            } else if (speaker2Match) {
                speakerIndex = 1;
                textContent = line.substring(speaker2Match[0].length).trim();
            }

            if (speakerIndex !== -1) {
                if (textContent) {
                    if (currentSpeakerLineParsing && currentSpeakerLineParsing.speakerIndex === speakerIndex) {
                        currentSpeakerLineParsing.text += ' ' + textContent;
                    } else {
                        currentSpeakerLineParsing = { speakerIndex, text: textContent };
                        parsedUtterances.push(currentSpeakerLineParsing);
                    }
                }
            } else if (currentSpeakerLineParsing && line.trim()) {
                currentSpeakerLineParsing.text += ' ' + line.trim();
            }
        }
        if (parsedUtterances.length === 0 && inputText.trim()) {
             console.warn("No 'Speaker 1:' or 'Speaker 2:' tags found in podcast mode. Treating entire text as Speaker 1.");
             parsedUtterances.push({ speakerIndex: 0, text: inputText.trim() });
        }
    } else { // monologue mode
        if (inputText.trim()) {
            parsedUtterances.push({ speakerIndex: 0, text: inputText.trim() });
        }
    }


    if (parsedUtterances.length === 0) {
        setError("Script text is empty or could not be parsed into utterances.");
        setStatusMessage('');
        setIsLoading(false);
        return;
    }

    const contents = inputText;

    // Validate and ensure voice names are valid (use current voices for audio generation)
    const validVoice1 = currentVoice1 && AVAILABLE_VOICES.some(v => v.apiName === currentVoice1)
      ? currentVoice1
      : project.defaultVoice1 || AVAILABLE_VOICES[0]?.apiName || 'zephyr';

    const validVoice2 = currentVoice2 && AVAILABLE_VOICES.some(v => v.apiName === currentVoice2)
      ? currentVoice2
      : project.defaultVoice2 || AVAILABLE_VOICES[1]?.apiName || 'puck';

    console.log('🎤 Voice validation:', {
      currentVoice1,
      currentVoice2,
      defaultVoice1: project.defaultVoice1,
      defaultVoice2: project.defaultVoice2,
      validVoice1,
      validVoice2,
      synthesisMode: project.synthesisMode
    });

    let speechConfigPayload: any;

    if (project.synthesisMode === 'podcast') {
        const speakerVoiceConfigs = [];
        speakerVoiceConfigs.push({
            speaker: `Speaker 1`,
            voiceConfig: { prebuiltVoiceConfig: { voiceName: validVoice1 } },
        });
        speakerVoiceConfigs.push({
            speaker: `Speaker 2`,
            voiceConfig: { prebuiltVoiceConfig: { voiceName: validVoice2 } },
        });
        speechConfigPayload = {
            multiSpeakerVoiceConfig: {
                speakerVoiceConfigs: speakerVoiceConfigs,
            },
        };
    } else { // monologue
        speechConfigPayload = {
            voiceConfig: {
                prebuiltVoiceConfig: { voiceName: validVoice1 } // Use the first voice
            }
        };
    }

    const requestPayload: any = {
      model: project.selectedTtsModel,
      contents: contents,
      config: {
        responseModalities: ['AUDIO'],
        speechConfig: speechConfigPayload,
      }
    };

    try {
      const responseStream = await retryWithBackoff(
        () => aiClient.models.generateContentStream(requestPayload),
        3, // max retries
        2000 // base delay in ms
      );
      const collectedChunks: ProcessedAudioChunk[] = [];
      let utteranceIndexForTagging = 0;

      setStatusMessage('Processing audio stream...');
      console.log('🎵 Starting audio stream processing...');

      let chunkCount = 0;
      for await (const chunk of responseStream) {
        chunkCount++;
        console.log(`🎵 Processing chunk ${chunkCount}:`, {
          hasCandidate: !!chunk.candidates?.[0],
          hasContent: !!chunk.candidates?.[0]?.content,
          hasParts: !!chunk.candidates?.[0]?.content?.parts?.[0],
          hasInlineData: !!chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData,
          hasText: !!chunk.text,
          hasUsageMetadata: !!chunk.usageMetadata
        });

        if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
          const inlineData = chunk.candidates[0].content.parts[0].inlineData;
          const base64Data = inlineData.data || '';
          const originalMimeType = inlineData.mimeType || 'application/octet-stream';

          console.log(`🎵 Found audio data in chunk ${chunkCount}:`, {
            mimeType: originalMimeType,
            dataLength: base64Data.length,
            dataPreview: base64Data.substring(0, 50) + '...'
          });

          if (!base64Data) {
            console.warn(`⚠️ Empty audio data in chunk ${chunkCount}`);
            continue;
          }

          try {
            let rawAudioBytes = base64ToArrayBuffer(base64Data);
            let decodableBuffer = rawAudioBytes;

            if (originalMimeType.toLowerCase().startsWith('audio/l')) {
              const wavConvert = convertToWav(base64Data, originalMimeType);
              decodableBuffer = wavConvert.data;
            }

            const decodedAudioBuffer = await audioContext.decodeAudioData(decodableBuffer.slice(0));
            console.log(`✅ Successfully decoded audio buffer:`, {
              duration: decodedAudioBuffer.duration,
              sampleRate: decodedAudioBuffer.sampleRate,
              numberOfChannels: decodedAudioBuffer.numberOfChannels
            });

            const speakerIndexForThisChunk = project.synthesisMode === 'monologue' ? 0 :
                                            (parsedUtterances[utteranceIndexForTagging]?.speakerIndex ??
                                            (parsedUtterances[0]?.speakerIndex ?? 0));

            collectedChunks.push({
              speakerIndex: speakerIndexForThisChunk,
              audioBuffer: decodedAudioBuffer,
              duration: decodedAudioBuffer.duration,
              originalMimeType: originalMimeType,
            });

            const numParsedUtterances = parsedUtterances.length;
            setStatusMessage(
              `Processed audio segment ${collectedChunks.length} (associating with utterance ${
                numParsedUtterances > 0 ? Math.min(utteranceIndexForTagging + 1, numParsedUtterances) : 1
              }/${
                numParsedUtterances > 0 ? numParsedUtterances : 'auto'
              })...`
            );

            if (utteranceIndexForTagging < parsedUtterances.length - 1) {
               utteranceIndexForTagging++;
            }
          } catch (decodeError) {
            console.error(`❌ Failed to decode audio chunk ${chunkCount}:`, decodeError);
            // Continue processing other chunks instead of failing completely
          }

        } else if (chunk.text) {
          console.log("📝 Received non-audio text part:", chunk.text);
        } else {
          console.log(`🤔 Chunk ${chunkCount} has no recognizable audio or text data:`, chunk);
        }

        if (chunk.usageMetadata) {
          const chunkTokens = chunk.usageMetadata?.totalTokenCount || 0;

          setTtsPromptTokenCount(prev => (prev || 0) + (chunk.usageMetadata?.promptTokenCount || 0));
          setTtsCandidatesTokenCount(prev => (prev || 0) + (chunk.usageMetadata?.candidatesTokenCount || 0));
          setTtsTotalTokenCount(prev => (prev || 0) + chunkTokens);

          // Track token usage for each chunk
          if (chunkTokens > 0) {
            incrementTokenUsage('audio', chunkTokens);
          }
        }
      }

      console.log(`🎵 Finished processing stream. Total chunks: ${chunkCount}, Audio chunks collected: ${collectedChunks.length}`);

      if (collectedChunks.length === 0) {
        console.error('❌ No audio chunks were successfully processed');

        // Provide more specific diagnostics
        const diagnostics = [];
        if (chunkCount === 0) {
          diagnostics.push("No response chunks received from API");
        } else {
          diagnostics.push(`Received ${chunkCount} chunks but none contained valid audio data`);
        }

        // Check text length for potential issues
        const textLength = inputText.trim().length;
        if (textLength > 5000) {
          diagnostics.push("Text input is very long (>5000 chars) - try shorter text");
        } else if (textLength < 10) {
          diagnostics.push("Text input is very short (<10 chars) - try longer text");
        }

        // Model-specific suggestions
        const modelSuggestions = [];
        if (project.selectedTtsModel === 'models/gemini-2.0-flash-exp') {
          modelSuggestions.push("Try switching to 'Gemini Flash TTS' for better reliability");
        } else {
          modelSuggestions.push("Try switching to 'Gemini 2.0 Flash Experimental' for enhanced features");
        }

        const errorMessage = `🎵 Audio Generation Failed

${diagnostics.join('\n')}

💡 Possible Solutions:
• ${modelSuggestions[0]}
• Check your Google AI Studio quota and billing
• Ensure your text contains only standard characters
• Try again in a few minutes (API rate limits)
• Use shorter text input (under 2000 characters)

📊 Session Info:
• Text length: ${textLength} characters
• Model: ${project.selectedTtsModel}
• Mode: ${project.synthesisMode}`;

        setError(errorMessage);
        setStatusMessage('');
        setIsLoading(false);
        return;
      }

      setStatusMessage('Compositing audio track...');
      const tempAudioFiles: AudioFile[] = [];
      let totalDuration = 0;
      const utteranceOffsets: number[] = [];
      for(const chunk of collectedChunks) {
        utteranceOffsets.push(totalDuration);
        totalDuration += chunk.duration;
      }

      if (collectedChunks.length > 0 && totalDuration > 0) {
        const combinedContextDuration = Math.max(totalDuration, 0.01);
        const firstValidChunk = collectedChunks.find(c => c.audioBuffer.sampleRate > 0);
        const combinedSampleRate = firstValidChunk ? firstValidChunk.audioBuffer.sampleRate : audioContext.sampleRate;

        if (combinedSampleRate > 0) {
            const combinedOfflineCtx = new OfflineAudioContext(
              1,
              Math.ceil(combinedContextDuration * combinedSampleRate),
              combinedSampleRate
            );

            for (let i = 0; i < collectedChunks.length; i++) {
              const chunkToProcess = collectedChunks[i];
              const sourceNode = combinedOfflineCtx.createBufferSource();
              sourceNode.buffer = chunkToProcess.audioBuffer;
              sourceNode.connect(combinedOfflineCtx.destination);
              sourceNode.start(utteranceOffsets[i]);
            }

            try {
                const renderedCombinedBuffer = await combinedOfflineCtx.startRendering();
                const combinedWavArrayBuffer = audioBufferToWav(renderedCombinedBuffer);
                const combinedBlob = new Blob([combinedWavArrayBuffer], { type: 'audio/wav' });
                const combinedUrl = URL.createObjectURL(combinedBlob);

                // Generate unique generation ID and filename
                const generationId = generateGenerationId();
                const finalFileName = generateAudioFilename({
                  projectName: project.projectName || 'WordWave',
                  scriptTopic: project.topic,
                  scriptContent: inputText,
                  synthesisMode: project.synthesisMode,
                  voiceConfig: {
                    voice1: validVoice1,
                    voice2: project.synthesisMode === 'podcast' ? validVoice2 : undefined,
                  },
                  generationId,
                  extension: 'wav'
                });

                console.log(`🎵 Generated audio file: ${finalFileName}`);

                tempAudioFiles.push({
                  id: generationId,
                  name: finalFileName,
                  url: combinedUrl,
                  processedMimeType: 'audio/wav',
                  speaker: 'Combined',
                  size: combinedWavArrayBuffer.byteLength,
                  duration: totalDuration,
                  createdAt: new Date().toISOString(),
                  voiceConfig: {
                    voice1: validVoice1,
                    voice2: project.synthesisMode === 'podcast' ? validVoice2 : undefined,
                  },
                  // Enhanced metadata
                  scriptContent: inputText,
                  scriptTopic: project.topic,
                  generationId,
                  ttsModel: project.selectedTtsModel,
                  synthesisMode: project.synthesisMode,
                });
            } catch (renderError) {
                console.error('Error rendering combined audio track:', renderError);
                setError(`Error rendering combined audio track: ${renderError instanceof Error ? renderError.message : String(renderError)}. It may be unavailable.`);
            }
        } else {
             console.warn('Cannot generate combined track due to zero sample rate from chunks.');
             setError('Could not generate combined audio track: audio sample rate issue.');
        }
      }

      setProject(prev => ({ ...prev, audioFiles: [...prev.audioFiles, ...tempAudioFiles] }));
      if (tempAudioFiles.length > 0) {
        setStatusMessage(`Successfully generated audio track: ${tempAudioFiles[0].name}.`);

        // Show success notification
        showNotification(
          `🎵 Audio Generated Successfully!\n${tempAudioFiles[0].name}`,
          'success',
          4000
        );

        // Track audio usage - estimate 1 minute per generation for now
        // In a real app, you'd calculate actual duration from the audio
        incrementUsage('audioGeneration', 1);

        // If no tokens were tracked during streaming (common with audio generation),
        // estimate tokens based on input text length
        if (ttsTotalTokenCount === null || ttsTotalTokenCount === 0) {
          const estimatedTokens = Math.ceil(inputText.length / 4); // Rough estimate: ~4 chars per token
          incrementTokenUsage('audio', estimatedTokens);
          setTtsTotalTokenCount(estimatedTokens);
        }

        // Auto-save audio if project is loaded
        console.log(`🔍 Auto-save check:`, {
          mode: projectContext.mode,
          projectId: projectContext.projectId,
          isAutoSaveEnabled: projectContext.isAutoSaveEnabled,
          hasUser: !!user,
          audioFilesCount: tempAudioFiles.length
        });

        if (projectContext.mode === 'loaded' && projectContext.projectId && projectContext.isAutoSaveEnabled && user && tempAudioFiles.length > 0) {
          // Validate that we have actual audio data before attempting auto-save
          const hasValidAudioData = tempAudioFiles.some(file =>
            (file.url && file.url.startsWith('blob:')) && file.size && file.size > 0
          );

          if (!hasValidAudioData) {
            console.warn(`⚠️ Skipping auto-save - no valid audio data in generated files`);
            setStatusMessage(`Successfully generated audio track: ${tempAudioFiles[0].name}. (No audio data to save)`);
          } else {
            setIsAutoSaving(true);
            try {
              console.log(`🔄 Auto-saving audio for project: ${projectContext.projectName} (${projectContext.projectId})`);
              setStatusMessage(`Successfully generated audio track: ${tempAudioFiles[0].name}. Auto-saving to "${projectContext.projectName}"...`);

              const audioData = createAudio(
                projectContext.projectId,
                tempAudioFiles[0].name,
                tempAudioFiles,
                project.selectedTtsModel,
                currentVoice1 || project.defaultVoice1 || 'zephyr',
                currentVoice2 || project.defaultVoice2 || 'puck',
                project.synthesisMode,
                user.id
              );

              await separatedApiClient.saveAudio(projectContext.projectId, audioData);

            // Update project context with last saved time
            updateProjectContext({ lastSaved: new Date().toISOString() });

            console.log(`✅ Auto-saved audio to project: ${projectContext.projectName}`);
            setStatusMessage(`Successfully generated and auto-saved audio track: ${tempAudioFiles[0].name} to "${projectContext.projectName}".`);
          } catch (autoSaveError) {
            console.error('❌ Failed to auto-save audio:', autoSaveError);
            setStatusMessage(`Successfully generated audio track: ${tempAudioFiles[0].name}. (Auto-save failed - you can save manually)`);
          } finally {
            setIsAutoSaving(false);
          }
          }
        } else {
          console.log(`⚠️ Auto-save skipped - not in loaded project mode or conditions not met`);
          if (projectContext.mode === 'new') {
            setStatusMessage(`Successfully generated audio track: ${tempAudioFiles[0].name}. Use "Save Project" to save your work.`);
          }
        }
      } else {
         setStatusMessage('Could not generate any audio tracks.');
         if(!error && collectedChunks.length > 0) setError('Failed to produce audio output from processed chunks.');
         else if (!error) setError('Failed to produce any audio output.');
      }

    } catch (e: any) {
      console.error("Error generating speech or processing audio:", e);
      let errorMessage = `Speech Generation Error: ${e.message || 'An unknown error occurred'}`;
      let isRetryable = false;

      if (e.name === 'EncodingError') {
          errorMessage = `Audio Encoding Error: One of the audio chunks could not be decoded. Ensure the input script is valid. ${e.message}`;
      } else if (e.message && e.message.includes('INVALID_ARGUMENT')) {
        // Check if it's a voice name error
        if (e.message.includes('Voice name is not supported') || e.message.includes('voice')) {
          errorMessage = "⚠️ Voice Configuration Error: The selected voice is not supported. Please try selecting a different voice from the dropdown.";

          // Auto-fix: Reset current voices to default values
          setCurrentVoice1(AVAILABLE_VOICES[0]?.apiName || 'zephyr');
          setCurrentVoice2(AVAILABLE_VOICES[1]?.apiName || 'puck');
        } else {
          errorMessage += " This might be due to incorrect request parameters, model compatibility issues, or unsupported input text structure.";
        }
      } else if (e.code === 429 || (e.message && e.message.includes('429'))) {
        // Handle API quota/rate limit errors
        errorMessage = "⚠️ API Rate Limit Exceeded: You've hit your API quota limit. Please try again later or switch to a different model (e.g., Gemini Flash TTS instead of Pro TTS).";
        isRetryable = true;
      } else if (e.message && e.message.includes('quota')) {
        // Handle quota-specific errors
        errorMessage = "⚠️ API Quota Exceeded: You've reached your API usage limit. Please check your billing details or try a different model with lower resource requirements.";
        isRetryable = true;
      } else if (e.message && e.message.includes('RESOURCE_EXHAUSTED')) {
        // Handle resource exhausted errors
        errorMessage = "⚠️ API Resources Exhausted: The API service is temporarily overloaded. Please wait a moment and try again, or switch to a different model.";
        isRetryable = true;
      } else if (e.response && typeof e.response.json === 'function') {
        try {
            const errorDetails = await e.response.json();
            if (errorDetails.error && errorDetails.error.message) {
                 errorMessage = `Speech Generation Error: ${errorDetails.error.message}`;

                 // Check for quota/rate limit in the detailed error message
                 if (errorDetails.error.message.includes('quota') ||
                     errorDetails.error.message.includes('rate limit') ||
                     errorDetails.error.code === 429) {
                   errorMessage = "⚠️ API Quota/Rate Limit: " + errorDetails.error.message + " Try switching to Gemini Flash TTS for lower resource usage.";
                   isRetryable = true;
                 }
            }
        } catch (parseError) { /* Ignore */ }
      } else if (e.message && e.message.includes("got status: 400 INVALID_ARGUMENT")) {
        const match = e.message.match(/\{[\s\S]*\}/);
        if (match && match[0]) {
            try {
                const errorJson = JSON.parse(match[0]);
                if (errorJson.error && errorJson.error.message) {
                    errorMessage = `Speech Generation Error: ${errorJson.error.message}`;
                }
            } catch (jsonErr) { /* Ignore */ }
        }
      }

      // Add helpful suggestions for retryable errors
      if (isRetryable) {
        errorMessage += "\n\n💡 Suggestions:\n• Wait a few minutes before trying again\n• Switch to 'Gemini Flash TTS' model for lower resource usage\n• Check your Google AI Studio quota and billing";
      }

      setError(errorMessage);
      setStatusMessage('');
    } finally {
      setIsLoading(false);
      setProject(prev => ({ ...prev, isGeneratingAudio: false }));
    }
  }, [inputText, aiClient, project.projectName, project.synthesisMode, project.selectedTtsModel, project.defaultVoice1, project.defaultVoice2, currentVoice1, currentVoice2, setStatusMessage, error, subscriptionStatus.isActive]);

  // Cleanup audio URLs when component unmounts
  const blobUrlsRef = useRef<Set<string>>(new Set());
  
  // Track new blob URLs as they're added
  useEffect(() => {
    project.audioFiles.forEach(file => {
      if (file.url && file.url.startsWith('blob:')) {
        blobUrlsRef.current.add(file.url);
      }
    });
  }, [project.audioFiles]);
  
  useEffect(() => {
    return () => {
      // Only cleanup on component unmount
      blobUrlsRef.current.forEach(url => {
        URL.revokeObjectURL(url);
      });
      blobUrlsRef.current.clear();
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Handle project loaded event
  const handleProjectLoaded = (loadedData: any) => {
    console.log('🔄 handleProjectLoaded called with:', loadedData);
    
    // Extract configuration data from the nested structure
    const config = loadedData?.configuration || {};
    const metadata = loadedData?.metadata || {};
    
    // Ensure all string fields have default values to prevent .trim() errors
    const safeLoadedData = {
      id: loadedData?.id || generateProjectId(),
      projectName: config?.projectName || metadata?.projectName || '',
      topic: config?.topic || '',
      scriptLinks: config?.scriptLinks || '',
      synthesisMode: config?.synthesisMode || 'podcast',
      selectedScriptModel: config?.selectedScriptModel || AVAILABLE_SCRIPT_MODELS[0]?.apiName || 'gemini-2.5-flash-lite',
      selectedTtsModel: config?.selectedTtsModel || AVAILABLE_TTS_MODELS[0]?.apiName || 'elevenlabs',
      voice1: config?.voice1 || config?.defaultVoice1 || AVAILABLE_VOICES[0]?.apiName || 'zephyr',
      voice2: config?.voice2 || config?.defaultVoice2 || AVAILABLE_VOICES[1]?.apiName || 'puck',
      speaker1Name: config?.speaker1Name || '',
      speaker2Name: config?.speaker2Name || '',
      projectStyle: config?.projectStyle || '',
      wordCount: config?.wordCount || 500,
      audioFiles: loadedData?.audioFiles || [], // Use audio files directly from loadedData
      inputText: config?.inputText || config?.script || '',
    };
    console.log('✅ Safe loaded data:', safeLoadedData);

    // Clear any previous audio files first to ensure clean state
    setProject(prev => ({
      ...prev,
      id: safeLoadedData.id,
      projectName: safeLoadedData.projectName,
      topic: safeLoadedData.topic,
      scriptLinks: safeLoadedData.scriptLinks,
      synthesisMode: safeLoadedData.synthesisMode,
      selectedScriptModel: safeLoadedData.selectedScriptModel,
      selectedTtsModel: safeLoadedData.selectedTtsModel,
      defaultVoice1: safeLoadedData.voice1,
      defaultVoice2: safeLoadedData.voice2,
      speaker1Name: safeLoadedData.speaker1Name,
      speaker2Name: safeLoadedData.speaker2Name,
      projectStyle: safeLoadedData.projectStyle,
      wordCount: safeLoadedData.wordCount,
      audioFiles: [], // Clear first
      script: safeLoadedData.inputText, // Keep script in sync
    }));

    // Then set the loaded audio files after a brief delay to ensure clean state
    setTimeout(() => {
      setProject(prev => ({
        ...prev,
        audioFiles: safeLoadedData.audioFiles, // Now set the loaded audio files
      }));
      console.log(`🎵 Loaded ${safeLoadedData.audioFiles.length} audio files for project: ${safeLoadedData.projectName}`);
    }, 100);

    setInputText(safeLoadedData.inputText);

    loadProject(safeLoadedData.id, safeLoadedData.projectName);
    setPickerOpen(false);
  };

  const handleNewProject = async (setupData?: {
    projectName: string;
    synthesisMode: SynthesisMode;
    speaker1Name: string;
    speaker2Name: string;
    projectStyle: string;
    voice1: string;
    voice2: string;
  }) => {
    console.log('🎤 handleNewProject called with setupData:', setupData);
    // Create the new project data
    const newProjectData = {
      id: generateProjectId(),
      projectName: setupData?.projectName || 'New Project',
      speaker1Name: setupData?.speaker1Name || getRandomItem(RANDOM_PERSONA_NAMES),
      speaker2Name: setupData?.speaker2Name || getRandomItem(RANDOM_PERSONA_NAMES),
      projectStyle: setupData?.projectStyle || getRandomItem(RANDOM_PODCAST_STYLES),
      topic: getRandomItem(RANDOM_PODCAST_TOPICS),
      scriptLinks: '',
      wordCount: 500,
      synthesisMode: setupData?.synthesisMode || 'podcast',
      selectedScriptModel: AVAILABLE_SCRIPT_MODELS.find(m => m.apiName.includes('gemini-2.5-flash-lite'))?.apiName || AVAILABLE_SCRIPT_MODELS[0]?.apiName || 'gemini-2.5-flash-lite',
      selectedTtsModel: AVAILABLE_TTS_MODELS[0].apiName, // Use Flash TTS as default (now index 0)
      defaultVoice1: setupData?.voice1 || AVAILABLE_VOICES[0]?.apiName || 'zephyr',
      defaultVoice2: setupData?.voice2 || AVAILABLE_VOICES[1]?.apiName || 'puck',
      script: '',
      audioFiles: [], // Clear audio files from previous project
      isGeneratingScript: false,
      isGeneratingAudio: false,
    };

    console.log('🎤 New project data created:', {
      setupDataVoice1: setupData?.voice1,
      setupDataVoice2: setupData?.voice2,
      defaultVoice1: newProjectData.defaultVoice1,
      defaultVoice2: newProjectData.defaultVoice2,
      preferredDefaults: preferredDefaultVoiceApiNames,
      availableVoices: AVAILABLE_VOICES.slice(0, 3).map(v => v.apiName)
    });

    // Set the project state
    setProject(newProjectData);
    setInputText('');
    // Set current voice settings to match the project defaults from setup data
    setCurrentVoice1(newProjectData.defaultVoice1);
    setCurrentVoice2(newProjectData.defaultVoice2);

    // If setupData is provided (from modal), automatically save the project
    if (setupData && isSignedIn && user && separatedApiClient) {
      try {
        console.log('💾 Auto-saving new project from modal...');
        
        // Create project configuration
        const projectData = createProjectConfig(
          newProjectData.projectName,
          newProjectData.topic,
          newProjectData.scriptLinks,
          newProjectData.synthesisMode,
          newProjectData.selectedScriptModel,
          newProjectData.selectedTtsModel,
          newProjectData.defaultVoice1,
          newProjectData.defaultVoice2,
          newProjectData.speaker1Name,
          newProjectData.speaker2Name,
          newProjectData.projectStyle,
          newProjectData.wordCount,
          user.id
        );

        projectData.metadata.description = `Project: ${newProjectData.projectName}`;

        // Save the project
        const projectResult = await separatedApiClient.saveProject(projectData);
        const savedProjectId = projectResult.data.projectId;

        // Switch to loaded mode with auto-save enabled
        setProjectContext({
          mode: 'loaded',
          projectId: savedProjectId,
          projectName: newProjectData.projectName,
          isAutoSaveEnabled: true,
          lastSaved: new Date().toISOString(),
        });

        console.log(`✅ Auto-saved new project: ${newProjectData.projectName} (${savedProjectId})`);
        showModal('Project Created', `Your project "${newProjectData.projectName}" has been created and saved.`, 'success');
        
      } catch (error) {
        console.error('❌ Failed to auto-save new project:', error);
        // Fall back to manual save mode
        startNewProject();
        showModal('Project Created', `Your project "${newProjectData.projectName}" has been created. Note: Auto-save failed, you'll need to save manually.`, 'warning');
      }
    } else {
      // No setupData (not from modal) or not signed in - use manual save mode
      startNewProject();
      console.log('🆕 New project started - cleared audio files and reset voices');
    }
  };

  // Resolve active project on mount: URL ?project=, localStorage lastProjectId, else show picker
  useEffect(() => {
    try {
      const params = new URLSearchParams(window.location.search);
      const urlProject = params.get('project');
      if (urlProject) {
        // let user load via internal flow (we don't fetch here)
        setPickerOpen(false);
        // store hint for later resume
        localStorage.setItem('ww_lastProjectId', urlProject);
        return;
      }

      const lastId = localStorage.getItem('ww_lastProjectId');
      const lastName = localStorage.getItem('ww_lastProjectName');
      if (!lastId) {
        setPickerOpen(true);
      } else {
        // Non-blocking: show picker with Resume option; user can dismiss
        setPickerOpen(true);
      }
    } catch { /* noop */ }
  }, []);

  // When project is loaded or saved, persist last project hints
  useEffect(() => {
    if (projectContext.mode === 'loaded' && projectContext.projectId) {
      localStorage.setItem('ww_lastProjectId', projectContext.projectId);
      localStorage.setItem('ww_lastProjectName', projectContext.projectName || 'Project');
    }
  }, [projectContext.mode, projectContext.projectId, projectContext.projectName]);

  // Helper to open internal Load dialog of ProjectSaveLoad
  const openLoadProjects = useCallback(() => {
    setOpenLoadDialog(true);
  }, []);

  return (
    <div className="relative min-h-screen bg-slate-900 overflow-hidden">
      {/* Noise Background */}
      <Noise
        patternSize={250}
        patternScaleX={1}
        patternScaleY={1}
        patternRefreshInterval={2}
        patternAlpha={20}
      />

      {/* Waves Background */}
      <Waves
        lineColor="#1e293b"
        backgroundColor="transparent"
        waveSpeedX={0.02}
        waveSpeedY={0.01}
        waveAmpX={40}
        waveAmpY={20}
        friction={0.9}
        tension={0.01}
        maxCursorMove={120}
        xGap={12}
        yGap={36}
      />

      {/* Content */}
      <div className="relative z-10 min-h-screen p-8 text-white">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:justify-center md:items-center mb-16 pt-8 gap-6">
          {/* Title - Centered on mobile, left-aligned in desktop row */}
          <div className="text-center md:text-left order-2 md:order-1">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-white brightness-125 mb-4 font-fredoka">
              <ShinyText text="WordWave Studio" />
            </h1>
          </div>
          
          {/* Navigation - Centered on mobile, to the right of title on desktop */}
          <div className="flex items-center gap-4 justify-center order-1 md:order-2 md:ml-6">
            <a
              href="/landing"
              className="flex items-center justify-center w-10 h-10 bg-slate-800/60 hover:bg-slate-700/60 text-white rounded-full border border-slate-600/40 hover:border-slate-500/60 transition-all duration-200 group"
              title="Go to Landing Page"
            >
              <svg 
                className="w-5 h-5 text-slate-300 group-hover:text-white transition-colors" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
                />
              </svg>
            </a>
            {isSignedIn && <AccountMenu />}
          </div>
        </div>

        {/* Authentication Section */}
        <div className="max-w-4xl mx-auto mb-12">
          <AuthSection />
        </div>

        {!isSignedIn && (
          <div className="text-center text-gray-400 py-16">
            <p>Please sign in to start creating AI-powered audio content.</p>
          </div>
        )}

        {isSignedIn && (
          <>
            {/* Project Status and Mode Selection - Single Column Layout */}
            <div className="max-w-6xl mx-auto mb-12">
              <div className="grid grid-cols-1 gap-8">
                {/* Project Status Indicator */}
                <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
                  <ProjectStatusIndicator
                    projectContext={projectContext}
                    isAutoSaving={isAutoSaving}
                  />
                </div>
              </div>
            </div>

            {/* Main Content Cards */}
            
            {/* Card 1: API Configuration - Single row */}
            <div className="max-w-6xl mx-auto mb-12">
              <ApiKeyCard />
            </div>

            {/* Cards 2-3: Project Setup and Script Generator - Two column layout */}
            <div className="max-w-6xl mx-auto mb-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Project Setup Card */}
              <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 flex items-center justify-center text-2xl">
                    <span role="img" aria-label="setup">⚙️</span>
                  </div>
                  <h2 className="text-xl font-semibold text-white brightness-125 font-fredoka"><ShinyText text="2. Project Setup" /></h2>
                </div>

                <div className="space-y-4">
                  {/* Switch/New Project Button - Always visible */}
                  <button
                    type="button"
                    onClick={() => setPickerOpen(true)}
                    className="w-full bg-green-600/20 hover:bg-green-600/30 border border-green-500/30 rounded-lg p-3 text-left transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <span role="img" aria-label="new">✨</span>
                      <span className="text-green-400">
                        {projectContext.mode === 'loaded' ? 'Switch/New Project' : 'Load/New Project'}
                      </span>
                    </div>
                  </button>

                  {/* Project Type Selection - Moved from main section */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Project Type:</label>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-8">
                      <div className="flex items-center gap-3">
                        <input
                          type="radio"
                          id="podcast-setup"
                          name="content-type-setup"
                          className="w-4 h-4 text-blue-500"
                          checked={project.synthesisMode === 'podcast'}
                          onChange={() => setProject(prev => ({ ...prev, synthesisMode: 'podcast' }))}
                        />
                        <label htmlFor="podcast-setup" className="text-white flex items-center gap-2">
                          <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                          Podcast (2 Speakers)
                        </label>
                      </div>
                      <div className="flex items-center gap-3">
                        <input
                          type="radio"
                          id="monologue-setup"
                          name="content-type-setup"
                          className="w-4 h-4 text-blue-500"
                          checked={project.synthesisMode === 'monologue'}
                          onChange={() => setProject(prev => ({ ...prev, synthesisMode: 'monologue' }))}
                        />
                        <label htmlFor="monologue-setup" className="text-white flex items-center gap-2">
                          <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          Monologue (1 Speaker)
                        </label>
                      </div>
                    </div>
                  </div>

                  <ProjectSaveLoad 
                    projectName={project.projectName}
                    topic={project.topic}
                    scriptLinks={project.scriptLinks}
                    synthesisMode={project.synthesisMode}
                    selectedScriptModel={project.selectedScriptModel}
                    selectedTtsModel={project.selectedTtsModel}
                    voice1={project.defaultVoice1}
                    voice2={project.defaultVoice2}
                    speaker1Name={project.speaker1Name}
                    speaker2Name={project.speaker2Name}
                    projectStyle={project.projectStyle}
                    wordCount={project.wordCount}
                    inputText={inputText}
                    audioFiles={project.audioFiles}
                    onProjectLoaded={handleProjectLoaded}
                    onProjectSaved={(id, name) => {
                      loadProject(id, name);
                      setPickerOpen(false);
                    }}
                    onError={(errorMessage) => showModal('Save/Load Error', errorMessage, 'error')}
                    onSuccess={(message) => showModal('Success', message, 'success')}
                    projectId={projectContext.projectId} // <-- Pass the project ID from context
                    openLoadDialogExternal={openLoadDialog}
                    onLoadDialogClosed={() => setOpenLoadDialog(false)}
                    showLoadButton={false} // Hide the Load Project button since it's handled by ProjectPickerModal
                  />

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      🔴 Project Name (used for filename):
                      {projectContext.mode === 'loaded' && (
                        <span className="text-blue-400 text-xs ml-2">(Loaded Project)</span>
                      )}
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={project.projectName || ''}
                        onChange={(e) => {
                          setProject(prev => ({ ...prev, projectName: e.target.value }));
                          // Refresh project list when user types to check for duplicates
                          if (e.target.value.trim()) {
                            loadExistingProjects();
                          }
                        }}
                        disabled={projectContext.mode === 'loaded'}
                        className={`w-full border rounded-lg px-3 py-2 text-white placeholder-gray-400 ${
                          projectContext.mode === 'loaded'
                            ? 'bg-slate-800/50 border-slate-700 cursor-not-allowed opacity-75'
                            : isProjectNameTaken(project.projectName)
                            ? 'bg-slate-700/50 border-red-500 focus:border-red-400'
                            : 'bg-slate-700/50 border-slate-600 focus:border-blue-500'
                        }`}
                        placeholder="My Awesome Project"
                      />
                      {projectContext.mode !== 'loaded' && isProjectNameTaken(project.projectName) && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <span className="text-red-400 text-sm" title="Project name already exists">⚠️</span>
                        </div>
                      )}
                    </div>
                    {projectContext.mode !== 'loaded' && isProjectNameTaken(project.projectName) && (
                      <p className="text-red-400 text-xs mt-1">
                        ⚠️ A project with this name already exists. Please choose a different name.
                      </p>
                    )}
                  </div>

                  {project.synthesisMode === 'podcast' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          🟣 Speaker 1 Persona Name:
                        </label>
                        <input
                          type="text"
                          value={project.speaker1Name || ''}
                          onChange={(e) => setProject(prev => ({ ...prev, speaker1Name: e.target.value }))}
                          className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                          placeholder="Host"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          🟣 Speaker 2 Persona Name:
                        </label>
                        <input
                          type="text"
                          value={project.speaker2Name || ''}
                          onChange={(e) => setProject(prev => ({ ...prev, speaker2Name: e.target.value }))}
                          className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                          placeholder="Co-host"
                        />
                      </div>
                    </>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      🔴 Project Style:
                    </label>
                    <textarea
                      value={project.projectStyle || ''}
                      onChange={(e) => setProject(prev => ({ ...prev, projectStyle: e.target.value }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white resize-vertical"
                      placeholder="Describe your project style... (e.g., Casual conversation, investigative journalism, comedy sketch, educational deep-dive, etc.)"
                      rows={3}
                      aria-label="Project Style"
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      💡 Tip: Be specific about tone, format, and approach. This guides the AI in generating content that matches your vision.
                    </p>
                  </div>

                  {/* Voice Selection - Moved from Card #4 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      🎤 Voice Configuration:
                    </label>
                    
                    {/* Voice Samples Link */}
                    <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-400">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                        <span className="text-sm font-medium">🎧 Listen to Voice Samples</span>
                      </div>
                      <p className="text-xs text-gray-400 mt-1">
                        Check out our <a 
                          href="https://www.youtube.com/@WordWaveStudio/videos" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline"
                        >
                          YouTube channel
                        </a> to hear examples of all available voices in action!
                      </p>
                    </div>

                    <div className="voice-selection-grid grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-slate-900/50 p-3 rounded-lg border border-slate-700">
                        <h3 className="text-lg font-semibold mb-2 text-gray-200">Project Default Voices</h3>
                        <p className="text-xs text-gray-400 mb-2">Default voices for this project.</p>
                        <div className="form-group">
                          <label htmlFor="default-voice-1" className="block text-sm font-medium text-gray-300 mb-1">Default Voice for Speaker 1:</label>
                          <select
                            id="default-voice-1"
                            value={project.defaultVoice1}
                            onChange={(e) => setProject(prev => ({ ...prev, defaultVoice1: e.target.value }))}
                            className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                          >
                            {AVAILABLE_VOICES.map(voice => (
                              <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                            ))}
                          </select>
                        </div>
                        {project.synthesisMode === 'podcast' && (
                          <div className="form-group">
                            <label htmlFor="default-voice-2" className="block text-sm font-medium text-gray-300 mb-1">Default Voice for Speaker 2:</label>
                            <select
                              id="default-voice-2"
                              value={project.defaultVoice2}
                              onChange={(e) => setProject(prev => ({ ...prev, defaultVoice2: e.target.value }))}
                              className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                            >
                              {AVAILABLE_VOICES.map(voice => (
                                <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                      </div>

                      <div className="bg-slate-900/50 p-3 rounded-lg border border-slate-700">
                        <h3 className="text-lg font-semibold mb-2 text-gray-200">Current Generation</h3>
                        <p className="text-xs text-gray-400 mb-2">Override voices for this generation.</p>
                        <div className="form-group">
                          <label htmlFor="current-voice-1" className="block text-sm font-medium text-gray-300 mb-1">Voice for Speaker 1:</label>
                          <select
                            id="current-voice-1"
                            value={currentVoice1}
                            onChange={(e) => setCurrentVoice1(e.target.value)}
                            className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                          >
                            {AVAILABLE_VOICES.map(voice => (
                              <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                            ))}
                          </select>
                        </div>
                        {project.synthesisMode === 'podcast' && (
                          <div className="form-group">
                            <label htmlFor="current-voice-2" className="block text-sm font-medium text-gray-300 mb-1">Voice for Speaker 2:</label>
                            <select
                              id="current-voice-2"
                              value={currentVoice2}
                              onChange={(e) => setCurrentVoice2(e.target.value)}
                              className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                            >
                              {AVAILABLE_VOICES.map(voice => (
                                <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        <button
                          type="button"
                          onClick={() => {
                            setCurrentVoice1(project.defaultVoice1);
                            setCurrentVoice2(project.defaultVoice2);
                          }}
                          className="w-full text-xs mt-2 p-2 rounded-md bg-slate-700 hover:bg-slate-600 transition-colors"
                        >
                          🔄 Reset to Project Defaults
                        </button>
                      </div>
                    </div>
                  </div>

                </div>
              </div>

              {/* Script Generator Card */}
              <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 flex items-center justify-center text-2xl">
                    <span role="img" aria-label="script">📝</span>
                  </div>
                  <h2 className="text-xl font-semibold text-white brightness-125 font-fredoka"><ShinyText text="3. Script Generator" /></h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Script Model:
                    </label>
                    <select
                      value={project.selectedScriptModel}
                      onChange={(e) => setProject(prev => ({ ...prev, selectedScriptModel: e.target.value }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                      aria-label="Script Model"
                    >
                      {AVAILABLE_SCRIPT_MODELS.map((model) => (
                        <option key={model.apiName} value={model.apiName}>
                          {model.apiName}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-400 mt-1">
                      Using model for script: {AVAILABLE_SCRIPT_MODELS.find(m => m.apiName === project.selectedScriptModel)?.apiName}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      💡 Script Topic:
                    </label>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <input
                        type="text"
                        value={project.topic || ''}
                        onChange={(e) => setProject(prev => ({ ...prev, topic: e.target.value }))}
                        className="flex-1 bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                        placeholder="e.g., The Future of Renewable Energy"
                      />
                      <button
                        type="button"
                        onClick={randomizeTopic}
                        className="bg-slate-700/50 hover:bg-slate-700 rounded-lg px-3 py-2 text-blue-400 transition-colors whitespace-nowrap sm:flex-shrink-0"
                      >
                        <div className="flex items-center justify-center gap-2">
                          <span role="img" aria-label="random">🎲</span>
                          <span className="hidden xs:inline">Random Topic</span>
                          <span className="xs:hidden">Random</span>
                        </div>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      🔗 Relevant Links (one per line, optional):
                    </label>
                    <textarea
                      value={project.scriptLinks || ''}
                      onChange={(e) => setProject(prev => ({ ...prev, scriptLinks: e.target.value }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 h-20 resize-none"
                      placeholder="e.g., https://example.com/article1&#10;https://example.com/study2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      🎯 Target Word Count:
                    </label>
                    <input
                      type="number"
                      value={project.wordCount || 500}
                      onChange={(e) => setProject(prev => ({ ...prev, wordCount: parseInt(e.target.value, 10) || 500 }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                      placeholder="500"
                      min="50"
                      max="2000"
                      step="50"
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      💡 Tip: Typical podcast segments are 300-800 words. Longer scripts may take more time to generate and synthesize.
                    </p>
                  </div>

                  {/* Subscription Status & Generate Button */}
                  {!subscriptionStatus.isActive ? (
                    <div className="space-y-4">
                      <div className="bg-amber-500/20 border border-amber-500/30 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-amber-400">⚠️</span>
                          <h3 className="text-amber-300 font-semibold">Subscription Required</h3>
                        </div>
                        <p className="text-amber-100 text-sm mb-3">
                          You need an active subscription to generate scripts and audio content.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-2 items-center">
                          <p className="text-amber-200 text-sm">
                            Click your profile → Subscription to subscribe or sync status
                          </p>
                          <button
                            onClick={syncSubscriptionStatus}
                            className="text-xs bg-slate-600 hover:bg-slate-500 text-white px-3 py-1 rounded transition-colors whitespace-nowrap"
                            title="If you just completed a payment, click here to refresh your subscription status"
                          >
                            🔄 Sync Status
                          </button>
                        </div>
                      </div>
                      <button
                        type="button"
                        disabled={true}
                        className="w-full bg-gray-600 rounded-lg py-3 text-gray-300 font-medium cursor-not-allowed"
                      >
                        Generate Script (Subscription Required)
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                        <p className="text-purple-200 text-sm">
                          🎭 <strong>Smart Voice Effects:</strong> Our AI will automatically include vocal effects and emotions in your generated script for more engaging audio!
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={handleGenerateScript}
                        disabled={project.isGeneratingScript || !apiKey}
                        className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg py-3 text-white font-medium transition-colors"
                      >
                        {project.isGeneratingScript ? 'Generating Script...' : 'Generate Script'}
                      </button>
                    </div>
                  )}

                  {/* Script Editor - Moved from Card #3 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      📜 Script Editor:
                    </label>
                    {/* Match the voice configuration grid structure for consistent visual width */}
                    <div className="script-editor-grid grid grid-cols-1 md:grid-cols-1 gap-4">
                      <div className="bg-slate-900/50 p-3 rounded-lg border border-slate-700">
                        <h3 className="text-lg font-semibold mb-2 text-gray-200">
                          {project.synthesisMode === 'podcast' ? 'Script (2 Speakers)' : 'Script (1 Speaker)'}
                        </h3>
                        <div className="mb-3">
                          <p className="text-sm text-gray-300 mb-1">
                            💡 <strong>You can paste your own script here!</strong>
                          </p>
                          <p className="text-xs text-gray-400">
                            {project.synthesisMode === 'podcast' 
                              ? 'Format: Each line should start with "Speaker 1:" or "Speaker 2:" followed by their dialogue.'
                              : 'Just paste or type your monologue text below.'
                            }
                          </p>
                        </div>

                        {/* Sound Effects & Vocal Tips - Expandable Section */}
                        <ScriptEffectsGuide synthesisMode={project.synthesisMode} />
                        {project.synthesisMode === 'podcast' && (
                          <div className="bg-blue-950/30 border border-blue-700/30 rounded-lg p-3 mb-3">
                            <div className="flex items-start gap-2">
                              <span className="text-blue-400 text-sm">📝</span>
                              <div className="text-xs text-blue-200">
                                <strong>Quick Format Guide:</strong>
                                <div className="mt-1 font-mono text-blue-100 bg-blue-950/50 p-2 rounded border">
                                  Speaker 1: Hello and welcome!<br/>
                                  Speaker 2: Thanks for having me.<br/>
                                  Speaker 1: Let's get started...
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        <textarea
                          value={inputText || ''}
                          onChange={(e) => setInputText(e.target.value)}
                          className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 h-48 resize-vertical"
                          placeholder={project.synthesisMode === 'podcast'
                            ? "Paste your script here! Format:\n\nSpeaker 1: Welcome to our podcast!\nSpeaker 2: Thanks for having me!\nSpeaker 1: Let's dive into today's topic...\nSpeaker 2: Absolutely, I'm excited to discuss...\n\n(Or use the script generator above)"
                            : "Paste or type your monologue script here...\n\n(Or use the script generator above)"}
                          disabled={isLoading || project.isGeneratingScript}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              </div>
            </div>

            {/* Card 4: Text-to-Speech Synthesizer */}
            <div className="max-w-6xl mx-auto mt-12">
              <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 flex items-center justify-center text-2xl">
                    <span role="img" aria-label="tts">🔊</span>
                  </div>
                  <h2 className="text-xl font-semibold text-white brightness-125 font-fredoka"><ShinyText text="4. Text-to-Speech Synthesizer" /></h2>
                </div>

                <div className="space-y-4">
                  {/* TTS Model Selection */}
                  <div className="form-group">
                    <label htmlFor="tts-model" className="block text-sm font-medium text-gray-300 mb-2">TTS Model:</label>
                    <select
                      id="tts-model"
                      value={project.selectedTtsModel}
                      onChange={(e) => setProject(prev => ({ ...prev, selectedTtsModel: e.target.value }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                    >
                      {AVAILABLE_TTS_MODELS.map(model => (
                        <option key={model.apiName} value={model.apiName}>
                          {model.apiName}
                        </option>
                      ))}
                    </select>
                    <div className="text-xs text-gray-400 mt-2 p-2 bg-slate-700/20 rounded-md">
                      <p><strong>Model Comparison:</strong></p>
                      <ul className="list-disc list-inside pl-2">
                        <li><strong>Flash TTS</strong>: Lower resource usage, faster processing, ideal for testing.</li>
                        <li><strong>Pro TTS</strong>: Higher quality, more resource-intensive, <span className="text-yellow-300">requires Tier 1 (paid) access</span>.</li>
                      </ul>
                      <p className="mt-2 text-purple-300"><strong>✨ Both models support:</strong> Natural voice effects, emotions, multi-speaker conversations, and 24 languages!</p>
                    </div>
                  </div>

                  {/* Generate Speech Button with Subscription Check */}
                  {!subscriptionStatus.isActive ? (
                    <div className="space-y-3">
                      <div className="bg-amber-500/20 border border-amber-500/30 rounded-lg p-3">
                        <p className="text-amber-100 text-sm">
                          <span className="text-amber-400">⚠️</span> Subscription required to generate audio. Check your profile menu to subscribe.
                        </p>
                      </div>
                      <button
                        type="button"
                        disabled={true}
                        className="w-full bg-gray-600 rounded-lg py-3 text-gray-300 font-medium cursor-not-allowed"
                      >
                        Generate Speech (Subscription Required)
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <button
                        type="button"
                        onClick={handleGenerateSpeech}
                        disabled={!inputText.trim() || isLoading || project.isGeneratingScript || project.isGeneratingAudio}
                        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg py-3 text-white font-medium transition-colors"
                      >
                        {isLoading || project.isGeneratingAudio ? 'Generating Speech...' : 'Generate Speech'}
                      </button>
                    </div>
                  )}

                  {statusMessage && (
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mt-4">
                      <p className="text-blue-400 text-sm">{statusMessage}</p>
                    </div>
                  )}

                  {error && (
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mt-4">
                      <div className="flex justify-between items-start gap-3">
                        <p className="text-red-400 text-sm whitespace-pre-line flex-1">{error}</p>
                        <button
                          type="button"
                          onClick={() => setError(null)}
                          className="text-red-400 hover:text-red-300 text-lg leading-none"
                          title="Clear error"
                        >
                          ×
                        </button>
                      </div>
                      {/* Show quick fix buttons for common errors */}
                      {(error.includes('quota') || error.includes('rate limit') || error.includes('429')) &&
                       project.selectedTtsModel === 'gemini-2.5-pro-preview-tts' && (
                        <button
                          type="button"
                          onClick={() => {
                            setProject(prev => ({ ...prev, selectedTtsModel: 'gemini-2.5-flash-preview-tts' }));
                            setError(null);
                          }}
                          className="mt-3 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded transition-colors"
                        >
                          🚀 Switch to Flash TTS (Lower Resource Usage)
                        </button>
                      )}
                      {(error.includes('No audio data received') || error.includes('Audio Generation Failed')) && (
                        <div className="mt-3 flex gap-2 flex-wrap">
                          <button
                            type="button"
                            onClick={() => {
                              setError(null);
                              handleGenerateSpeech();
                            }}
                            className="bg-purple-600 hover:bg-purple-700 text-white text-xs px-3 py-1 rounded transition-colors"
                            disabled={isLoading}
                          >
                            🔄 Retry Generation
                          </button>
                          {project.selectedTtsModel === 'gemini-2.5-pro-preview-tts' && (
                            <button
                              type="button"
                              onClick={() => {
                                setProject(prev => ({ ...prev, selectedTtsModel: 'gemini-2.5-flash-preview-tts' }));
                                setError(null);
                              }}
                              className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded transition-colors"
                            >
                              🚀 Try Flash TTS
                            </button>
                          )}
                          {inputText.length > 1000 && (
                            <button
                              type="button"
                              onClick={() => {
                                const shortText = inputText.split('\n').slice(0, 2).join('\n');
                                setInputText(shortText);
                                setError(null);
                              }}
                              className="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded transition-colors"
                            >
                              ✂️ Use Shorter Text
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Card 5: Audio Gallery */}
            <div className="max-w-6xl mx-auto mt-12">
              <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 flex items-center justify-center text-2xl">
                    <span role="img" aria-label="gallery">🖼️</span>
                  </div>
                  <h2 className="text-xl font-semibold text-white brightness-125 font-fredoka"><ShinyText text="5. Audio Gallery" /></h2>
                </div>
                <AudioGallery
                  audioFiles={project.audioFiles}
                  onDelete={async (fileId) => {
                    // Only delete from cloud storage if we have a loaded project
                    if (projectContext.mode === 'loaded' && projectContext.projectId) {
                      try {
                        console.log(`🗑️ Deleting audio ${fileId} from project ${projectContext.projectId}`);
                        await separatedApiClient.deleteAudio(projectContext.projectId, fileId);
                        console.log(`✅ Successfully deleted audio ${fileId} from cloud storage`);
                      } catch (error) {
                        console.error('❌ Failed to delete audio from cloud storage:', error);
                        // Show error to user but still remove from local state
                        showNotification(
                          'Failed to delete audio from cloud storage. The file has been removed locally but may still exist in storage.',
                          'warning',
                          8000
                        );
                      }
                    }

                    // Remove from local state regardless of cloud storage result
                    setProject(prev => ({
                      ...prev,
                      audioFiles: prev.audioFiles.filter(f => f.id !== fileId)
                    }));
                  }}
                  onLoadScript={handleLoadScript}
                />
                {project.audioFiles.length === 0 && (
                  <div className="gallery-placeholder text-center py-10 border-2 border-dashed border-slate-700 rounded-lg">
                    <p className="text-gray-400">Your generated audio will appear here.</p>
                  </div>
                )}
              </div>
            </div>


          </>
        )}

        {/* Project Picker gating modal */}
        <ProjectPickerModal
          isOpen={pickerOpen && isSignedIn}
          onClose={() => setPickerOpen(false)}
          onStartNew={handleNewProject}
          onLoadExisting={(projectData) => {
            handleProjectLoaded(projectData);
          }}
          onResumeLast={(() => {
            const lastId = typeof window !== 'undefined' ? localStorage.getItem('ww_lastProjectId') : null;
            const lastName = typeof window !== 'undefined' ? localStorage.getItem('ww_lastProjectName') : null;
            if (!lastId || !lastName) return undefined;
            return () => {
              // Directly load the last project
              loadProject(lastId, lastName);
              setPickerOpen(false);
            };
          })()}
          lastProjectName={typeof window !== 'undefined' ? localStorage.getItem('ww_lastProjectName') : null}
        />

        {/* Custom Modal */}
        <Modal
          isOpen={modal.isOpen}
          onClose={closeModal}
          title={modal.title}
          message={modal.message}
          type={modal.type}
        />

        {/* Notification System */}
        {notification && (
          <Notification
            message={notification.message}
            type={notification.type}
            duration={notification.duration}
            onClose={clearNotification}
          />
        )}

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
