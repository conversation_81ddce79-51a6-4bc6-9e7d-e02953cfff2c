"use client";

import React, { useState } from 'react';
import { SynthesisMode } from '@/lib/types';

interface ScriptEffectsGuideProps {
  synthesisMode: SynthesisMode;
}

const ScriptEffectsGuide: React.FC<ScriptEffectsGuideProps> = ({ synthesisMode }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="mb-3">
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-sm font-medium text-purple-400 hover:text-purple-300 transition-colors mb-2"
      >
        <span className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`}>▶</span>
        <span>🎭 Add Voice Effects & Emotions</span>
        <span className="text-xs text-gray-500">(Click to expand)</span>
      </button>
      
      {isExpanded && (
        <div className="bg-slate-800/60 border border-purple-500/20 rounded-lg p-4 mb-3 space-y-3">
          <div className="text-sm text-purple-200">
            <h4 className="font-semibold mb-2 text-purple-300">🎤 Enhance Your Audio with Natural Voice Effects</h4>
            <p className="text-xs text-gray-300 mb-3">
              Our Google Gemini TTS system supports natural language voice effects. Use parenthetical cues to guide vocal delivery.
            </p>
          </div>

          {/* Basic Format Guide */}
          <div className="bg-slate-900/60 border border-slate-600/30 rounded-md p-3">
            <h5 className="text-sm font-medium text-green-400 mb-2">✅ Basic Format</h5>
            <div className="space-y-2">
              {synthesisMode === 'podcast' ? (
                <div className="font-mono text-xs text-green-200 bg-slate-950/50 p-2 rounded border">
                  Speaker 1: (excitedly) Welcome to our show!<br/>
                  Speaker 2: (whispering) I have a secret to share...<br/>
                  Speaker 1: (laughing) That's amazing!
                </div>
              ) : (
                <div className="font-mono text-xs text-green-200 bg-slate-950/50 p-2 rounded border">
                  (cheerfully) Welcome to today's episode! (pauses) Let me tell you something incredible. (whispering) This will change everything.
                </div>
              )}
            </div>
          </div>

          {/* Emotions & Tones */}
          <div className="bg-slate-900/60 border border-slate-600/30 rounded-md p-3">
            <h5 className="text-sm font-medium text-blue-400 mb-2">😊 Emotions & Tones</h5>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-1 text-xs">
              {[
                'excited', 'cheerful', 'happy', 'joyful',
                'sad', 'crying', 'angry', 'frustrated',
                'scared', 'nervous', 'calm', 'gentle',
                'sarcastic', 'skeptical', 'empathetic', 'bored',
                'tired', 'dreamy', 'wistful', 'serious'
              ].map(emotion => (
                <span key={emotion} className="text-blue-200 bg-blue-950/30 px-2 py-1 rounded">
                  ({emotion})
                </span>
              ))}
            </div>
          </div>

          {/* Vocal Effects */}
          <div className="bg-slate-900/60 border border-slate-600/30 rounded-md p-3">
            <h5 className="text-sm font-medium text-yellow-400 mb-2">🎵 Vocal Effects</h5>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-1 text-xs">
              {[
                'whispering', 'quietly', 'loudly', 'shouting',
                'booming', 'forcefully', 'quickly', 'slowly',
                'hesitantly', 'deliberately', 'rushed', 'drawling',
                'breathy', 'raspy voice', 'sing-song', 'monotone'
              ].map(effect => (
                <span key={effect} className="text-yellow-200 bg-yellow-950/30 px-2 py-1 rounded">
                  ({effect})
                </span>
              ))}
            </div>
          </div>

          {/* Sound Effects */}
          <div className="bg-slate-900/60 border border-slate-600/30 rounded-md p-3">
            <h5 className="text-sm font-medium text-red-400 mb-2">🔊 Sound Effects</h5>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-1 text-xs">
              {[
                'laughing', 'giggling', 'coughing', 'sighing vocally',
                'gasping', 'muttering', 'stammering', 'pauses',
                'brief pause', 'long pause', 'clears throat'
              ].map(sound => (
                <span key={sound} className="text-red-200 bg-red-950/30 px-2 py-1 rounded">
                  ({sound})
                </span>
              ))}
            </div>
          </div>

          {/* Pro Tips */}
          <div className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/20 rounded-md p-3">
            <h5 className="text-sm font-medium text-purple-300 mb-2">💡 Pro Tips</h5>
            <ul className="text-xs text-purple-100 space-y-1 list-disc list-inside">
              <li>Place effects at the start of dialogue: <code className="bg-slate-800 px-1 rounded">(excited) Hello there!</code></li>
              <li>Use multiple effects: <code className="bg-slate-800 px-1 rounded">(whispering quietly) It's a secret</code></li>
              <li>Mix emotions naturally throughout your script</li>
              <li>Effects work best with natural language - experiment to find what sounds best!</li>
              {synthesisMode === 'podcast' && (
                <li>Each speaker can have different emotional styles to create dynamic conversations</li>
              )}
            </ul>
          </div>

          {/* Quick Examples */}
          <div className="bg-slate-900/60 border border-slate-600/30 rounded-md p-3">
            <h5 className="text-sm font-medium text-orange-400 mb-2">🎯 Quick Examples</h5>
            <div className="space-y-2 text-xs">
              {synthesisMode === 'podcast' ? (
                <>
                  <div className="bg-slate-950/50 p-2 rounded">
                    <div className="text-orange-300 mb-1">Excited Conversation:</div>
                    <div className="font-mono text-orange-100">
                      Speaker 1: (excitedly) Did you hear the news?<br/>
                      Speaker 2: (curiously) No, what happened?<br/>
                      Speaker 1: (dramatically) You're not going to believe this!
                    </div>
                  </div>
                  <div className="bg-slate-950/50 p-2 rounded">
                    <div className="text-orange-300 mb-1">Mysterious Tone:</div>
                    <div className="font-mono text-orange-100">
                      Speaker 1: (whispering) I discovered something strange...<br/>
                      Speaker 2: (nervously) What kind of strange?<br/>
                      Speaker 1: (pauses) (seriously) The kind that changes everything.
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="bg-slate-950/50 p-2 rounded">
                    <div className="text-orange-300 mb-1">Engaging Monologue:</div>
                    <div className="font-mono text-orange-100">
                      (cheerfully) Welcome to today's episode! (pauses) Today, we're exploring something fascinating. (whispering) Something that might just blow your mind.
                    </div>
                  </div>
                  <div className="bg-slate-950/50 p-2 rounded">
                    <div className="text-orange-300 mb-1">Storytelling Style:</div>
                    <div className="font-mono text-orange-100">
                      (mysteriously) It was a dark and stormy night... (dramatically) when everything changed. (sighing vocally) If only I had known what was coming.
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="text-xs text-gray-400 border-t border-slate-600/30 pt-2">
            ℹ️ This feature uses Google Gemini 2.5 TTS natural language processing. Effects are processed automatically - no SSML markup needed!
          </div>
        </div>
      )}
    </div>
  );
};

export default ScriptEffectsGuide;
