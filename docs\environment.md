# Environment Configuration Guide

This guide explains how to configure environment variables for WordWave Studio.

## Quick Setup

1. **Copy the example file:**
   ```bash
   cp .env.local.example .env.local
   ```

2. **Fill in your API keys** (see sections below for where to get them)

3. **Start the development server:**
   ```bash
   npm run dev
   ```

## Required Environment Variables

### 🔐 Authentication (Clerk)

WordWave Studio uses Clerk for user authentication and management.

**Setup Steps:**
1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new application or select existing one
3. Go to "API Keys" section
4. Copy the keys to your `.env.local`:

```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
```

**Important:** The `NEXT_PUBLIC_` prefix makes the publishable key available to the client-side code.

### 🤖 AI Services (Google Gemini)

Used for script generation and text-to-speech synthesis.

**Setup Steps:**
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Add to your `.env.local`:

```env
GOOGLE_GEMINI_API_KEY=your_api_key_here
```

### ☁️ Cloud Storage (Backblaze B2)

Used for storing project files and audio content.

**Setup Steps:**
1. Create a [Backblaze B2](https://www.backblaze.com/b2/cloud-storage.html) account
2. Create a new bucket for your projects
3. Generate application keys with read/write permissions
4. Add to your `.env.local`:

```env
B2_APPLICATION_KEY_ID=your_key_id_here
B2_APPLICATION_KEY=your_application_key_here
B2_BUCKET_NAME=your_bucket_name_here
```

### 💳 Payments (Stripe)

Used for subscription management and billing.

**Setup Steps:**
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your API keys from the "Developers" section
3. Add to your `.env.local`:

```env
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

## Security Best Practices

### ✅ Do's
- Keep `.env.local` in `.gitignore`
- Use different API keys for development and production
- Regularly rotate your API keys
- Use test keys during development

### ❌ Don'ts
- Never commit `.env.local` to version control
- Don't share API keys in chat, email, or documentation
- Don't use production keys in development
- Don't expose server-side keys to client-side code

## Environment Variable Reference

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` | ✅ | Clerk publishable key | `pk_test_...` |
| `CLERK_SECRET_KEY` | ✅ | Clerk secret key | `sk_test_...` |
| `GOOGLE_GEMINI_API_KEY` | ✅ | Google Gemini API key | `AIza...` |
| `B2_APPLICATION_KEY_ID` | ✅ | Backblaze B2 key ID | `004f...` |
| `B2_APPLICATION_KEY` | ✅ | Backblaze B2 application key | `K004...` |
| `B2_BUCKET_NAME` | ✅ | Backblaze B2 bucket name | `wordwave-projects` |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | ✅ | Stripe publishable key | `pk_test_...` |
| `STRIPE_SECRET_KEY` | ✅ | Stripe secret key | `sk_test_...` |
