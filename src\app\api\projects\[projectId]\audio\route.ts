import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, ValidationError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
    }>
}

// POST /api/projects/[projectId]/audio - Save audio for project
export async function POST(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;
    const body = await request.json();
    const { audioData } = body;

    if (!audioData || !audioData.files || audioData.files.length === 0) {
      throw new ValidationError('Audio files are required');
    }

    console.log(`🎵 Saving audio for project ${projectId}, user ${userId}`);

    // Validate that we have actual audio files before proceeding
    if (!audioData.files || audioData.files.length === 0) {
      console.warn(`⚠️ Rejecting audio save for ${audioData.id} - no audio files provided`);
      return NextResponse.json(
        { success: false, error: { message: 'Cannot save audio: No audio files provided', code: 'NO_AUDIO_FILES' } },
        { status: 400 }
      );
    }

    // Check if any files have actual audio data (base64)
    const hasAudioData = audioData.files.some((file: any) => file.base64Data && file.base64Data.length > 0);

    if (!hasAudioData) {
      console.warn(`⚠️ Rejecting audio save for ${audioData.id} - no valid audio data found`);
      return NextResponse.json(
        { success: false, error: { message: 'Cannot save audio: No valid audio data found in files', code: 'NO_AUDIO_DATA' } },
        { status: 400 }
      );
    }

    console.log(`✅ Audio validation passed for ${audioData.id} - ${audioData.files.length} files with valid data`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // Check for existing audio with the same generation ID to prevent duplicates
    try {
      const existingAudio = await cloudService.listAudio(projectId, userId);
      const duplicateAudio = existingAudio.find(audio =>
        audio.id === audioData.id ||
        (audio.generationId && audioData.generationId && audio.generationId === audioData.generationId)
      );

      if (duplicateAudio) {
        console.log(`⚠️ Preventing duplicate audio save - audio with ID ${audioData.id} or generation ID ${audioData.generationId} already exists`);
        return NextResponse.json(
          { success: false, error: { message: 'Audio file already exists', code: 'DUPLICATE_AUDIO' } },
          { status: 409 }
        );
      }
    } catch (listError) {
      console.warn('⚠️ Could not check for duplicate audio files:', listError);
      // Continue with save - better to have duplicates than fail completely
    }

    // Ensure audio is linked to project
    const audioWithProject = {
      ...audioData,
      projectId,
      metadata: {
        ...audioData.metadata,
        userId,
        updatedAt: new Date().toISOString()
      }
    };

    const savedAudio = await cloudService.saveAudio(audioWithProject, userId);

    const response: ApiResponse = {
      success: true,
      data: savedAudio,
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('❌ Failed to save audio:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to save audio',
        code: error.code || 'SAVE_AUDIO_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// GET /api/projects/[projectId]/audio - List audio for project
export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;
    const { searchParams } = new URL(request.url);
    const scriptId = searchParams.get('scriptId'); // Optional filter by script

    console.log(`📋 Listing audio for project ${projectId}, user ${userId}${scriptId ? `, script ${scriptId}` : ''}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const audioList = await cloudService.listAudio(projectId, userId, scriptId || undefined);

    const response: ApiResponse = {
      success: true,
      data: audioList,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to list audio:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to list audio',
        code: error.code || 'LIST_AUDIO_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
