// Audio file naming utilities for WordWave Studio
// Provides consistent and unique naming for audio files to prevent overwrites

/**
 * Sanitize a string for use in filenames
 */
export function sanitizeForFilename(text: string): string {
  return text
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_+/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, '') // Remove leading/trailing underscores
    .substring(0, 50); // Limit length
}

/**
 * Extract a topic/title from script content
 */
export function extractTopicFromScript(scriptContent: string, fallbackTopic?: string): string {
  if (fallbackTopic && fallbackTopic.trim()) {
    return sanitizeForFilename(fallbackTopic);
  }

  // Try to extract meaningful content from the first line or sentence
  const lines = scriptContent.split('\n').filter(line => line.trim());
  if (lines.length === 0) return 'Untitled';

  // Remove speaker prefixes like "Speaker 1:", "Host:", etc.
  const firstLine = lines[0].replace(/^(Speaker\s*\d+|Host|Co-host|Narrator):\s*/i, '').trim();
  
  // Take first sentence or first 30 characters
  const firstSentence = firstLine.split(/[.!?]/)[0];
  const topic = firstSentence.length > 0 ? firstSentence : firstLine;
  
  return sanitizeForFilename(topic.substring(0, 30));
}

/**
 * Generate a timestamp string for filenames
 */
export function generateTimestamp(): string {
  const now = new Date();
  return `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
}

/**
 * Generate a unique generation ID
 */
export function generateGenerationId(): string {
  return `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a comprehensive audio filename
 * Pattern: Topic_Timestamp_ID_Voices.extension
 * Example: Climate_Change_Solutions_20250813_142301_abc123_alice-bob.wav
 */
export function generateAudioFilename(options: {
  projectName: string;
  scriptTopic?: string;
  scriptContent?: string;
  synthesisMode?: 'monologue' | 'podcast';
  voiceConfig?: {
    voice1?: string;
    voice2?: string;
  };
  generationId?: string;
  extension?: string;
}): string {
  const {
    projectName,
    scriptTopic,
    scriptContent = '',
    synthesisMode = 'monologue',
    voiceConfig,
    generationId,
    extension = 'wav'
  } = options;

  // Extract or use topic as the primary identifier
  const topic = extractTopicFromScript(scriptContent, scriptTopic);
  
  // Create voice identifier
  let voiceId = '';
  if (voiceConfig) {
    if (synthesisMode === 'podcast' && voiceConfig.voice1 && voiceConfig.voice2) {
      voiceId = `_${voiceConfig.voice1}-${voiceConfig.voice2}`;
    } else if (voiceConfig.voice1) {
      voiceId = `_${voiceConfig.voice1}`;
    }
  }
  
  // Generate timestamp
  const timestamp = generateTimestamp();
  
  // Generate unique ID if not provided
  const uniqueId = generationId || generateGenerationId();
  const shortId = uniqueId.split('_').pop() || 'gen';
  
  // Construct filename: Topic_Timestamp_ID_Voices.extension (removed project name for cleaner filenames)
  const parts = [
    topic,
    timestamp,
    shortId
  ].filter(Boolean);
  
  return `${parts.join('_')}${voiceId}.${extension}`;
}

/**
 * Parse filename to extract metadata (for legacy support)
 * Supports both old format (ProjectName_Topic_...) and new format (Topic_...)
 */
export function parseAudioFilename(filename: string): {
  projectName?: string;
  topic?: string;
  timestamp?: string;
  generationId?: string;
  voices?: string;
} {
  // Remove extension
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
  
  // Try to parse the structured filename
  const parts = nameWithoutExt.split('_');
  
  if (parts.length >= 4) {
    // Check if this looks like the old format (with project name) or new format (topic-first)
    // Old format: ProjectName_Topic_Timestamp_ID_Voices
    // New format: Topic_Timestamp_ID_Voices
    
    // If the second-to-last part looks like a timestamp (8 digits + 6 digits), 
    // assume new format, otherwise assume old format
    const potentialTimestamp = parts[parts.length - 2];
    const isNewFormat = /^\d{8}_\d{6}$/.test(`${parts[parts.length - 3]}_${potentialTimestamp}`);
    
    if (isNewFormat || parts.length === 3) {
      // New format: Topic_Timestamp_ID
      return {
        topic: parts.slice(0, -2).join('_'),
        timestamp: parts[parts.length - 2],
        generationId: parts[parts.length - 1],
      };
    } else {
      // Old format: ProjectName_Topic_Timestamp_ID_Voices
      return {
        projectName: parts[0],
        topic: parts.slice(1, -2).join('_'),
        timestamp: parts[parts.length - 2],
        generationId: parts[parts.length - 1],
      };
    }
  }
  
  // Fallback for simple filenames
  return {
    topic: parts.join('_') || 'Unknown'
  };
}

/**
 * Generate a display name for audio files
 */
export function generateDisplayName(audioFile: {
  name: string;
  scriptTopic?: string;
  scriptContent?: string;
  createdAt?: string;
  voiceConfig?: {
    voice1?: string;
    voice2?: string;
  };
}): string {
  const { name, scriptTopic, scriptContent, createdAt, voiceConfig } = audioFile;
  
  // Try to extract meaningful info
  const topic = scriptTopic || extractTopicFromScript(scriptContent || '', undefined);
  const date = createdAt ? new Date(createdAt).toLocaleDateString() : '';
  
  // Create a user-friendly display name
  if (topic && topic !== 'Untitled') {
    return date ? `${topic} (${date})` : topic;
  }
  
  // Fallback to filename without extension
  return name.replace(/\.[^/.]+$/, '');
}

/**
 * Check if two audio files would have conflicting names
 */
export function wouldConflict(
  existingFiles: Array<{ name: string; generationId?: string }>,
  newFilename: string,
  newGenerationId?: string
): boolean {
  return existingFiles.some(file => 
    file.name === newFilename && 
    file.generationId !== newGenerationId
  );
}
