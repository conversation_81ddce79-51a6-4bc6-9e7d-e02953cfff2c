import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';

export async function GET() {
  try {
    console.log('🔍 Testing Clerk authentication...');
    
    const user = await currentUser();
    
    if (!user) {
      return NextResponse.json({
        status: 'error',
        message: 'No authenticated user found',
        timestamp: new Date().toISOString(),
      }, { status: 401 });
    }

    return NextResponse.json({
      status: 'success',
      message: 'Clerk authentication working',
      user: {
        id: user.id,
        email: user.emailAddresses[0]?.emailAddress,
        name: user.fullName || `${user.firstName} ${user.lastName}`,
        hasStripeCustomerId: !!user.unsafeMetadata?.stripeCustomerId,
        stripeCustomerId: user.unsafeMetadata?.stripeCustomerId,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Clerk auth test failed:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Clerk authentication failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
