// Project serialization utilities for WordWave Studio

import { ProjectData, LegacyProjectData, ProjectConfiguration, ProjectScript, ProjectMetadata, AudioFile } from '@/lib/types';
import { generateProjectId } from '@/lib/audioUtils';

/**
 * Create a new project data structure
 */
export function createNewProject(
  configuration: Omit<ProjectConfiguration, 'createdAt' | 'updatedAt'>
): LegacyProjectData {
  const now = new Date().toISOString();

  return {
    id: generateProjectId(),
    configuration: {
      ...configuration,
      createdAt: now,
      updatedAt: now,
    },
    audioFiles: [],
    metadata: {
      version: '1.0.0',
      createdAt: now,
      updatedAt: now,
      userId: '', // Will be set when saving
      totalSize: 0,
      fileCount: 0,
    },
  };
}

/**
 * Update project configuration
 */
export function updateProjectConfiguration(
  project: LegacyProjectData,
  updates: Partial<Omit<ProjectConfiguration, 'createdAt' | 'updatedAt'>>
): LegacyProjectData {
  return {
    ...project,
    configuration: {
      ...project.configuration,
      ...updates,
      updatedAt: new Date().toISOString(),
    },
    metadata: {
      ...project.metadata,
      updatedAt: new Date().toISOString(),
    },
  };
}

/**
 * Add or update script in project
 */
export function updateProjectScript(
  project: LegacyProjectData,
  script: Omit<ProjectScript, 'generatedAt'>
): LegacyProjectData {
  return {
    ...project,
    script: {
      ...script,
      generatedAt: new Date().toISOString(),
    },
    metadata: {
      ...project.metadata,
      updatedAt: new Date().toISOString(),
    },
  };
}

/**
 * Add audio file to project
 */
export function addAudioFileToProject(
  project: LegacyProjectData,
  audioFile: AudioFile
): LegacyProjectData {
  const updatedAudioFiles = [...project.audioFiles, audioFile];

  return {
    ...project,
    audioFiles: updatedAudioFiles,
    metadata: {
      ...project.metadata,
      updatedAt: new Date().toISOString(),
      fileCount: updatedAudioFiles.length + (project.script ? 1 : 0), // +1 for script if exists
      totalSize: updatedAudioFiles.reduce((total: number, file: AudioFile) => total + (file.size || 0), 0),
    },
  };
}

/**
 * Remove audio file from project
 */
export function removeAudioFileFromProject(
  project: LegacyProjectData,
  audioFileId: string
): LegacyProjectData {
  const updatedAudioFiles = project.audioFiles.filter((file: AudioFile) => file.id !== audioFileId);

  return {
    ...project,
    audioFiles: updatedAudioFiles,
    metadata: {
      ...project.metadata,
      updatedAt: new Date().toISOString(),
      fileCount: updatedAudioFiles.length + (project.script ? 1 : 0),
      totalSize: updatedAudioFiles.reduce((total: number, file: AudioFile) => total + (file.size || 0), 0),
    },
  };
}

/**
 * Update project metadata
 */
export function updateProjectMetadata(
  project: LegacyProjectData,
  updates: Partial<Omit<ProjectMetadata, 'createdAt' | 'updatedAt' | 'userId'>>
): LegacyProjectData {
  return {
    ...project,
    metadata: {
      ...project.metadata,
      ...updates,
      updatedAt: new Date().toISOString(),
    },
  };
}

/**
 * Serialize project for storage
 */
export function serializeProject(project: LegacyProjectData): string {
  return JSON.stringify(project, null, 2);
}

/**
 * Deserialize project from storage
 */
export function deserializeProject(serializedProject: string): LegacyProjectData {
  try {
    const project = JSON.parse(serializedProject) as LegacyProjectData;
    
    // Validate required fields
    if (!project.id || !project.configuration || !project.metadata) {
      throw new Error('Invalid project structure');
    }
    
    return project;
  } catch (error) {
    throw new Error(`Failed to deserialize project: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate project data structure
 */
export function validateProject(project: any): project is ProjectData {
  if (!project || typeof project !== 'object') {
    return false;
  }

  // Check required top-level fields
  if (!project.id || typeof project.id !== 'string') {
    return false;
  }

  if (!project.configuration || typeof project.configuration !== 'object') {
    return false;
  }

  if (!project.metadata || typeof project.metadata !== 'object') {
    return false;
  }

  if (!Array.isArray(project.audioFiles)) {
    return false;
  }

  // Check configuration fields
  const config = project.configuration;
  if (!config.projectName || typeof config.projectName !== 'string') {
    return false;
  }

  if (!config.synthesisMode || !['monologue', 'podcast'].includes(config.synthesisMode)) {
    return false;
  }

  // Check metadata fields
  const metadata = project.metadata;
  if (!metadata.version || typeof metadata.version !== 'string') {
    return false;
  }

  if (!metadata.createdAt || typeof metadata.createdAt !== 'string') {
    return false;
  }

  return true;
}

/**
 * Get project summary for display
 */
export function getProjectSummary(project: LegacyProjectData): {
  id: string;
  name: string;
  mode: string;
  hasScript: boolean;
  audioFileCount: number;
  lastUpdated: string;
  totalSize: number;
} {
  return {
    id: project.id,
    name: project.configuration.projectName,
    mode: project.configuration.synthesisMode,
    hasScript: !!project.script,
    audioFileCount: project.audioFiles.length,
    lastUpdated: project.metadata.updatedAt,
    totalSize: project.metadata.totalSize,
  };
}
