import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, NotFoundError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
        scriptId: string;
    }>
}

// GET /api/projects/[projectId]/scripts/[scriptId] - Load specific script
export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId, scriptId } = await context.params;

    console.log(`📥 Loading script ${scriptId} for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const scriptData = await cloudService.loadScript(scriptId, projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: scriptData,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to load script:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to load script',
        code: error.code || 'LOAD_SCRIPT_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// PUT /api/projects/[projectId]/scripts/[scriptId] - Update script
export async function PUT(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId, scriptId } = await context.params;
    const body = await request.json();
    const { scriptData } = body;

    console.log(`📝 Updating script ${scriptId} for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const updatedScript = await cloudService.updateScript(
      scriptId,
      projectId,
      userId,
      scriptData
    );

    const response: ApiResponse = {
      success: true,
      data: updatedScript,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to update script:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to update script',
        code: error.code || 'UPDATE_SCRIPT_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// DELETE /api/projects/[projectId]/scripts/[scriptId] - Delete script
export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId, scriptId } = await context.params;

    console.log(`🗑️ Deleting script ${scriptId} for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    await cloudService.deleteScript(scriptId, projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Script deleted successfully' },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to delete script:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to delete script',
        code: error.code || 'DELETE_SCRIPT_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
