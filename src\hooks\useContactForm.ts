import { useState } from 'react';

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormState {
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
}

export function useContactForm() {
  const [state, setState] = useState<ContactFormState>({
    isLoading: false,
    isSuccess: false,
    error: null,
  });

  const submitForm = async (formData: ContactFormData) => {
    setState({ isLoading: true, isSuccess: false, error: null });

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send message');
      }

      setState({ isLoading: false, isSuccess: true, error: null });
      return { success: true, message: result.message };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setState({ isLoading: false, isSuccess: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  const resetForm = () => {
    setState({ isLoading: false, isSuccess: false, error: null });
  };

  return {
    ...state,
    submitForm,
    resetForm,
  };
}
