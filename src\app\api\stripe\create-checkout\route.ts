import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import Stripe from 'stripe';

export async function POST(request: NextRequest) {
  console.log('🚀 Starting checkout session creation...');
  
  try {
    // Check environment variables first
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('❌ STRIPE_SECRET_KEY is missing');
      return NextResponse.json({ error: 'Stripe configuration error' }, { status: 500 });
    }

    if (!process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID || !process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID) {
      console.error('❌ Stripe price IDs are missing');
      return NextResponse.json({ error: 'Stripe price configuration error' }, { status: 500 });
    }

    // Initialize Stripe inside the handler to avoid build-time issues
    console.log('🔧 Initializing Stripe...');
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

    console.log('📨 Parsing request body...');
    const { priceId } = await request.json();
    console.log('💰 Requested price ID:', priceId);

    console.log('👤 Getting current user...');
    const user = await currentUser();

    if (!user) {
      console.error('❌ No authenticated user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('✅ User authenticated:', user.id);

    // Check if user already has a Stripe customer ID
    let stripeCustomerId = user.unsafeMetadata?.stripeCustomerId as string;
    console.log('🔍 Checking for existing Stripe customer ID:', stripeCustomerId);

    // Validate that the stored customer ID exists in current Stripe environment
    if (stripeCustomerId) {
      console.log('🔍 Validating stored customer ID...');
      try {
        await stripe.customers.retrieve(stripeCustomerId);
        console.log('✅ Existing customer ID is valid');
      } catch (customerError) {
        console.log('⚠️ Stored customer ID is invalid (likely from different Stripe environment), creating new customer');
        stripeCustomerId = ''; // Reset to force creation of new customer
      }
    }

    if (!stripeCustomerId) {
      console.log('🆕 Creating new Stripe customer for user:', user.id);
      try {
        // Create new Stripe customer
        const customer = await stripe.customers.create({
          email: user.emailAddresses[0]?.emailAddress,
          name: user.fullName || `${user.firstName} ${user.lastName}`,
          metadata: {
            clerkUserId: user.id,
          },
        });

        stripeCustomerId = customer.id;
        console.log('✅ Created Stripe customer:', stripeCustomerId);

        // Store Stripe customer ID in Clerk metadata
        console.log('💾 Storing customer ID in Clerk metadata...');
        try {
          const clerk = await clerkClient();
          await clerk.users.updateUserMetadata(user.id, {
            unsafeMetadata: {
              ...user.unsafeMetadata,
              stripeCustomerId: customer.id,
            },
          });
          console.log('✅ Successfully stored customer ID in Clerk metadata');
        } catch (metadataError) {
          console.error('⚠️ Failed to update Clerk metadata, but continuing with checkout:', metadataError);
          // Don't fail the entire checkout if metadata update fails
        }
      } catch (customerError) {
        console.error('❌ Failed to create Stripe customer:', customerError);
        throw new Error('Failed to create customer');
      }
    } else {
      console.log('✅ Using existing Stripe customer:', stripeCustomerId);
    }

    // Determine the mode based on the price ID
    const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID;
    const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID;
    
    let mode: 'subscription' | 'payment';
    if (priceId === monthlyPriceId) {
      mode = 'subscription'; // Monthly recurring subscription
    } else if (priceId === lifetimePriceId) {
      mode = 'payment'; // One-time lifetime payment
    } else {
      throw new Error(`Unknown price ID: ${priceId}`);
    }

    console.log(`💳 Creating checkout session with mode: ${mode} for price: ${priceId}`);

    // Create checkout session configuration
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: mode,
      success_url: `${request.nextUrl.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.nextUrl.origin}/pricing`,
      allow_promotion_codes: true, // Enable promotion codes in checkout
      metadata: {
        clerkUserId: user.id,
        priceId: priceId,
      },
    };

    // Add trial period for monthly subscriptions
    if (mode === 'subscription') {
      sessionConfig.subscription_data = {
        trial_period_days: 7, // 7-day free trial
        metadata: {
          clerkUserId: user.id,
          priceId: priceId,
        },
      };
    }

    // Create checkout session
    console.log('🛒 Creating Stripe checkout session...');
    try {
      const session = await stripe.checkout.sessions.create(sessionConfig);
      console.log('✅ Checkout session created successfully:', session.id);
      return NextResponse.json({ sessionId: session.id });
    } catch (sessionError) {
      console.error('❌ Failed to create checkout session:', sessionError);
      throw new Error(`Checkout session creation failed: ${sessionError instanceof Error ? sessionError.message : 'Unknown error'}`);
    }
  } catch (error) {
    console.error('💥 Error creating checkout session:', error);
    console.error('📊 Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });
    
    return NextResponse.json(
      { 
        error: 'Failed to create checkout session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
