// Model configuration service
import modelsConfig from '../config/models.json';

export interface ModelInfo {
  name: string;
  apiName: string;
  description?: string;
  category?: string;
}

export interface VoiceInfo {
  name: string;
  apiName: string;
  description?: string;
  category?: string;
  gender?: string;
  accent?: string;
  ageRange?: string;
}

export interface ModelsConfig {
  scriptModels: ModelInfo[];
  ttsModels: ModelInfo[];
  voices: VoiceInfo[];
  lastUpdated: string;
  source: 'manual' | 'api';
}

// Cache for API-fetched models
let cachedModels: ModelsConfig | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Load models from JSON configuration file
 */
export function getModelsFromConfig(): ModelsConfig {
  return modelsConfig as ModelsConfig;
}

/**
 * Fetch available models from Google's API
 * This requires an API key and should be used sparingly due to rate limits
 * For pricing information, see: https://ai.google.dev/gemini-api/docs/pricing
 */
export async function fetchModelsFromAPI(apiKey?: string): Promise<ModelInfo[]> {
  if (!apiKey) {
    throw new Error('API key is required to fetch models from Google API');
  }

  try {
    const response = await fetch(
      'https://generativelanguage.googleapis.com/v1beta/models',
      {
        headers: {
          'x-goog-api-key': apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch models: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.models || !Array.isArray(data.models)) {
      throw new Error('Invalid response format from models API');
    }

    return data.models.map((model: any) => ({
      name: model.baseModelId || model.name,
      apiName: model.baseModelId || model.name,
      description: model.description || '',
      category: categorizeModel(model.baseModelId || model.name),
    }));
  } catch (error) {
    console.error('Error fetching models from API:', error);
    throw error;
  }
}

/**
 * Fetch available voices from Google's API
 * This requires an API key and should be used sparingly due to rate limits
 * Note: Currently uses hardcoded voices as Google doesn't provide a voices API endpoint
 */
export async function fetchVoicesFromAPI(apiKey?: string): Promise<VoiceInfo[]> {
  // For now, return the current hardcoded voices with enhanced metadata
  // In the future, this could fetch from an actual API endpoint if Google provides one
  const config = getModelsFromConfig();
  return config.voices || [];
}

/**
 * Categorize a model based on its name
 */
function categorizeModel(modelName: string): string {
  if (modelName.includes('pro')) return 'premium';
  if (modelName.includes('lite')) return 'lite';
  return 'standard';
}

/**
 * Filter models for script generation (text generation)
 * Only include Gemini and Gemma models
 */
function filterScriptModels(models: ModelInfo[]): ModelInfo[] {
  // Only include Gemini and Gemma models, exclude TTS and other specialized models
  return models.filter(model => {
    const modelName = model.apiName.toLowerCase();
    
    // Must be Gemini or Gemma model
    const isGeminiOrGemma = modelName.includes('gemini') || modelName.includes('gemma');
    
    // Exclude specialized models
    const isSpecialized = modelName.includes('tts') ||
                         modelName.includes('image-generation') ||
                         modelName.includes('embedding') ||
                         modelName.includes('live');
    
    return isGeminiOrGemma && !isSpecialized;
  });
}

/**
 * Filter models for TTS (text-to-speech)
 * Only include Gemini and Gemma models with TTS capability
 */
function filterTTSModels(models: ModelInfo[]): ModelInfo[] {
  return models.filter(model => {
    const modelName = model.apiName.toLowerCase();
    
    // Must be Gemini or Gemma model with TTS capability
    const isGeminiOrGemma = modelName.includes('gemini') || modelName.includes('gemma');
    const isTTS = modelName.includes('tts');
    
    return isGeminiOrGemma && isTTS;
  });
}

/**
 * Get models with optional API fallback
 * Uses cached config by default, optionally fetches from API if enabled
 */
export async function getAvailableModels(options?: {
  useAPI?: boolean;
  apiKey?: string;
  forceRefresh?: boolean;
}): Promise<ModelsConfig> {
  const { useAPI = false, apiKey, forceRefresh = false } = options || {};

  // Check if we should use cached API data
  const now = Date.now();
  const shouldUseCache = cachedModels && 
    cachedModels.source === 'api' && 
    (now - lastFetchTime < CACHE_DURATION) && 
    !forceRefresh;

  if (shouldUseCache) {
    return cachedModels!;
  }

  // If API usage is disabled or no API key, use JSON config
  if (!useAPI || !apiKey) {
    return getModelsFromConfig();
  }

  try {
    console.log('Fetching latest models from Google API...');
    const allModels = await fetchModelsFromAPI(apiKey);
    const allVoices = await fetchVoicesFromAPI(apiKey);
    
    const config: ModelsConfig = {
      scriptModels: filterScriptModels(allModels),
      ttsModels: filterTTSModels(allModels),
      voices: allVoices,
      lastUpdated: new Date().toISOString(),
      source: 'api',
    };

    // Cache the result
    cachedModels = config;
    lastFetchTime = now;

    return config;
  } catch (error) {
    console.warn('Failed to fetch models from API, falling back to local config:', error);
    return getModelsFromConfig();
  }
}

/**
 * Get script models
 */
export async function getScriptModels(options?: Parameters<typeof getAvailableModels>[0]): Promise<ModelInfo[]> {
  const config = await getAvailableModels(options);
  return config.scriptModels;
}

/**
 * Get TTS models
 */
export async function getTTSModels(options?: Parameters<typeof getAvailableModels>[0]): Promise<ModelInfo[]> {
  const config = await getAvailableModels(options);
  return config.ttsModels;
}

/**
 * Get available voices
 */
export async function getVoices(options?: Parameters<typeof getAvailableModels>[0]): Promise<VoiceInfo[]> {
  const config = await getAvailableModels(options);
  return config.voices;
}

/**
 * Get voices filtered by category
 */
export async function getVoicesByCategory(category: string, options?: Parameters<typeof getAvailableModels>[0]): Promise<VoiceInfo[]> {
  const voices = await getVoices(options);
  return voices.filter(voice => voice.category === category);
}

/**
 * Get voices filtered by characteristics
 */
export async function getVoicesFiltered(
  filters: {
    category?: string;
    gender?: string;
    accent?: string;
    ageRange?: string;
  },
  options?: Parameters<typeof getAvailableModels>[0]
): Promise<VoiceInfo[]> {
  const voices = await getVoices(options);
  
  return voices.filter(voice => {
    if (filters.category && voice.category !== filters.category) return false;
    if (filters.gender && voice.gender !== filters.gender) return false;
    if (filters.accent && voice.accent !== filters.accent) return false;
    if (filters.ageRange && voice.ageRange !== filters.ageRange) return false;
    return true;
  });
}

/**
 * Update the local models.json file with latest data from API
 * This is useful for maintaining the JSON config file
 */
export async function updateModelsConfig(apiKey: string): Promise<void> {
  const config = await getAvailableModels({ useAPI: true, apiKey, forceRefresh: true });
  
  // In a real app, you'd want to write this to a file
  // For now, just log the updated config
  console.log('Updated models config:', JSON.stringify(config, null, 2));
  
  // Note: Writing to files in the browser isn't possible
  // This would need to be implemented on the server side
}
