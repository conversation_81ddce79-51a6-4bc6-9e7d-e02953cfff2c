import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, NotFoundError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service with singleton pattern
let cloudService: ServerCloudStorageService | null = null;
let initializationPromise: Promise<void> | null = null;

function getCloudService(): ServerCloudStorageService {
  if (!cloudService) {
    cloudService = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
      applicationKey: process.env.B2_APPLICATION_KEY!,
      bucketName: process.env.B2_BUCKET_NAME!
    });
  }
  return cloudService;
}

async function ensureCloudServiceInitialized(): Promise<void> {
  const service = getCloudService();

  // If already initialized, return immediately
  if (service['isAuthorized'] && service['bucketId']) {
    return;
  }

  // If initialization is in progress, wait for it
  if (initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  initializationPromise = service.initialize();

  try {
    await initializationPromise;
  } finally {
    initializationPromise = null;
  }
}

interface RouteContext {
    params: Promise<{
        projectId: string;
    }>
}

// GET /api/projects/[projectId] - Load project configuration only
export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;

    console.log(`📥 Loading project configuration ${projectId} for user ${userId}`);

    // Ensure cloud storage service is initialized
    await ensureCloudServiceInitialized();
    const service = getCloudService();

    // Load project configuration only
    const projectData = await service.loadProjectConfig(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: projectData,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to load project configuration:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to load project configuration',
        code: error.code || 'LOAD_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// PUT /api/projects/[projectId] - Update project configuration
export async function PUT(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;
    const body = await request.json();
    const { configuration } = body;

    console.log(`📝 Updating project configuration ${projectId} for user ${userId}`);

    // Ensure cloud storage service is initialized
    await ensureCloudServiceInitialized();
    const service = getCloudService();

    const updatedProject = await service.updateProjectConfig(
      projectId,
      userId,
      configuration
    );

    const response: ApiResponse = {
      success: true,
      data: updatedProject,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to update project configuration:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to update project configuration',
        code: error.code || 'UPDATE_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// DELETE /api/projects/[projectId] - Delete project and all associated data
export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;

    console.log(`🗑️ Deleting project ${projectId} for user ${userId}`);

    // Ensure cloud storage service is initialized
    await ensureCloudServiceInitialized();
    const service = getCloudService();

    await service.deleteProject(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Project deleted successfully' },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to delete project:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to delete project',
        code: error.code || 'DELETE_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
