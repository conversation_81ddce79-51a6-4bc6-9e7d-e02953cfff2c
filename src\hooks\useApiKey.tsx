'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';

export interface ApiKeyHook {
  apiKey: string | null;
  isSignedIn: boolean;
  setApiKey: (key: string) => void;
  clearApiKey: () => void;
  isLoading: boolean;
}

/**
 * Hook to manage Google Gemini API key for the authenticated user
 */
export const useApiKey = (): ApiKeyHook => {
  const { user, isSignedIn, isLoaded } = useUser();
  const [apiKey, setApiKeyState] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load API key from localStorage and user metadata when user is loaded
  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      console.log('🔑 Loading API key for user:', user.id);

      // Try localStorage first (more reliable)
      const localStorageKey = localStorage.getItem(`wordwave_api_key_${user.id}`);
      console.log('🔑 localStorage key found:', !!localStorageKey);

      if (localStorageKey) {
        console.log('🔑 Using localStorage key');
        setApiKeyState(localStorageKey);
      } else {
        // Fallback to Clerk metadata
        const storedApiKey = user.unsafeMetadata?.geminiApiKey as string;
        console.log('🔑 Clerk metadata key found:', !!storedApiKey);

        if (storedApiKey) {
          console.log('🔑 Using Clerk metadata key');
          setApiKeyState(storedApiKey);
          // Sync to localStorage for future use
          localStorage.setItem(`wordwave_api_key_${user.id}`, storedApiKey);
        }
      }
      setIsLoading(false);
    } else if (isLoaded && !isSignedIn) {
      console.log('🔑 User not signed in, clearing API key');
      setApiKeyState(null);
      setIsLoading(false);
    }
  }, [user, isSignedIn, isLoaded]);

  const setApiKey = async (key: string) => {
    if (!user) return;

    try {
      // Store API key in localStorage as fallback and primary storage
      localStorage.setItem(`wordwave_api_key_${user.id}`, key);
      setApiKeyState(key);

      // Also try to store in Clerk metadata (optional)
      try {
        await user.update({
          unsafeMetadata: {
            ...user.unsafeMetadata,
            geminiApiKey: key,
          },
        });
      } catch (clerkError) {
        console.warn('Could not save to Clerk metadata, using localStorage only:', clerkError);
      }
    } catch (error) {
      console.error('Failed to save API key:', error);
      throw error;
    }
  };

  const clearApiKey = async () => {
    if (!user) return;

    try {
      // Remove API key from localStorage
      localStorage.removeItem(`wordwave_api_key_${user.id}`);
      setApiKeyState(null);

      // Also try to remove from Clerk metadata (optional)
      try {
        const { geminiApiKey, ...restMetadata } = user.unsafeMetadata as any;
        await user.update({
          unsafeMetadata: restMetadata,
        });
      } catch (clerkError) {
        console.warn('Could not clear from Clerk metadata, localStorage cleared:', clerkError);
      }
    } catch (error) {
      console.error('Failed to clear API key:', error);
      throw error;
    }
  };

  return {
    apiKey,
    isSignedIn: isSignedIn || false,
    setApiKey,
    clearApiKey,
    isLoading,
  };
};
