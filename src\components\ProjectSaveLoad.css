/* Enhanced Project Save/Load Component Styles */

.project-save-load {
  margin-top: 1rem;
}

.save-load-buttons {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.save-button,
.load-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.save-button {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
}

.save-button:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
}

.load-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.load-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
}

.load-button:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
}

.save-description {
  margin-bottom: 0.75rem;
}

.description-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(100, 116, 139, 0.5);
  border-radius: 0.5rem;
  color: white;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.description-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.description-input::placeholder {
  color: rgba(156, 163, 175, 0.8);
}

/* Dialog Styles */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.dialog {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 1rem;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(100, 116, 139, 0.2);
}

.dialog-header h3 {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease;
}

.close-button:hover {
  color: white;
}

.dialog-content {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.loading,
.empty-state {
  text-align: center;
  color: #9ca3af;
  padding: 2rem;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(100, 116, 139, 0.2);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.project-item:hover {
  background: rgba(51, 65, 85, 0.5);
  border-color: rgba(100, 116, 139, 0.4);
  transform: translateY(-1px);
}

.project-info {
  flex: 1;
  cursor: pointer;
}

.project-name {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.project-meta {
  color: #9ca3af;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.project-description {
  color: #d1d5db;
  font-size: 0.875rem;
  font-style: italic;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.load-action-btn,
.delete-action-btn {
  background: none;
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
}

.load-action-btn {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
}

.load-action-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
}

.delete-action-btn {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.delete-action-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.5);
  transform: translateY(-1px);
}

.load-action-btn:disabled,
.delete-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.load-icon {
  font-size: 1.25rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.project-item:hover .load-icon {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 640px) {
  .save-load-buttons {
    flex-direction: column;
  }
  
  .dialog {
    margin: 0.5rem;
    max-width: none;
  }
  
  .dialog-header,
  .dialog-content {
    padding: 1rem;
  }
}
