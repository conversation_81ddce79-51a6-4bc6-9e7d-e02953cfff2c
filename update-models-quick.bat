@echo off
REM Quick models and voices updater using environment variable
REM Set your API key once as an environment variable

echo.
echo ========================================
echo WordWave Quick Models & Voices Updater
echo ========================================
echo.

REM Check if GEMINI_API_KEY environment variable is set
if "%GEMINI_API_KEY%"=="" (
    echo Error: GEMINI_API_KEY environment variable is not set!
    echo.
    echo Please set it first by running:
    echo set GEMINI_API_KEY=your_api_key_here
    echo.
    echo Or add it to your system environment variables.
    echo.
    pause
    exit /b 1
)

echo Using API key from environment variable...
echo.
echo Fetching latest models and voices from Google's API...
echo.

REM Run the npm script using environment variable
npm run update-models

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ SUCCESS! Models updated successfully!
    echo ========================================
    echo.
    echo The models.json file has been updated.
    echo.
    echo Would you like to restart the dev server? (y/n)
    set /p choice="Enter your choice: "
    
    if /i "%choice%"=="y" (
        echo.
        echo Stopping existing servers and starting fresh...
        taskkill /F /IM node.exe >nul 2>&1
        timeout /t 2 >nul
        npm run dev
    ) else (
        echo.
        echo Remember to restart your dev server to see the changes!
        pause
    )
) else (
    echo.
    echo ❌ Failed to update models. Check the error above.
    pause
    exit /b %errorlevel%
)
