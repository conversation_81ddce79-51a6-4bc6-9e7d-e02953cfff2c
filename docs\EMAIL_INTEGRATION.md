# Email Integration Guide

## Overview
This document outlines the email integration in WordWave Studio, which uses both SendGrid and Resend for sending transactional emails.

## Table of Contents
1. [Email Service Setup](#email-service-setup)
2. [Environment Configuration](#environment-configuration)
3. [API Interaction](#api-interaction)
4. [Key Files](#key-files)

---

## 1. Email Service Setup

The application is configured to use three different email services:

*   **Sender**: [https://www.sender.net/](https://www.sender.net/)
*   **Resend**: [https://resend.com/](https://resend.com/)
*   **SendGrid**: [https://sendgrid.com/](https://sendgrid.com/)

You will need to create an account with at least one of these services to send emails.

---

## 2. Environment Configuration

The following environment variables need to be set in your `.env.local` file. The application will use the services in the order they are listed below.

```bash
# Email Services (add at least one)
SENDER_API_KEY=
RESEND_API_KEY=
SENDGRID_API_KEY=
FROM_EMAIL=
```

---

## 3. API Interaction

The `EmailService` class in `src/lib/EmailService.ts` is a singleton that handles all email sending. It has a `sendEmail` method that will attempt to send an email using Sender, Resend, or SendGrid, in that order, depending on which API keys are configured.

```typescript
// src/lib/EmailService.ts

private async sendEmail(template: EmailTemplate): Promise<boolean> {
  try {
    if (process.env.SENDER_API_KEY) {
      return await this.sendWithSender(template);
    } else if (process.env.RESEND_API_KEY) {
      return await this.sendWithResend(template);
    } else if (process.env.SENDGRID_API_KEY) {
      return await this.sendWithSendGrid(template);
    } else {
      console.log('Email service not configured. Would send email:', {
        to: template.to,
        subject: template.subject,
        htmlLength: template.html.length
      });
      return true; // Return true for development
    }
  } catch (error) {
    console.error('Error in email service:', error);
    return false;
  }
}
```

---

## 4. Key Files

| File | Purpose |
|------|---------|
| `src/lib/EmailService.ts` | Contains the `EmailService` class that handles all email sending. |
