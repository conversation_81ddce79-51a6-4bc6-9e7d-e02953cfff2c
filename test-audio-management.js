/**
 * Test script to validate audio file management fixes
 * 
 * This script tests:
 * 1. Audio upload error handling - ensures JSON metadata is not saved when audio upload fails
 * 2. Audio file validation - ensures only complete audio entries are displayed
 * 3. Script content preservation - ensures script content is saved and loaded correctly
 * 4. Cleanup mechanism - ensures orphaned metadata files can be cleaned up
 */

const BASE_URL = 'http://localhost:3001/api';

// Mock user ID for testing (replace with actual user ID when testing)
const TEST_USER_ID = 'test_user_123';
const TEST_PROJECT_ID = 'test_project_audio_management';

/**
 * Test 1: Verify that audio metadata is not saved when audio upload fails
 */
async function testAudioUploadErrorHandling() {
  console.log('\n🧪 Test 1: Audio Upload Error Handling');
  
  try {
    // Create a test audio data with invalid base64 data to simulate upload failure
    const invalidAudioData = {
      id: 'test_audio_invalid',
      projectId: TEST_PROJECT_ID,
      name: 'Test Invalid Audio',
      files: [{
        id: 'invalid_file',
        name: 'invalid_audio.wav',
        base64Data: 'invalid_base64_data', // This should cause upload to fail
        processedMimeType: 'audio/wav',
        size: 1000,
        scriptContent: 'This is test script content',
        scriptTopic: 'Test Topic'
      }],
      ttsModel: 'test-model',
      voiceConfig: {
        voice1: 'test-voice1',
        voice2: 'test-voice2',
        synthesisMode: 'monologue'
      },
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
        totalSize: 1000,
        fileCount: 1,
        description: 'Test audio with invalid data'
      }
    };

    const response = await fetch(`${BASE_URL}/projects/${TEST_PROJECT_ID}/audio`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ audioData: invalidAudioData })
    });

    const result = await response.json();
    
    if (!result.success) {
      console.log('✅ Test 1 PASSED: Audio upload correctly failed when base64 data is invalid');
      console.log(`   Error message: ${result.error?.message}`);
    } else {
      console.log('❌ Test 1 FAILED: Audio upload should have failed but succeeded');
    }
  } catch (error) {
    console.log('❌ Test 1 ERROR:', error.message);
  }
}

/**
 * Test 2: Verify that only complete audio entries are returned by listAudio
 */
async function testAudioFileValidation() {
  console.log('\n🧪 Test 2: Audio File Validation');
  
  try {
    const response = await fetch(`${BASE_URL}/projects/${TEST_PROJECT_ID}/audio`);
    const result = await response.json();
    
    if (result.success) {
      const audioList = result.data;
      console.log(`✅ Test 2 INFO: Found ${audioList.length} audio entries`);
      
      // Check if all returned audio entries have valid files
      let allValid = true;
      for (const audio of audioList) {
        if (!audio.files || audio.files.length === 0) {
          console.log(`❌ Audio entry ${audio.id} has no files`);
          allValid = false;
        } else {
          console.log(`✅ Audio entry ${audio.id} has ${audio.files.length} files`);
        }
      }
      
      if (allValid) {
        console.log('✅ Test 2 PASSED: All returned audio entries have valid files');
      } else {
        console.log('❌ Test 2 FAILED: Some audio entries have no valid files');
      }
    } else {
      console.log('❌ Test 2 ERROR: Failed to list audio files');
    }
  } catch (error) {
    console.log('❌ Test 2 ERROR:', error.message);
  }
}

/**
 * Test 3: Test the cleanup mechanism
 */
async function testCleanupMechanism() {
  console.log('\n🧪 Test 3: Cleanup Mechanism');
  
  try {
    const response = await fetch(`${BASE_URL}/projects/${TEST_PROJECT_ID}/audio/cleanup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Test 3 PASSED: Cleanup mechanism executed successfully');
      console.log(`   Cleaned: ${result.data.cleaned} orphaned files`);
      console.log(`   Errors: ${result.data.errors.length}`);
      if (result.data.errors.length > 0) {
        console.log('   Error details:', result.data.errors);
      }
    } else {
      console.log('❌ Test 3 FAILED: Cleanup mechanism failed');
      console.log(`   Error: ${result.error?.message}`);
    }
  } catch (error) {
    console.log('❌ Test 3 ERROR:', error.message);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Audio File Management Tests');
  console.log('=====================================');
  
  await testAudioUploadErrorHandling();
  await testAudioFileValidation();
  await testCleanupMechanism();
  
  console.log('\n✅ All tests completed!');
  console.log('=====================================');
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  runAllTests().catch(console.error);
} else {
  // Browser environment - expose functions to global scope
  window.testAudioManagement = {
    runAllTests,
    testAudioUploadErrorHandling,
    testAudioFileValidation,
    testCleanupMechanism
  };
  console.log('Audio management test functions available at window.testAudioManagement');
}
