# Trial Implementation Guide

## Overview

WordWave Studio now offers a 7-day free trial for monthly subscriptions. This document explains how the trial system works and how to manage it.

### Why 7 Days?

7 days is the optimal trial duration for SaaS products because:
- **Urgency**: Creates healthy conversion pressure
- **Engagement**: Users are more likely to actively try features within a week
- **Cost Efficiency**: Reduces server costs and potential abuse
- **Industry Standard**: Most successful SaaS companies use 7-day trials
- **Decision Making**: Sufficient time to evaluate without decision fatigue

For WordWave Studio specifically, 7 days allows users to:
- Generate multiple scripts across different topics
- Test various voice combinations
- Create several complete audio projects
- Experience the full content creation workflow

## Implementation Details

### 1. Trial Configuration

**Trial Duration**: 7 days
**Applies To**: Monthly subscriptions only
**Payment**: Credit card required but not charged until trial ends

### 2. Code Changes Made

#### 2.1 Checkout Session (create-checkout/route.ts)
- Added `subscription_data.trial_period_days: 7` for monthly subscriptions
- Trial is automatically applied when users select monthly plan

#### 2.2 Webhook Handler (webhook/route.ts)
- Updated to handle `trialing` subscription status
- Stores trial end date in user metadata
- Handles transition from trial to active subscription

#### 2.3 Subscription Status Hook (useSubscriptionStatus.tsx)
- Added `trialEnd` and `isTrialing` fields
- Updated logic to consider trialing subscriptions as active
- Proper handling of trial expiration

#### 2.4 UI Updates
- **AccountMenu**: Shows "Free Trial" status with trial end date
- **Pricing Page**: Updated to highlight 7-day free trial
- **Button Text**: Changed from "Start Monthly Plan" to "Start 7-Day Free Trial"

### 3. Subscription States

The system now handles these subscription states:

1. **trialing**: User is in free trial period
2. **active**: User has paid subscription (post-trial or lifetime)
3. **past_due**: Payment failed, subscription suspended
4. **canceled**: Subscription cancelled

### 4. Trial User Experience

#### 4.1 Sign Up Flow
1. User clicks "Start 7-Day Free Trial"
2. Redirected to Stripe Checkout
3. Credit card required but not charged
4. Subscription created with `trialing` status
5. User gains immediate access to all features

#### 4.2 During Trial
- Full access to all features
- Account menu shows "Free Trial" status
- Trial end date displayed
- Can manage subscription through customer portal

#### 4.3 Trial End
- Automatic conversion to paid subscription
- First charge occurs at trial end
- Status changes from `trialing` to `active`

### 5. Webhooks to Monitor

Important webhook events for trial management:

```typescript
// Trial starts
customer.subscription.created (status: trialing)

// Trial ends, payment successful  
customer.subscription.updated (status: active)
invoice.payment_succeeded

// Trial ends, payment failed
invoice.payment_failed
customer.subscription.updated (status: past_due)
```

### 6. Testing Trials

#### 6.1 Stripe Test Mode
Use Stripe test cards to simulate different scenarios:

```
Successful payment: 4242 4242 4242 4242
Failed payment: 4000 0000 0000 0002
```

#### 6.2 Shortening Trial for Testing
To test trial expiration quickly, modify the trial period:

```typescript
// In create-checkout/route.ts - for testing only
subscription_data: {
  trial_period_days: 1, // Expires in 1 day instead of 7
}
```

### 7. Customer Communication

#### 7.1 Trial Start Email
Consider setting up Stripe emails for:
- Welcome to trial
- Trial ending reminders (3 days, 1 day before)
- Successful conversion to paid

#### 7.2 Trial Status Display
The UI now shows:
- "Free Trial (Monthly)" in account menu
- "Trial ends: [date]" instead of billing date
- Clear indication of trial status

### 8. Business Logic

#### 8.1 Feature Access
Trial users have full access to:
- Script generation
- Audio synthesis  
- Project saving
- All voice models
- Cloud storage

#### 8.2 Usage Tracking
Trial usage is tracked the same as paid subscriptions. Consider:
- Monitoring trial user engagement
- Identifying conversion patterns
- Optimizing trial duration based on usage data

### 9. Environment Variables

No new environment variables required. Uses existing:
- `NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID`
- `NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID`
- `STRIPE_SECRET_KEY`
- `STRIPE_WEBHOOK_SECRET`

### 10. Production Deployment

Before deploying:

1. **Test thoroughly** in Stripe test mode
2. **Verify webhook endpoints** handle trial events
3. **Update email templates** if using Stripe emails
4. **Monitor trial conversions** closely after launch

### 11. Troubleshooting

#### Common Issues:
- **Trial not starting**: Check Stripe logs for checkout session creation
- **User not getting access**: Verify webhook processing and metadata updates
- **Trial not ending**: Check subscription status in Stripe dashboard

#### Debug Commands:
```bash
# Check user metadata
console.log(user.unsafeMetadata)

# Check subscription in Stripe
stripe subscriptions retrieve sub_xxxxx

# Force sync subscription status  
POST /api/stripe/sync-status
```

## Benefits of This Implementation

1. **Low Friction**: No immediate payment required
2. **Full Experience**: Users get complete feature access
3. **Automatic Conversion**: Seamless transition to paid plan
4. **Clear Communication**: Users always know trial status
5. **Easy Management**: Users can cancel anytime through portal

## Future Enhancements

Consider adding:
- Trial extension for power users
- Different trial periods for different plans
- Trial-specific onboarding flow
- Advanced trial analytics
