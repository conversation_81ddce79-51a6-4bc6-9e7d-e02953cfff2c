import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
    }>
}

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
  };
}

// POST /api/projects/[projectId]/audio/cleanup - Clean up orphaned audio metadata
export async function POST(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;

    console.log(`🧹 Starting cleanup for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const cleanupResult = await cloudService.cleanupOrphanedAudioMetadata(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: {
        cleaned: cleanupResult.cleaned,
        errors: cleanupResult.errors,
        message: `Cleanup completed: ${cleanupResult.cleaned} orphaned metadata files removed${cleanupResult.errors.length > 0 ? ` with ${cleanupResult.errors.length} errors` : ''}`
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to cleanup audio metadata:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to cleanup audio metadata',
        code: error.code || 'CLEANUP_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
