/**
 * Email Notification Service for GDPR Data Export
 * Handles sending email notifications for data export completion
 */

export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export class EmailService {
  private static instance: EmailService;

  private constructor() {}

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Send data export completion email
   */
  async sendDataExportCompleteEmail(
    userEmail: string,
    userName: string,
    downloadUrl: string,
    expiresAt: string
  ): Promise<boolean> {
    try {
      console.log(`📧 EmailService: Preparing data export email for: ${userEmail}`);
      console.log(`📧 Parameters: userName="${userName}", downloadUrl="${downloadUrl}", expiresAt="${expiresAt}"`);
      
      const template = this.createDataExportTemplate(userName, downloadUrl, expiresAt);
      console.log(`📧 Template created: subject="${template.subject}"`);
      
      const result = await this.sendEmail({
        to: userEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      });
      
      console.log(`📧 Email send result: ${result}`);
      return result;
    } catch (error) {
      console.error('💥 Error sending data export email:', error);
      console.error('Email error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return false;
    }
  }

  /**
   * Send account deletion confirmation email
   */
  async sendAccountDeletionEmail(
    userEmail: string,
    userName: string,
    deletionDate: string
  ): Promise<boolean> {
    try {
      const template = this.createAccountDeletionTemplate(userName, deletionDate);
      
      return await this.sendEmail({
        to: userEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      });
    } catch (error) {
      console.error('Error sending account deletion email:', error);
      return false;
    }
  }

  /**
   * Create data export email template
   */
  private createDataExportTemplate(userName: string, downloadUrl: string, expiresAt: string) {
    const subject = 'Your WordWave Studio Data Export is Ready';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3b82f6; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
          .warning { background: #fef3cd; border: 1px solid #fde047; padding: 15px; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
          ul { padding-left: 20px; }
          li { margin: 8px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🔒 Your Data Export is Ready</h1>
        </div>
        
        <div class="content">
          <p>Hello ${userName || 'there'},</p>
          
          <p>Your personal data export from WordWave Studio has been completed and is ready for download.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${downloadUrl}" class="button">📥 Download Your Data</a>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important Security Notice:</strong><br>
            This download link will expire on <strong>${new Date(expiresAt).toLocaleDateString()}</strong> for security purposes.
          </div>
          
          <h3>📋 What's Included in Your Export:</h3>
          <ul>
            <li><strong>Account Information:</strong> Your profile, email addresses, and account settings</li>
            <li><strong>Project Data:</strong> All your projects, scripts, and configurations</li>
            <li><strong>Audio Files:</strong> All your generated audio files (.wav) plus metadata</li>
            <li><strong>Subscription Data:</strong> Billing history and subscription information</li>
            <li><strong>Usage Analytics:</strong> Your usage patterns and activity logs</li>
            <li><strong>Support Communications:</strong> Any support tickets or communications</li>
          </ul>
          
          <h3>📁 File Format:</h3>
          <p>Your data is provided as a ZIP archive containing JSON files and your original audio files. The main data file (<code>user-data.json</code>) contains all your information in a structured, machine-readable format.</p>
          
          <h3>🔐 Privacy & Security:</h3>
          <p>This export was generated specifically for you and contains sensitive personal information. Please:</p>
          <ul>
            <li>Store the downloaded file securely</li>
            <li>Do not share the download link with others</li>
            <li>The link will automatically expire after 7 days</li>
            <li>Delete the file securely when no longer needed</li>
          </ul>
          
          <p>If you have any questions about your data export or need assistance, please contact our privacy team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          
          <div class="footer">
            <p>Best regards,<br>
            <strong>WordWave Studio Privacy Team</strong></p>
            
            <p style="margin-top: 20px; font-size: 12px;">
              This email was sent in response to your GDPR data export request.<br>
              WordWave Studio • <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
    
    const text = `
${subject}

Hello ${userName || 'there'},

Your personal data export from WordWave Studio has been completed and is ready for download.

Download your data: ${downloadUrl}

IMPORTANT: This download link will expire on ${new Date(expiresAt).toLocaleDateString()} for security purposes.

What's included in your export:
• Account Information: Your profile, email addresses, and account settings
• Project Data: All your projects, scripts, and configurations  
• Audio Files: All your generated audio files (.wav) plus metadata
• Subscription Data: Billing history and subscription information
• Usage Analytics: Your usage patterns and activity logs
• Support Communications: Any support tickets or communications

Your data is provided as a ZIP archive containing JSON files and your original audio files.

Privacy & Security:
- Store the downloaded file securely
- Do not share the download link with others
- The link will automatically expire after 7 days
- Delete the file securely when no longer needed

If you have any questions, please contact our privacy <NAME_EMAIL>.

Best regards,
WordWave Studio Privacy Team
    `.trim();

    return { subject, html, text };
  }

  /**
   * Create account deletion email template
   */
  private createAccountDeletionTemplate(userName: string, deletionDate: string) {
    const subject = 'WordWave Studio Account Deletion Confirmed';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🗑️ Account Deletion Confirmed</h1>
        </div>
        
        <div class="content">
          <p>Hello ${userName || 'there'},</p>
          
          <p>This email confirms that your WordWave Studio account has been permanently deleted on ${new Date(deletionDate).toLocaleDateString()}.</p>
          
          <h3>What was deleted:</h3>
          <ul>
            <li>Your account profile and login credentials</li>
            <li>All project files and audio content</li>
            <li>Subscription and billing information</li>
            <li>Usage data and analytics</li>
            <li>All associated data across our systems</li>
          </ul>
          
          <p><strong>This action cannot be undone.</strong> If you wish to use WordWave Studio again in the future, you will need to create a new account.</p>
          
          <p>Thank you for using WordWave Studio. We're sorry to see you go!</p>
          
          <div class="footer">
            <p>WordWave Studio Privacy Team<br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `;
    
    const text = `
${subject}

Hello ${userName || 'there'},

This email confirms that your WordWave Studio account has been permanently deleted on ${new Date(deletionDate).toLocaleDateString()}.

What was deleted:
• Your account profile and login credentials
• All project files and audio content  
• Subscription and billing information
• Usage data and analytics
• All associated data across our systems

This action cannot be undone. If you wish to use WordWave Studio again in the future, you will need to create a new account.

Thank you for using WordWave Studio. We're sorry to see you go!

WordWave Studio Privacy Team
<EMAIL>
    `.trim();

    return { subject, html, text };
  }

  /**
   * Send email using configured email service
   */
  private async sendEmail(template: EmailTemplate): Promise<boolean> {
    try {
      console.log('📧 Email Service: Preparing to send email');
      console.log('To:', template.to);
      console.log('Subject:', template.subject);
      
      if (process.env.SENDER_API_KEY) {
        return await this.sendWithSender(template);
      } else if (process.env.RESEND_API_KEY) {
        return await this.sendWithResend(template);
      } else if (process.env.SENDGRID_API_KEY) {
        return await this.sendWithSendGrid(template);
      } else {
        console.log('📧 Email service not configured. Would send email:', {
          to: template.to,
          subject: template.subject,
          htmlLength: template.html.length
        });
        console.log('💡 To enable emails, add SENDER_API_KEY, RESEND_API_KEY, or SENDGRID_API_KEY to your environment variables');
        return true; // Return true for development
      }
      
    } catch (error) {
      console.error('❌ Error in email service:', error);
      return false;
    }
  }

  /**
   * Send email using Sender
   */
  private async sendWithSender(template: EmailTemplate): Promise<boolean> {
    try {
      console.log('🔧 Attempting to send email via Sender...');
      const axios = await import('axios');
      
      console.log('🔧 Creating Sender API request with key:', process.env.SENDER_API_KEY ? 'Present' : 'Missing');
      
      const senderData = {
        from: {
          email: process.env.FROM_EMAIL || '<EMAIL>',
          name: 'WordWave Studio'
        },
        to: [
          {
            email: template.to
          }
        ],
        subject: template.subject,
        html: template.html,
        text: template.text || template.html.replace(/<[^>]*>/g, '') // Strip HTML for text version
      };
      
      console.log('🔧 Preparing Sender email data:', {
        from: senderData.from.email,
        to: template.to,
        subject: template.subject,
        htmlLength: template.html.length
      });
      
      const response = await axios.default.post(
        'https://api.sender.net/v2/email',
        senderData,
        {
          headers: {
            'Authorization': `Bearer ${process.env.SENDER_API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );

      console.log('✅ Sender API response:', response.data);
      console.log(`✅ Email sent successfully via Sender to: ${template.to}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email via Sender - Full error:', error);
      if (error instanceof Error) {
        console.error('❌ Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }
      // Log response data if it's an axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        console.error('❌ Sender API error response:', axiosError.response?.data);
      }
      return false;
    }
  }

  /**
   * Send email using Resend
   */
  private async sendWithResend(template: EmailTemplate): Promise<boolean> {
    try {
      console.log('🔧 Attempting to import Resend...');
      const { Resend } = await import('resend');
      console.log('✅ Resend imported successfully');
      
      console.log('🔧 Creating Resend instance with API key:', process.env.RESEND_API_KEY ? 'Present' : 'Missing');
      const resend = new Resend(process.env.RESEND_API_KEY);
      
      const emailData = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: template.to,
        subject: template.subject,
        html: template.html,
      };
      
      console.log('🔧 Preparing email data:', {
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        htmlLength: emailData.html.length
      });
      
      console.log('📤 Sending email via Resend API...');
      const result = await resend.emails.send(emailData);

      console.log('✅ Resend API response:', JSON.stringify(result, null, 2));
      
      if (result.error) {
        console.error('❌ Resend API returned error:', result.error);
        console.error('❌ This might be due to unverified recipient email address');
        console.error('💡 Add the recipient email to verified recipients in Resend dashboard');
        return false;
      }
      
      if (result.data && result.data.id) {
        console.log(`✅ Email sent successfully via Resend! Email ID: ${result.data.id}`);
        console.log(`📧 Email sent to: ${template.to}`);
        return true;
      } else {
        console.warn('⚠️ Resend API response missing expected data:', result);
        console.warn('💡 This might indicate the email was rejected due to unverified recipient');
        return false;
      }
      
    } catch (error) {
      console.error('❌ Failed to send email via Resend - Full error:', error);
      if (error instanceof Error) {
        console.error('❌ Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }
      return false;
    }
  }

  /**
   * Send email using SendGrid
   */
  private async sendWithSendGrid(template: EmailTemplate): Promise<boolean> {
    try {
      const sgMail = await import('@sendgrid/mail');
      sgMail.default.setApiKey(process.env.SENDGRID_API_KEY!);
      
      const msg = {
        to: template.to,
        from: process.env.FROM_EMAIL || '<EMAIL>',
        subject: template.subject,
        html: template.html,
        text: template.text,
      };

      await sgMail.default.send(msg);
      console.log(`✅ Email sent successfully via SendGrid to: ${template.to}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email via SendGrid:', error);
      return false;
    }
  }

  /**
   * Test email connectivity (useful for debugging)
   */
  async testEmailService(): Promise<boolean> {
    try {
      console.log('🧪 Testing email service connectivity...');
      
      const testTemplate: EmailTemplate = {
        to: '<EMAIL>',
        subject: 'Email Service Test',
        html: '<h1>Test Email</h1><p>This is a test email to verify email service connectivity.</p>',
        text: 'Test Email - This is a test email to verify email service connectivity.'
      };
      
      // Test which email service would be used
      if (process.env.SENDER_API_KEY) {
        console.log('✅ Sender API key detected - would use Sender');
        console.log('📊 Sender offers 15,000 free emails/month');
      } else if (process.env.RESEND_API_KEY) {
        console.log('✅ Resend API key detected - would use Resend');
        console.log('📊 Resend offers 3,000 free emails/month');
      } else if (process.env.SENDGRID_API_KEY) {
        console.log('✅ SendGrid API key detected - would use SendGrid');
        console.log('📊 SendGrid offers 100 free emails/month');
      } else {
        console.log('⚠️ No email service configured - running in development mode');
        console.log('💡 Add SENDER_API_KEY, RESEND_API_KEY, or SENDGRID_API_KEY to enable email sending');
      }
      
      console.log('✅ Email service test completed');
      return true;
      
    } catch (error) {
      console.error('❌ Email service test failed:', error);
      return false;
    }
  }

  /**
   * Public method for sending emails (for GDPR account deletion)
   */
  async sendEmailPublic(template: EmailTemplate): Promise<boolean> {
    return await this.sendEmail(template);
  }
}
