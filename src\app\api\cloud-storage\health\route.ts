import { NextResponse } from 'next/server';
import { ApiResponse, CloudStorageError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

/**
 * GET /api/cloud-storage/health
 * Cloud storage service health check
 */
export async function GET() {
  try {
    // Check if required environment variables are present
    const requiredEnvVars = [
      'B2_APPLICATION_KEY_ID',
      'B2_APPLICATION_KEY',
      'B2_BUCKET_NAME'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new CloudStorageError(
        `Missing required environment variables: ${missingVars.join(', ')}`,
        500,
        'MISSING_ENV_VARS'
      );
    }

    // Test actual B2 connectivity
    const cloudService = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
      applicationKey: process.env.B2_APPLICATION_KEY!,
      bucketName: process.env.B2_BUCKET_NAME!
    });

    // Try to initialize the service (this will test B2 connectivity)
    await cloudService.initialize();

    const healthData = {
      status: 'healthy',
      service: 'Cloud Storage (Backblaze B2)',
      timestamp: new Date().toISOString(),
      configuration: {
        bucketName: process.env.B2_BUCKET_NAME,
        hasCredentials: !!(process.env.B2_APPLICATION_KEY_ID && process.env.B2_APPLICATION_KEY),
        connectionTested: true,
      },
    };

    const response: ApiResponse = {
      success: true,
      data: healthData,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('❌ Cloud storage health check failed:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Cloud storage health check failed',
        code: error.code || 'CLOUD_STORAGE_HEALTH_ERROR',
        details: error.details,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
