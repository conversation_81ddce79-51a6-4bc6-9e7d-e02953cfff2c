# Sender Email Service Integration Guide

## Overview

This guide documents how to integrate Sender email service into your Next.js application for reliable email delivery with generous limits. Sender offers 15,000 free emails per month compared to Resend's 3,000, making it an excellent choice for growing applications.

## Why Sender?

### Key Benefits:
- **15,000 free emails/month** (5x more than Resend)
- **99% deliverability rate**
- **Advanced analytics and tracking**
- **GDPR compliant**
- **Drag & drop email builder**
- **Real-time email validation**
- **A/B testing capabilities**
- **Excellent customer support**

### Pricing Comparison:
- **Sender Free**: 15,000 emails/month
- **Sender Standard**: $8/month for 60,000 emails
- **Resend Free**: 3,000 emails/month
- **Resend Pro**: $20/month for 50,000 emails

## Prerequisites

- Next.js application
- Sender account and API key
- Environment variables configured

## Setup Instructions

### 1. Create Sender Account

1. Sign up at [sender.net](https://www.sender.net/)
2. Verify your email address
3. Complete account setup

### 2. Get API Key

1. Go to Sender dashboard
2. Navigate to Settings → API & Webhooks
3. Click "Create API Key"
4. Copy your API key (starts with `sender_`)

### 3. Install Dependencies

```bash
npm install axios
```

### 4. Environment Configuration

Add these environment variables to your `.env.local` file:

```bash
# Sender Email Service Configuration
SENDER_API_KEY=sender_your_api_key_here
FROM_EMAIL=<EMAIL>  # Your verified domain email
```

#### Email Domain Setup:

**For Testing:**
- You can use any email address initially
- Sender allows testing without domain verification

**For Production:**
- Verify your domain in Sender dashboard
- Go to Settings → Domains
- Add your domain and complete DNS verification
- Use format: `<EMAIL>` or `<EMAIL>`

### 5. Email Service Implementation

The EmailService class automatically detects and prioritizes Sender:

```typescript
// Priority order:
// 1. Sender (if SENDER_API_KEY exists)
// 2. Resend (if RESEND_API_KEY exists)
// 3. SendGrid (if SENDGRID_API_KEY exists)
// 4. Development fallback (logs only)
```

## Usage Examples

### Basic Email Sending

```typescript
import { EmailService } from '@/lib/EmailService';

const emailService = EmailService.getInstance();

await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome to WordWave',
  html: '<h1>Welcome!</h1><p>Thanks for signing up.</p>',
  text: 'Welcome! Thanks for signing up.'
});
```

### Email Templates

Create reusable email templates:

```typescript
export class EmailTemplates {
  static welcomeEmail(userName: string, userEmail: string) {
    return {
      to: userEmail,
      subject: 'Welcome to WordWave Studio',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Welcome to WordWave</title>
        </head>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Welcome ${userName}!</h1>
          <p>Thanks for joining WordWave Studio. Get started by creating your first project.</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}" 
             style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Get Started
          </a>
        </body>
        </html>
      `,
      text: `Welcome ${userName}! Thanks for joining WordWave Studio. Get started at ${process.env.NEXT_PUBLIC_APP_URL}`
    };
  }

  static passwordResetEmail(userEmail: string, resetUrl: string) {
    return {
      to: userEmail,
      subject: 'Reset Your Password',
      html: `
        <h1>Password Reset Request</h1>
        <p>Click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link expires in 1 hour.</p>
      `,
      text: `Password Reset Request. Click this link to reset your password: ${resetUrl}. This link expires in 1 hour.`
    };
  }
}
```

### API Route Integration

Use in API routes for automated emails:

```typescript
// src/app/api/send-welcome/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { EmailService, EmailTemplates } from '@/lib/EmailService';

export async function POST(req: NextRequest) {
  try {
    const { userEmail, userName } = await req.json();
    
    const emailService = EmailService.getInstance();
    const template = EmailTemplates.welcomeEmail(userName, userEmail);
    
    const success = await emailService.sendEmail(template);
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Best Practices

### 1. Environment-Specific Configuration

```bash
# Development
SENDER_API_KEY=sender_test_key
FROM_EMAIL=<EMAIL>

# Production
SENDER_API_KEY=sender_live_key
FROM_EMAIL=<EMAIL>
```

### 2. Error Handling

- Always wrap email sending in try-catch blocks
- Log detailed errors for debugging
- Provide fallback behavior for development
- Return boolean success indicators

### 3. Rate Limiting

Sender has generous rate limits, but for high-volume applications:

```typescript
// Add rate limiting for bulk emails
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function sendBulkEmails(templates: EmailTemplate[]) {
  for (const template of templates) {
    await emailService.sendEmail(template);
    await delay(50); // 50ms delay between emails (optional)
  }
}
```

### 4. Email Validation

```typescript
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Use before sending
if (!isValidEmail(userEmail)) {
  throw new Error('Invalid email address');
}
```

## Testing

### 1. Development Testing

```typescript
// Test in development with console logs
console.log('Testing email service...');
const result = await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  html: '<p>This is a test</p>',
  text: 'This is a test'
});
console.log('Email sent:', result);
```

### 2. Using Test Script

Run the provided test script:

```bash
node test-sender-email.js
```

### 3. Sender Dashboard Monitoring

- Check https://app.sender.net/email/reports for sent emails
- Monitor delivery rates and bounces
- View email content and status
- Track opens and clicks

## Troubleshooting

### Common Issues:

1. **"Email not sent" but no errors:**
   - Check API key is correct
   - Verify FROM_EMAIL domain is authorized
   - Check Sender dashboard for delivery status

2. **401 Unauthorized:**
   - Verify SENDER_API_KEY is correct
   - Check API key hasn't expired
   - Ensure key has email sending permissions

3. **Domain verification:**
   - Add DNS records for your domain
   - Wait for verification to complete
   - Use a verified domain in FROM_EMAIL

4. **Rate limiting:**
   - Sender has generous limits (15,000/month free)
   - Add delays between bulk emails if needed
   - Monitor usage in dashboard

### Debug Logging:

Add detailed logging to troubleshoot:

```typescript
console.log('Email config:', {
  hasApiKey: !!process.env.SENDER_API_KEY,
  fromEmail: process.env.FROM_EMAIL,
  toEmail: template.to
});
```

## Production Checklist

- [ ] Sender API key configured
- [ ] Domain verified (if using custom domain)
- [ ] FROM_EMAIL set to verified domain
- [ ] Error handling implemented
- [ ] Email templates tested
- [ ] Rate limiting considered (optional with Sender's generous limits)
- [ ] Monitoring set up in Sender dashboard

## Integration Examples

This Sender setup can be extended for:

### Authentication Emails
- Welcome emails
- Password reset
- Email verification
- Account notifications

### Business Emails
- Order confirmations
- Subscription updates
- Support notifications
- Marketing campaigns

### System Emails
- GDPR data exports (implemented)
- System alerts
- Backup notifications
- Error reports

## Cost Considerations

Sender pricing (as of 2025):
- **Free tier**: 15,000 emails/month
- **Standard**: $8/month for 60,000 emails
- **Pro**: $16/month for 120,000 emails
- **Enterprise**: Custom pricing

Monitor usage in Sender dashboard to stay within limits.

## Sender vs. Competitors

| Feature | Sender | Resend | SendGrid |
|---------|--------|--------|----------|
| Free emails/month | 15,000 | 3,000 | 100 |
| Lowest paid plan | $8 (60k) | $20 (50k) | $15 (40k) |
| Email builder | ✅ | ❌ | ✅ |
| A/B testing | ✅ | ❌ | ✅ |
| Analytics | ✅ | ✅ | ✅ |
| API quality | ✅ | ✅ | ✅ |

## Summary

This Sender integration provides:
- ✅ 5x more free emails than Resend
- ✅ Reliable email delivery (99% rate)
- ✅ Easy development testing
- ✅ Production-ready error handling
- ✅ Scalable architecture
- ✅ Comprehensive logging
- ✅ Environment-specific configuration
- ✅ Advanced analytics and reporting

The implementation prioritizes Sender when available, falling back to Resend or SendGrid, ensuring maximum flexibility and reliability.
