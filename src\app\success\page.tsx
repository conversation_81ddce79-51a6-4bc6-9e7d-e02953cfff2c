'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import PageLayout from '@/components/PageLayout';
import Link from 'next/link';

function SuccessContent() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Give some time for webhooks to process
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-400 mx-auto"></div>
            <h1 className="text-3xl font-bold text-white">Processing your subscription...</h1>
            <p className="text-gray-300">
              Please wait while we set up your account. This should only take a moment.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="text-6xl">🎉</div>
            <h1 className="text-4xl md:text-5xl font-bold text-white">
              Welcome to WordWave Studio!
            </h1>
            <p className="text-xl text-gray-300">
              Your subscription has been successfully activated. You now have access to all premium features.
            </p>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-xl font-semibold text-white mb-4">What's Next?</h2>
              <ul className="space-y-2 text-gray-300 text-left">
                <li>• Set up your Google AI API key in your account settings</li>
                <li>• Create your first audio project</li>
                <li>• Explore all the premium voices and TTS models</li>
                <li>• Start generating amazing content!</li>
              </ul>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Start Creating
              </Link>
              <Link 
                href="/account"
                className="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Account Settings
              </Link>
            </div>
            
            {sessionId && (
              <p className="text-sm text-gray-400">
                Session ID: {sessionId}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function SuccessPage() {
  return (
    <PageLayout>
      <Suspense fallback={
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-gray-300 mt-4">Loading...</p>
          </div>
        </div>
      }>
        <SuccessContent />
      </Suspense>
    </PageLayout>
  );
}
