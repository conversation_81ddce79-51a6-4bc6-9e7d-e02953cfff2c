# Google Gemini TTS: Sound Effects & Annotation Rules

Google Gemini 2.5 TTS enables the synthesis of speech with realistic emotions, vocal effects, and sound annotations. However, its approach differs from traditional SSML-based text-to-speech services.

## Key Concepts

- **No SSML markup:** Gemini does **not** use `<speak>`, `<break>`, or other SSML tags.
- **Natural language prompts:** Style, emotion, and vocal effects are controlled by describing the speaking style or effect in plain language.

## Supported Vocal Effects

Gemini TTS can produce several non-speech vocalizations and stylings via prompt instructions:

- **Emotions/Tones:** angry, excited, sarcastic, empathetic, bored, cheerful, etc.
- **Vocal modulations:** whispering, shouting, tired, happy, etc.
- **Sound effects:** laughing, giggling, coughing, throat clearing, sighs, breathing sounds.

### Example Prompts

- `Say cheerfully: Have a wonderful day!`
- `Whisper softly: It's a secret.`
- `Say angrily: I can't believe this happened!`

```
Make Speaker1 sound tired and bored, and Speaker2 sound excited and happy:
Speaker1: So... what's on the agenda today?
Speaker2: You're never going to guess!
```

- You may also use square brackets for sound effects, e.g.
  - `[laughing]`, `[whispering]`, `[coughing]`
  - Results can be unpredictable; plain language is often preferred.

## Rules and Limitations

- **Describe effects/tones** at the start of the prompt or before dialogue.
- **Multi-speaker styling:** Use plain language to assign emotions to each speaker.
- **No background/environmental audio:** Only vocal effects described by prompt are supported.
- **Timing is implicit:** Sound effects may not occur exactly where described; experiment for best results.

## Supported Models

- Gemini 2.5 Flash Preview TTS
- Gemini 2.5 Pro Preview TTS

## Language Support

- 24 languages supported; mixing is allowed.
- Effects work for spoken content in supported languages.

---

*References:*
- Natural language prompt guidelines tested with Gemini 2.5 Pro TTS[21].
- Gemini API documentation and developer reports[1][3][6][21][24].
