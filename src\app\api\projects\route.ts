import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, ValidationError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service with singleton pattern
let cloudService: ServerCloudStorageService | null = null;
let initializationPromise: Promise<void> | null = null;

function getCloudService(): ServerCloudStorageService {
  if (!cloudService) {
    cloudService = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
      applicationKey: process.env.B2_APPLICATION_KEY!,
      bucketName: process.env.B2_BUCKET_NAME!
    });
  }
  return cloudService;
}

async function ensureCloudServiceInitialized(): Promise<void> {
  const service = getCloudService();

  // If already initialized, return immediately
  if (service['isAuthorized'] && service['bucketId']) {
    return;
  }

  // If initialization is in progress, wait for it
  if (initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  initializationPromise = service.initialize();

  try {
    await initializationPromise;
  } finally {
    initializationPromise = null;
  }
}

// POST /api/projects - Save project configuration only
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { projectData } = body;

    if (!projectData || !projectData.configuration) {
      throw new ValidationError('Project configuration is required');
    }

    console.log(`📤 Saving project configuration ${projectData.id} for user ${userId}`);

    // Ensure cloud storage service is initialized
    await ensureCloudServiceInitialized();
    const service = getCloudService();

    // Save only project configuration (no scripts or audio)
    const projectConfigOnly = {
      id: projectData.id,
      configuration: projectData.configuration,
      metadata: {
        ...projectData.metadata,
        userId,
        updatedAt: new Date().toISOString()
      }
    };

    const cloudProject = await service.saveProjectConfig(
      projectConfigOnly,
      userId
    );

    const response: ApiResponse = {
      success: true,
      data: {
        projectId: cloudProject.projectId,
        cloudPath: cloudProject.cloudPath,
        message: 'Project configuration saved successfully'
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('❌ Failed to save project configuration:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to save project configuration',
        code: error.code || 'SAVE_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// GET /api/projects - List user's projects
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    console.log(`📋 Listing projects for user ${userId}`);

    // Ensure cloud storage service is initialized
    await ensureCloudServiceInitialized();
    const service = getCloudService();

    const projects = await service.listProjects(userId);

    console.log(`✅ Found ${projects.length} projects for user ${userId}`);
    console.log(`📋 Project IDs: ${projects.map(p => p.projectId).join(', ')}`);

    const response: ApiResponse = {
      success: true,
      data: projects,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to list projects:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to list projects',
        code: error.code || 'LIST_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
