"use client";

import React, { useState, useRef, useEffect } from 'react';

interface AudioFile {
  id: string;
  name: string;
  url?: string;
  processedMimeType?: string;
  size?: number;
  duration?: number;
  createdAt?: string;
  voiceConfig?: {
    voice1?: string;
    voice2?: string;
  };
  // Enhanced metadata
  scriptContent?: string;
  scriptTopic?: string;
  generationId?: string;
  ttsModel?: string;
  synthesisMode?: 'monologue' | 'podcast';
}

interface AudioGalleryProps {
  audioFiles: AudioFile[];
  onDelete?: (audioId: string) => void;
  onRename?: (audioId: string, newName: string) => void;
  onRegenerate?: (audioFile: AudioFile) => void;
  onLoadScript?: (scriptContent: string) => void;
  className?: string;
}

const AudioGallery: React.FC<AudioGalleryProps> = ({
  audioFiles,
  onDelete,
  onRename,
  onRegenerate,
  onLoadScript,
  className = ''
}) => {
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState<{ [key: string]: number }>({});
  const [duration, setDuration] = useState<{ [key: string]: number }>({});
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({});

  const handleSaveScript = (scriptContent: string, filename: string) => {
    const blob = new Blob([scriptContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}_script.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const formatDuration = (seconds: number) => {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const kb = bytes / 1024;
    const mb = kb / 1024;
    if (mb >= 1) return `${mb.toFixed(1)} MB`;
    return `${kb.toFixed(1)} KB`;
  };

  const handlePlay = (audioId: string) => {
    // Pause all other audio
    Object.entries(audioRefs.current).forEach(([id, audio]) => {
      if (id !== audioId && !audio.paused) {
        audio.pause();
      }
    });
    
    const audio = audioRefs.current[audioId];
    if (audio) {
      if (audio.paused) {
        audio.play();
        setPlayingId(audioId);
      } else {
        audio.pause();
        setPlayingId(null);
      }
    }
  };

  const handleTimeUpdate = (audioId: string, audio: HTMLAudioElement) => {
    setCurrentTime(prev => ({
      ...prev,
      [audioId]: audio.currentTime
    }));
  };

  const handleLoadedMetadata = (audioId: string, audio: HTMLAudioElement) => {
    // Only set duration if it's a valid, finite number
    if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
      setDuration(prev => ({
        ...prev,
        [audioId]: audio.duration
      }));
    }
  };

  const handleEnded = (audioId: string) => {
    setPlayingId(null);
    setCurrentTime(prev => ({
      ...prev,
      [audioId]: 0
    }));
  };

  const handleSeek = (audioId: string, seekTime: number) => {
    const audio = audioRefs.current[audioId];
    if (audio) {
      audio.currentTime = seekTime;
      setCurrentTime(prev => ({
        ...prev,
        [audioId]: seekTime
      }));
    }
  };

  const handleProgressClick = (audioId: string, event: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) return; // Don't handle click if we're dragging

    const progressBar = event.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const progressWidth = rect.width;
    const clickRatio = Math.max(0, Math.min(1, clickX / progressWidth));

    const audioDuration = duration[audioId] && isFinite(duration[audioId])
      ? duration[audioId]
      : (audioFiles.find(f => f.id === audioId)?.duration && isFinite(audioFiles.find(f => f.id === audioId)!.duration!)
         ? audioFiles.find(f => f.id === audioId)!.duration! : 0);

    if (audioDuration > 0) {
      const seekTime = clickRatio * audioDuration;
      handleSeek(audioId, seekTime);
    }
  };

  const handleMouseDown = (audioId: string, event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(audioId);
    handleProgressClick(audioId, event);
  };

  const handleMouseMove = (audioId: string, event: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging === audioId) {
      handleProgressClick(audioId, event);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
  };

  // Add global mouse up listener
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(null);
    };

    if (isDragging) {
      document.addEventListener('mouseup', handleGlobalMouseUp);
      return () => {
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging]);

  const handleKeyDown = (audioId: string, event: React.KeyboardEvent<HTMLDivElement>) => {
    const audioDuration = duration[audioId] && isFinite(duration[audioId])
      ? duration[audioId]
      : (audioFiles.find(f => f.id === audioId)?.duration && isFinite(audioFiles.find(f => f.id === audioId)!.duration!)
         ? audioFiles.find(f => f.id === audioId)!.duration! : 0);

    if (audioDuration <= 0) return;

    const currentAudioTime = currentTime[audioId] || 0;
    let newTime = currentAudioTime;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        newTime = Math.max(0, currentAudioTime - 5); // Skip back 5 seconds
        break;
      case 'ArrowRight':
        event.preventDefault();
        newTime = Math.min(audioDuration, currentAudioTime + 5); // Skip forward 5 seconds
        break;
      case 'Home':
        event.preventDefault();
        newTime = 0; // Go to beginning
        break;
      case 'End':
        event.preventDefault();
        newTime = audioDuration; // Go to end
        break;
      default:
        return;
    }

    handleSeek(audioId, newTime);
  };

  const startEdit = (audioFile: AudioFile) => {
    setEditingId(audioFile.id);
    setEditName(audioFile.name.replace(/\.[^/.]+$/, '')); // Remove extension
  };

  const saveEdit = (audioId: string) => {
    if (onRename && editName.trim()) {
      const originalFile = audioFiles.find(f => f.id === audioId);
      const extension = originalFile?.name.split('.').pop() || 'wav';
      onRename(audioId, `${editName.trim()}.${extension}`);
    }
    setEditingId(null);
    setEditName('');
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditName('');
  };

  if (audioFiles.length === 0) {
    return (
      <div className={`audio-gallery-empty ${className}`}>
        <div className="empty-state">
          <div className="empty-icon">🎵</div>
          <h3>No Audio Files Yet</h3>
          <p>Generate some audio to see your creations here!</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`audio-gallery ${className}`}>
      <div className="gallery-header">
        <h3 className="gallery-title">
          <span className="gallery-icon">🎧</span>
          Audio Gallery ({audioFiles.length} {audioFiles.length === 1 ? 'file' : 'files'})
        </h3>
      </div>

      <div className="gallery-grid">
        {audioFiles.map((audioFile) => {
          const isPlaying = playingId === audioFile.id;

          // Calculate progress safely, handling invalid duration values
          const audioDuration = duration[audioFile.id] && isFinite(duration[audioFile.id])
            ? duration[audioFile.id]
            : (audioFile.duration && isFinite(audioFile.duration) ? audioFile.duration : 0);

          const progress = audioDuration > 0
            ? Math.min(100, Math.max(0, (currentTime[audioFile.id] || 0) / audioDuration * 100))
            : 0;

          // Calculate ARIA values outside JSX to avoid expression issues
          const audioDurationForAria = duration[audioFile.id] && isFinite(duration[audioFile.id])
            ? duration[audioFile.id]
            : (audioFile.duration && isFinite(audioFile.duration) ? audioFile.duration : 0);
          
          // Ensure ARIA values are always valid numbers (never NaN, undefined, or null)
          const maxValue = Number.isFinite(audioDurationForAria) && audioDurationForAria > 0 
            ? Math.max(1, Math.round(audioDurationForAria)) 
            : 100;
          const currentValue = Number.isFinite(currentTime[audioFile.id]) 
            ? Math.max(0, Math.round(currentTime[audioFile.id])) 
            : 0;
          const valueText = `${formatDuration(currentTime[audioFile.id] || 0)} of ${formatDuration(audioDurationForAria)}`;

          return (
            <div key={audioFile.id} className="audio-card">
              {/* Hidden audio element */}
              {audioFile.url && (
                <audio
                  ref={(el) => {
                    if (el) audioRefs.current[audioFile.id] = el;
                  }}
                  src={audioFile.url}
                  onTimeUpdate={(e) => handleTimeUpdate(audioFile.id, e.currentTarget)}
                  onLoadedMetadata={(e) => handleLoadedMetadata(audioFile.id, e.currentTarget)}
                  onEnded={() => handleEnded(audioFile.id)}
                  onPause={() => setPlayingId(null)}
                  preload="metadata"
                />
              )}

              {/* Card Header */}
              <div className="card-header">
                <div className="audio-info">
                  {editingId === audioFile.id ? (
                    <div className="edit-name">
                      <input
                        type="text"
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        className="name-input"
                        placeholder="Enter new name"
                        title="Enter new name"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') saveEdit(audioFile.id);
                          if (e.key === 'Escape') cancelEdit();
                        }}
                        autoFocus
                      />
                      <div className="edit-actions">
                        <button
                          type="button"
                          onClick={() => saveEdit(audioFile.id)}
                          className="save-btn"
                          title="Save"
                        >
                          ✓
                        </button>
                        <button
                          type="button"
                          onClick={cancelEdit}
                          className="cancel-btn"
                          title="Cancel"
                        >
                          ✕
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="audio-name">
                      <span className="name-text">{audioFile.name}</span>
                      {onRename && (
                        <button
                          type="button"
                          onClick={() => startEdit(audioFile)}
                          className="edit-btn"
                          title="Rename"
                        >
                          ✏️
                        </button>
                      )}
                    </div>
                  )}
                </div>

                <div className="card-actions">
                  {onLoadScript && audioFile.scriptContent && (
                    <button
                      type="button"
                      onClick={() => onLoadScript(audioFile.scriptContent!)}
                      className="control-btn load-script-btn"
                      title="Load script into editor"
                    >
                      📜
                    </button>
                  )}
                  {audioFile.scriptContent && (
                     <button
                        type="button"
                        onClick={() => handleSaveScript(audioFile.scriptContent!, audioFile.name)}
                        className="control-btn save-script-btn"
                        title="Save script to .txt file"
                      >
                        💾
                      </button>
                  )}
                  {onRegenerate && audioFile.scriptContent && (
                    <button
                      type="button"
                      onClick={() => onRegenerate(audioFile)}
                      className="control-btn regenerate-btn"
                      title="Regenerate audio from this script"
                    >
                      🔄
                    </button>
                  )}
                  {audioFile.url && (
                    <a
                      href={audioFile.url}
                      download={audioFile.name}
                      className="download-btn"
                      title="Download"
                    >
                      ⬇️
                    </a>
                  )}
                  {onDelete && (
                    <button
                      type="button"
                      onClick={() => onDelete(audioFile.id)}
                      className="control-btn delete-btn"
                      title="Delete"
                    >
                      🗑️
                    </button>
                  )}
                </div>
              </div>

              {/* Waveform Visualization */}
              <div className="waveform-container">
                <div className="waveform">
                  {Array.from({ length: 24 }, (_, i) => {
                    const waveHeight = Math.random() * 60 + 20;
                    const waveDelay = i * 0.1;
                    return (
                      <div
                        key={i}
                        className={`wave-bar ${isPlaying ? 'playing' : ''}`}
                        data-height={waveHeight}
                        data-delay={waveDelay}
                      />
                    );
                  })}
                </div>
                <div
                  className="progress-overlay"
                  data-progress={progress}
                />
              </div>

              {/* Interactive Progress Bar */}
              <div className="progress-bar-container">
                <div
                  className={`progress-bar-track ${isDragging === audioFile.id ? 'dragging' : ''}`}
                  onClick={(e) => handleProgressClick(audioFile.id, e)}
                  onMouseDown={(e) => handleMouseDown(audioFile.id, e)}
                  onMouseMove={(e) => handleMouseMove(audioFile.id, e)}
                  onMouseUp={handleMouseUp}
                  onKeyDown={(e) => handleKeyDown(audioFile.id, e)}
                  tabIndex={0}
                  role="slider"
                  aria-label="Audio progress"
                  aria-valuemin={0}
                  aria-valuemax={maxValue}
                  aria-valuenow={currentValue}
                  aria-valuetext={valueText}
                  title="Click, drag, or use arrow keys to seek (Left/Right: ±5s, Home/End: start/end)"
                  style={{
                    ['--progress-width' as any]: `${progress}%`,
                    ['--thumb-position' as any]: `${progress}%`
                  }}
                >
                  <div className="progress-bar-fill" />
                  <div className="progress-bar-thumb" />
                </div>
              </div>

              {/* Controls */}
              <div className="audio-controls">
                <button
                  type="button"
                  onClick={() => handlePlay(audioFile.id)}
                  className={`play-btn ${isPlaying ? 'playing' : ''}`}
                  disabled={!audioFile.url}
                  title={isPlaying ? 'Pause' : 'Play'}
                >
                  {isPlaying ? '⏸️' : '▶️'}
                </button>

                <div className="time-info">
                  <span className="current-time">
                    {formatDuration(currentTime[audioFile.id] || 0)}
                  </span>
                  <span className="separator">/</span>
                  <span className="total-time">
                    {/* Prioritize HTML audio element duration over metadata duration */}
                    {formatDuration(
                      duration[audioFile.id] && isFinite(duration[audioFile.id])
                        ? duration[audioFile.id]
                        : (audioFile.duration && isFinite(audioFile.duration) ? audioFile.duration : 0)
                    )}
                  </span>
                </div>
              </div>

              {/* Script Content Preview */}
              {audioFile.scriptContent && (
                <div className="script-preview">
                  <div className="script-header">
                    <span className="script-label">📜 Script Used:</span>
                    {audioFile.scriptTopic && (
                      <span className="script-topic">{audioFile.scriptTopic}</span>
                    )}
                  </div>
                  <div className="script-content">
                    {audioFile.scriptContent.length > 150
                      ? `${audioFile.scriptContent.substring(0, 150)}...`
                      : audioFile.scriptContent
                    }
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div className="audio-metadata">
                <div className="metadata-item">
                  <span className="metadata-label">Size:</span>
                  <span className="metadata-value">{formatFileSize(audioFile.size)}</span>
                </div>
                {audioFile.createdAt && (
                  <div className="metadata-item">
                    <span className="metadata-label">Created:</span>
                    <span className="metadata-value">
                      {new Date(audioFile.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {audioFile.voiceConfig && (
                  <div className="metadata-item">
                    <span className="metadata-label">Voices:</span>
                    <span className="metadata-value">
                      {audioFile.voiceConfig.voice1}
                      {audioFile.voiceConfig.voice2 && `, ${audioFile.voiceConfig.voice2}`}
                    </span>
                  </div>
                )}
                {audioFile.ttsModel && (
                  <div className="metadata-item">
                    <span className="metadata-label">Model:</span>
                    <span className="metadata-value">{audioFile.ttsModel}</span>
                  </div>
                )}
                {audioFile.synthesisMode && (
                  <div className="metadata-item">
                    <span className="metadata-label">Mode:</span>
                    <span className="metadata-value">
                      {audioFile.synthesisMode === 'podcast' ? 'Podcast (2 speakers)' : 'Monologue (1 speaker)'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AudioGallery;
