// API Client for WordWave Studio Backend
import React from 'react';
import { useAuth } from '@clerk/nextjs';
import {
  ProjectData,
  ProjectSummary,
  SaveProjectOptions,
  LoadProjectOptions,
  ListProjectsOptions
} from '../types/CloudStorage';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
  };
}

export class ApiClient {
  private baseUrl: string;
  private getToken: () => Promise<string | null>;

  constructor(baseUrl: string = '', getToken: () => Promise<string | null>) {
    this.baseUrl = baseUrl;
    this.getToken = getToken;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = await this.getToken();
      
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
          ...options.headers,
        },
      });

      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Save a project to cloud storage
   */
  async saveProject(
    projectData: ProjectData, 
    options?: SaveProjectOptions
  ): Promise<{ projectId: string; cloudPath: string; fileCount: number; message: string }> {
    const response = await this.request<{ projectId: string; cloudPath: string; fileCount: number; message: string }>(
      '/api/cloud-storage/projects',
      {
        method: 'POST',
        body: JSON.stringify({
          projectData,
          options
        })
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to save project');
    }

    return response.data;
  }

  /**
   * Load a project from cloud storage
   */
  async loadProject(projectId: string, options?: LoadProjectOptions): Promise<ProjectData> {
    const queryParams = new URLSearchParams();
    if (options?.includeAudio !== undefined) {
      queryParams.append('includeAudio', options.includeAudio.toString());
    }

    const endpoint = `/api/cloud-storage/projects/${projectId}${queryParams.toString() ? `?${queryParams}` : ''}`;
    
    const response = await this.request<ProjectData>(endpoint);

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to load project');
    }

    return response.data;
  }

  /**
   * List projects for the authenticated user
   */
  async listProjects(options?: ListProjectsOptions): Promise<{
    projects: ProjectSummary[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const queryParams = new URLSearchParams();
    if (options?.limit !== undefined) queryParams.append('limit', options.limit.toString());
    if (options?.offset !== undefined) queryParams.append('offset', options.offset.toString());
    if (options?.search) queryParams.append('search', options.search);
    if (options?.tags) {
      options.tags.forEach(tag => queryParams.append('tags', tag));
    }

    const endpoint = `/api/cloud-storage/projects${queryParams.toString() ? `?${queryParams}` : ''}`;
    
    const response = await this.request<{
      projects: ProjectSummary[];
      total: number;
      limit: number;
      offset: number;
    }>(endpoint);

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to list projects');
    }

    return response.data;
  }

  /**
   * Delete a project from cloud storage
   */
  async deleteProject(projectId: string, confirmDelete: boolean = false): Promise<{ message: string; projectId: string }> {
    const response = await this.request<{ message: string; projectId: string }>(
      `/api/cloud-storage/projects/${projectId}`,
      {
        method: 'DELETE',
        body: JSON.stringify({
          confirmDelete
        })
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to delete project');
    }

    return response.data;
  }

  /**
   * Check health of cloud storage service
   */
  async checkHealth(): Promise<{ status: string; service: string; timestamp: string }> {
    const response = await this.request<{ status: string; service: string; timestamp: string }>(
      '/api/cloud-storage/health'
    );

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Health check failed');
    }

    return response.data;
  }
}

/**
 * React hook to get an authenticated API client
 */
export const useApiClient = (): ApiClient => {
  const { getToken } = useAuth();

  // Use useMemo to prevent creating new instance on every render
  return React.useMemo(() => {
    return new ApiClient(
      '', // Use relative paths for Next.js API routes
      getToken
    );
  }, [getToken]);
};
