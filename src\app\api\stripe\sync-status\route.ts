import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import Stripe from 'stripe';

export async function POST(request: NextRequest) {
  try {
    // Initialize Stripe inside the handler to avoid build-time issues
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const stripeCustomerId = user.unsafeMetadata?.stripeCustomerId as string;

    if (!stripeCustomerId) {
      console.log('No Stripe customer ID found for user. Setting status to inactive.');
      
      // Update Clerk metadata to reflect inactive status
      await (await clerkClient()).users.updateUserMetadata(user.id, {
        unsafeMetadata: {
          ...user.unsafeMetadata,
          subscriptionStatus: 'inactive',
          planType: null,
          subscriptionId: null,
          currentPeriodEnd: null,
          trialEnd: null,
          lastPaymentDate: null,
          lastSyncDate: new Date().toISOString(),
        },
      });

      return NextResponse.json({
        success: true,
        status: 'inactive',
        message: 'No Stripe customer ID. Status set to inactive.',
      });
    }

    console.log('🔄 Syncing subscription status for customer:', stripeCustomerId);

    // Get all subscriptions for this customer
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      status: 'all',
      limit: 10,
    });

    // Get all completed payments (for lifetime purchases)
    const payments = await stripe.paymentIntents.list({
      customer: stripeCustomerId,
      limit: 10,
    });

    // Check for an active or trialing subscription
    // Prioritize 'trialing' status
    let activeSubscription = subscriptions.data.find(sub => sub.status === 'trialing');
    if (!activeSubscription) {
      activeSubscription = subscriptions.data.find(sub => sub.status === 'active');
    }

    // Check for lifetime payment
    const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID;
    const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID;

    const lifetimePayment = payments.data.find(payment => 
      payment.status === 'succeeded' && 
      payment.metadata?.priceId === lifetimePriceId
    );

    let subscriptionStatus = 'inactive';
    let planType: 'monthly' | 'lifetime' | null = null;
    let subscriptionId: string | null = null;
    let currentPeriodEnd: Date | null = null;
    let lastPaymentDate: Date | null = null;
    let trialEnd: Date | null = null;

    if (activeSubscription) {
      // Monthly subscription is active or trialing
      subscriptionStatus = activeSubscription.status;
      planType = 'monthly';
      subscriptionId = activeSubscription.id;
      currentPeriodEnd = new Date((activeSubscription as any).current_period_end * 1000);
      trialEnd = activeSubscription.trial_end ? new Date(activeSubscription.trial_end * 1000) : null;
      lastPaymentDate = activeSubscription.latest_invoice 
        ? new Date((activeSubscription.latest_invoice as any).created * 1000)
        : null;
    } else if (lifetimePayment) {
      // Lifetime payment found
      subscriptionStatus = 'active';
      planType = 'lifetime';
      lastPaymentDate = new Date(lifetimePayment.created * 1000);
    }

    // Update Clerk metadata
    await (await clerkClient()).users.updateUserMetadata(user.id, {
      unsafeMetadata: {
        ...user.unsafeMetadata,
        subscriptionStatus,
        planType,
        subscriptionId,
        currentPeriodEnd: currentPeriodEnd?.toISOString() || null,
        trialEnd: trialEnd?.toISOString() || null,
        lastPaymentDate: lastPaymentDate?.toISOString() || null,
        lastSyncDate: new Date().toISOString(),
      },
    });

    console.log(`✅ Synced subscription status: ${subscriptionStatus} (${planType})`);
    if (trialEnd) {
      console.log(`🎯 Trial ends: ${trialEnd.toISOString()}`);
    }

    return NextResponse.json({
      success: true,
      status: subscriptionStatus,
      planType,
      subscriptionId,
      currentPeriodEnd: currentPeriodEnd?.toISOString() || null,
      trialEnd: trialEnd?.toISOString() || null,
      lastPaymentDate: lastPaymentDate?.toISOString() || null,
      message: `Subscription status synced: ${subscriptionStatus} ${planType ? `(${planType})` : ''}`,
    });

  } catch (error) {
    console.error('Error syncing subscription status:', error);
    return NextResponse.json(
      { error: 'Failed to sync subscription status' },
      { status: 500 }
    );
  }
}
