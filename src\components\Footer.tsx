import Link from "next/link";

const Footer = () => {
  return (
    <footer className="max-w-6xl mx-auto mt-12 bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-8">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
        <div className="space-y-4 col-span-2 md:col-span-1">
          <h2 className="text-2xl font-bold text-white">WordWave Studio</h2>
          <p className="text-zinc-400">
            AI-powered podcast and audio content creation platform.
          </p>
        </div>
        <div>
          <h3 className="text-white font-semibold">Product</h3>
          <ul className="mt-4 space-y-2">
            <li>
              <Link href="/features" className="text-zinc-400 hover:text-white">
                Features
              </Link>
            </li>
            <li>
              <Link
                href="/how-it-works"
                className="text-zinc-400 hover:text-white"
              >
                How It Works
              </Link>
            </li>
            <li>
              <Link href="/pricing" className="text-zinc-400 hover:text-white">
                Pricing
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="text-white font-semibold">Support</h3>
          <ul className="mt-4 space-y-2">
            <li>
              <Link
                href="/documentation"
                className="text-zinc-400 hover:text-white"
              >
                Documentation
              </Link>
            </li>
            <li>
              <Link
                href="/help-center"
                className="text-zinc-400 hover:text-white"
              >
                Help Center
              </Link>
            </li>
            <li>
              <Link href="/contact" className="text-zinc-400 hover:text-white">
                Contact
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="text-white font-semibold">Company</h3>
          <ul className="mt-4 space-y-2">
            <li>
              <Link href="/about" className="text-zinc-400 hover:text-white">
                About
              </Link>
            </li>
            <li>
              <a 
                href="https://www.youtube.com/@WordWaveStudio/videos" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-zinc-400 hover:text-white flex items-center gap-1"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
                Voice Samples
              </a>
            </li>
            <li>
              <Link href="/privacy" className="text-zinc-400 hover:text-white">
                Privacy Policy
              </Link>
            </li>
            <li>
              <Link href="/privacy-dashboard" className="text-zinc-400 hover:text-white">
                Privacy Dashboard
              </Link>
            </li>
            <li>
              <Link href="/terms" className="text-zinc-400 hover:text-white">
                Terms
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="mt-8 border-t border-zinc-700/40 pt-8 text-center text-zinc-400">
        <div className="space-y-1">
          <div>&copy; {new Date().getFullYear()} WordWave Studio. All rights reserved.</div>
          <div className="text-sm">
            PJE Trading | ABN: **************
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
