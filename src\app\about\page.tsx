import React from 'react';
import PageLayout from '@/components/PageLayout';

export default function AboutPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            About WordWave Studio
          </h1>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 mb-12">
            <p className="text-xl text-gray-300 leading-relaxed text-center">
              WordWave Studio is revolutionizing audio content creation by combining the power of 
              artificial intelligence with intuitive design. We make it possible for anyone to 
              create professional-quality podcasts, monologues, and audio content without the 
              traditional barriers of expensive equipment or technical expertise.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Our Mission</h2>
              <p className="text-gray-300 leading-relaxed">
                To democratize audio content creation by providing powerful AI tools that enable 
                creators, educators, businesses, and storytellers to bring their ideas to life 
                through high-quality audio experiences.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Our Vision</h2>
              <p className="text-gray-300 leading-relaxed">
                A world where compelling audio content is accessible to everyone, where ideas 
                flow freely from mind to microphone, and where the only limit to audio storytelling 
                is imagination itself.
              </p>
            </div>
          </div>
          
          <div className="mb-12">
            <h2 className="text-3xl font-semibold text-white mb-8 text-center">What Sets Us Apart</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Lightning Fast</h3>
                <p className="text-gray-300">
                  Generate professional audio content in minutes, not hours or days.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">AI-Powered</h3>
                <p className="text-gray-300">
                  Cutting-edge AI models create engaging, natural-sounding content.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">User-Friendly</h3>
                <p className="text-gray-300">
                  Intuitive interface designed for creators of all skill levels.
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 mb-12">
            <h2 className="text-3xl font-semibold text-white mb-6 text-center">Our Story</h2>
            <div className="space-y-4 text-gray-300 leading-relaxed">
              <p>
                WordWave Studio was born from a simple observation: creating quality audio content 
                shouldn't require a professional studio, expensive equipment, or years of technical training. 
                Our founders, passionate about both technology and storytelling, saw an opportunity to 
                bridge the gap between powerful AI capabilities and creative expression.
              </p>
              <p>
                What started as an experiment in AI-powered content creation has evolved into a 
                comprehensive platform that serves thousands of creators worldwide. From educators 
                creating engaging learning materials to entrepreneurs building their brand through 
                podcasting, WordWave Studio empowers diverse voices to share their stories.
              </p>
              <p>
                Today, we continue to push the boundaries of what's possible in AI-assisted content 
                creation, always with our users' creative vision at the center of everything we build.
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-white mb-4">Ready to Create?</h2>
            <p className="text-gray-300 mb-6">
              Join thousands of creators who are already using WordWave Studio to bring their ideas to life.
            </p>
            <a 
              href="/app" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get Started Free
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
