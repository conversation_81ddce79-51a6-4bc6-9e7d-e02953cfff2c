'use client';

import React from 'react';
import PageLayout from '@/components/PageLayout';
import InfoBanner from '@/components/InfoBanner';
import { createCheckoutSession, useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { useUser } from '@clerk/nextjs';

export default function PricingPage() {
  const { user } = useUser();
  const subscription = useSubscriptionStatus();

  const handleSubscribe = async (plan: 'monthly' | 'lifetime') => {
    if (!user) {
      alert('Please sign in to subscribe');
      return;
    }

    try {
      const priceId = plan === 'monthly' 
        ? process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID!
        : process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID!;
      
      await createCheckoutSession(priceId);
    } catch (error) {
      console.error('Subscription error:', error);
      alert('Failed to start checkout. Please try again.');
    }
  };

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            Pricing Plans
          </h1>
          <p className="text-xl text-gray-300 text-center mb-4">
            Start creating for free with your own Google API key, upgrade for advanced features
          </p>
          <div className="text-center mb-12">
            <InfoBanner
              type="info"
              icon="💡"
              message=""
              className="max-w-2xl mx-auto"
            >
              <p className="text-sm">
                <strong>Get Started Free:</strong> Basic text-to-speech works immediately with your free Google API key.
                Subscriptions unlock cloud storage, project management, and priority support.
              </p>
            </InfoBanner>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Features Info */}
            <div className="md:col-span-2 mb-8">
              <InfoBanner
                type="success"
                title="Always Free Features"
                icon="🆓"
                message=""
                className="p-6"
              >
                <div className="grid md:grid-cols-2 gap-4 mt-3">
                  <ul className="space-y-2 text-green-200 text-sm">
                    <li>• Basic text-to-speech synthesis</li>
                    <li>• 30+ Google AI voices</li>
                    <li>• Multi-speaker conversations</li>
                    <li>• Local audio downloads</li>
                  </ul>
                  <ul className="space-y-2 text-green-200 text-sm">
                    <li>• Your own Google API quota</li>
                    <li>• No monthly limits</li>
                    <li>• Script editing tools</li>
                    <li>• Audio effects support</li>
                  </ul>
                </div>
                <p className="text-green-300 text-xs mt-3">
                  💡 Just bring your free Google AI API key and start creating immediately!
                </p>
              </InfoBanner>
            </div>

            {/* Monthly Plan */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Monthly Pro</h2>
              <div className="text-4xl font-bold text-white mb-2">
                $4.99<span className="text-lg font-normal text-gray-300">/month</span>
              </div>
              <div className="text-green-400 text-sm mb-6 font-medium">
                🎯 7-day free trial included
              </div>
              <p className="text-gray-300 text-sm mb-4">Everything in Free, plus:</p>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  AI-powered script generation
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Cloud storage & project management
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Advanced premium features
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Priority customer support
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Access to latest Google AI models
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  WAV & MP3 export formats
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Cancel anytime
                </li>
              </ul>
              <button 
                onClick={() => handleSubscribe('monthly')}
                disabled={subscription.isLoading || (subscription.isActive && subscription.planType === 'monthly')}
                className={`w-full font-semibold py-3 px-6 rounded-lg transition-colors duration-200 ${
                  subscription.isActive && subscription.planType === 'monthly'
                    ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {subscription.isActive && subscription.planType === 'monthly' 
                  ? 'Current Plan' 
                  : 'Start 7-Day Free Trial'}
              </button>
            </div>
            
            {/* Lifetime Plan */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border-2 border-green-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Best Value
                </span>
              </div>
              <h2 className="text-2xl font-semibold text-white mb-4">Lifetime Pro</h2>
              <div className="text-4xl font-bold text-white mb-6">
                $29.99<span className="text-lg font-normal text-gray-300"> once</span>
              </div>
              <p className="text-gray-300 text-sm mb-4">Everything in Free, plus:</p>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  All Monthly Pro features
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <strong>Pay once, use forever</strong>
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  All future updates included
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Priority customer support
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-400 font-semibold">Save $30 vs 12 months</span>
                </li>
              </ul>
              <button 
                onClick={() => handleSubscribe('lifetime')}
                disabled={subscription.isLoading || (subscription.isActive && subscription.planType === 'lifetime')}
                className={`w-full font-semibold py-3 px-6 rounded-lg transition-colors duration-200 ${
                  subscription.isActive && subscription.planType === 'lifetime'
                    ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {subscription.isActive && subscription.planType === 'lifetime' 
                  ? 'Current Plan' 
                  : 'Get Lifetime Access'}
              </button>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 max-w-3xl mx-auto">
              <h3 className="text-xl font-semibold text-white mb-4">🔑 Bring Your Own Key (BYOK) Model</h3>
              <p className="text-gray-300 mb-4">
                WordWave Studio uses a BYOK approach - you provide your own Google AI API key, giving you:
              </p>
              <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-300">
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Direct control over API costs
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No artificial usage limits
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Maximum privacy & security
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Access to latest Google AI models
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-center mt-16">
            <p className="text-gray-300 mb-4">
              All monthly plans include a 7-day free trial. Or start creating immediately with just your Google API key.
            </p>
            <a 
              href="/app" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get Started Free
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
