'use client';

import React, { useState, useRef, useEffect } from 'react';
import { UserButton, useUser } from '@clerk/nextjs';
import { useSubscription } from '@/hooks/useSubscription';
import { useSubscriptionStatus, createCheckoutSession, openCustomerPortal, syncSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

interface AccountMenuProps {
  className?: string;
}

export const AccountMenu: React.FC<AccountMenuProps> = ({ className = '' }) => {
  const { user } = useUser();
  const { usage, syncUsage } = useSubscription();
  const subscriptionStatus = useSubscriptionStatus();
  const [isOpen, setIsOpen] = useState(false);
  const [isSyncingUsage, setIsSyncingUsage] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getUsageColor = (value: number) => {
    // Simple color coding based on activity level
    if (value >= 100) return 'text-green-400';
    if (value >= 50) return 'text-blue-400';
    if (value >= 10) return 'text-yellow-400';
    return 'text-gray-400';
  };

  if (!user) return null;

  return (
    <div className={`relative ${className}`} ref={menuRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 sm:gap-3 bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-2 sm:p-3 hover:bg-slate-700/50 transition-colors"
      >
        <UserButton
          appearance={{
            elements: {
              avatarBox: "w-6 h-6 sm:w-8 sm:h-8",
            },
          }}
        />
        <div className="text-left hidden sm:block">
          <div className="text-white text-sm font-medium">
            {user.firstName || user.emailAddresses[0]?.emailAddress}
          </div>
          <div className="text-gray-400 text-xs">
            {usage.scripts} scripts • {usage.audioGeneration} audio
          </div>
        </div>
        <div className="text-left sm:hidden">
          <div className="text-white text-xs font-medium truncate max-w-[80px]">
            {user.firstName || user.emailAddresses[0]?.emailAddress?.split('@')[0]}
          </div>
          <div className="text-gray-400 text-xs">
            {usage.scripts} scripts
          </div>
        </div>
        <svg
          className={`w-3 h-3 sm:w-4 sm:h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 sm:right-0 left-1/2 sm:left-auto transform -translate-x-1/2 sm:translate-x-0 top-full mt-2 w-80 max-w-[calc(100vw-2rem)] bg-slate-800 border border-slate-700 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <UserButton 
                appearance={{
                  elements: {
                    avatarBox: "w-10 h-10",
                  },
                }}
              />
              <div>
                <div className="text-white font-medium">
                  {user.firstName} {user.lastName}
                </div>
                <div className="text-gray-400 text-sm">
                  {user.emailAddresses[0]?.emailAddress}
                </div>
              </div>
            </div>

            <div className="border-t border-slate-700 pt-4">
              <h4 className="text-white font-medium mb-3">📊 Usage This Month</h4>
              
              {/* Simple Usage Display - No Limits */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Scripts Generated</span>
                  <span className={`text-sm font-medium ${getUsageColor(usage.scripts)}`}>
                    {usage.scripts}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Audio Generated</span>
                  <span className={`text-sm font-medium ${getUsageColor(usage.audioGeneration)}`}>
                    {usage.audioGeneration}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Tokens Used</span>
                  <span className={`text-sm font-medium ${getUsageColor(usage.tokens)}`}>
                    {usage.tokens.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Sync Usage Button */}
              <div className="mt-3 pt-3 border-t border-slate-700/50">
                <button
                  onClick={async () => {
                    try {
                      setIsSyncingUsage(true);
                      console.log('🔄 Manual usage sync triggered');
                      await syncUsage();
                      console.log('✅ Manual usage sync completed');
                    } catch (error) {
                      console.error('❌ Manual usage sync failed:', error);
                    } finally {
                      setIsSyncingUsage(false);
                    }
                  }}
                  disabled={isSyncingUsage}
                  className={`w-full text-xs font-medium py-2 px-3 rounded transition-colors border ${
                    isSyncingUsage
                      ? 'bg-gray-600/20 text-gray-500 border-gray-600/30 cursor-not-allowed'
                      : 'bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 border-blue-600/30'
                  }`}
                >
                  {isSyncingUsage ? '🔄 Syncing...' : '🔄 Sync Usage with Storage'}
                </button>
                <p className="text-gray-500 text-xs mt-1 text-center">
                  Refresh usage data
                </p>
              </div>

              {/* Educational Links */}
              <div className="mt-4 pt-3 border-t border-slate-700/50">
                <div className="space-y-2">
                  <a
                    href="https://console.cloud.google.com/apis/dashboard"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-blue-400 hover:text-blue-300 text-xs transition-colors"
                  >
                    <span>📈</span>
                    <span>View detailed usage & billing →</span>
                  </a>
                  <a
                    href="https://ai.google.dev/gemini-api/docs/pricing"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-gray-400 hover:text-gray-300 text-xs transition-colors"
                  >
                    <span>💡</span>
                    <span>Understanding API costs →</span>
                  </a>
                </div>
              </div>
            </div>

            {/* Subscription Section */}
            <div className="border-t border-slate-700 pt-4">
              <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                💳 Subscription
              </h4>

              {!subscriptionStatus.isActive ? (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-amber-400">⚠️</span>
                    <span className="text-amber-300 text-sm">No Active Subscription</span>
                  </div>
                  <p className="text-gray-400 text-xs mb-3">
                    Subscribe to generate scripts and audio content
                  </p>
                  <div className="flex flex-col gap-2 mb-3">
                    <button
                      onClick={() => createCheckoutSession(process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID!)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
                    >
                      Monthly - $4.99/mo
                    </button>
                    <button
                      onClick={() => createCheckoutSession(process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID!)}
                      className="w-full bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
                    >
                      Lifetime - $29.99
                    </button>
                  </div>
                  <button
                    onClick={syncSubscriptionStatus}
                    className="w-full text-xs bg-slate-600 hover:bg-slate-500 text-white py-1 px-2 rounded transition-colors"
                    title="If you just completed a payment, click here to refresh your subscription status"
                  >
                    🔄 Sync Status
                  </button>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">✅</span>
                      <span className="text-green-300 text-sm">
                        {subscriptionStatus.isTrialing 
                          ? `Free Trial (${subscriptionStatus.planType === 'lifetime' ? 'Lifetime' : 'Monthly'})`
                          : subscriptionStatus.planType === 'lifetime' ? 'Lifetime Plan' : 'Monthly Plan'}
                      </span>
                    </div>
                    <button
                      onClick={() => openCustomerPortal(user?.primaryEmailAddress?.emailAddress)}
                      className="bg-slate-600 hover:bg-slate-500 text-white text-xs py-1 px-2 rounded transition-colors"
                    >
                      Manage
                    </button>
                  </div>
                  <p className="text-gray-400 text-xs">
                    {subscriptionStatus.isTrialing && subscriptionStatus.trialEnd
                      ? `Trial ends: ${new Date(subscriptionStatus.trialEnd).toLocaleDateString()}`
                      : subscriptionStatus.planType === 'lifetime' 
                        ? 'Enjoy unlimited access to all features'
                        : subscriptionStatus.currentPeriodEnd 
                          ? `Next billing: ${new Date(subscriptionStatus.currentPeriodEnd).toLocaleDateString()}`
                          : 'Active subscription'
                    }
                  </p>
                </div>
              )}
            </div>


          </div>
        </div>
      )}
    </div>
  );
};
