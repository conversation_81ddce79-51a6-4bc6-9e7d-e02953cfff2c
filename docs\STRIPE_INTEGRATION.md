# Stripe Payment Integration Guide

## Overview
This document outlines the complete Stripe payment integration implemented in WordWave Studio, including subscription management, webhooks, and customer portal functionality.

## Table of Contents
1. [Stripe Dashboard Setup](#stripe-dashboard-setup)
2. [Environment Configuration](#environment-configuration)
3. [Payment Flow Implementation](#payment-flow-implementation)
4. [Webhook Implementation](#webhook-implementation)
5. [Customer Portal Integration](#customer-portal-integration)
6. [Subscription Status Management](#subscription-status-management)
7. [UI Integration](#ui-integration)
8. [Testing Process](#testing-process)
9. [Deployment Considerations](#deployment-considerations)
10. [Troubleshooting](#troubleshooting)

---

## 1. Stripe Dashboard Setup

### Products and Prices Created
1. **Monthly Subscription**
   - Product Name: "WordWave Studio Monthly"
   - Price: $4.99 USD
   - Billing: Monthly recurring
   - Price ID: `price_1RrzP8B7AgNnIs3i4tHoMluA`

2. **Lifetime Access**
   - Product Name: "WordWave Studio Lifetime"
   - Price: $29.99 USD
   - Billing: One-time payment
   - Price ID: `price_1RrzQFB7AgNnIs3iQ1pZ63NS`

### Webhook Endpoints
#### Development (Stripe CLI)
- **URL**: `http://localhost:3000/api/stripe/webhook`
- **Secret**: `whsec_3bb80f2227a234d74df7f425b2ceca823f9b520b2a370bcfaa95627f28ad5d60`
- **Command**: `stripe listen --forward-to localhost:3000/api/stripe/webhook`

#### Production (AWS - To be configured)
- **URL**: `https://your-domain.com/api/stripe/webhook`
- **Events**: 
  - `checkout.session.completed`
  - `payment_intent.succeeded`
  - `customer.subscription.created`
  - `customer.subscription.updated`
  - `customer.subscription.deleted`
  - `invoice.payment_succeeded`

### Customer Portal Configuration
- **Portal URL**: `https://billing.stripe.com/p/login/test_aFa5kC4VkbNwdsSbAe2VG00`
- **Features Enabled**:
  - Update payment methods
  - View invoices
  - Cancel subscriptions
  - Download receipts

---

## 2. Environment Configuration

### Environment Variables (.env)
```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RayEhB7AgNnIs3i4ntm8COBAgXDO7HjXH1pqH9sHvPdYLeIdjg5rDJxdpWd2V9hOwcXBHiMQbZBiRJ6sFM1FsmJ00Uh9We3iq
STRIPE_SECRET_KEY=sk_test_51RayEhB7AgNnIs3i4uQuRScSkHe5p1ky3MpsuXWo5m3sIoNKIzv8GhpNPL1Lq6GfzixhghhOHZQ27vbVJGRVshXo00scMA0o3N

# Webhook Secret - ENVIRONMENT SPECIFIC
# LOCAL (Stripe CLI): Use the secret from `stripe listen --forward-to localhost:3000/api/stripe/webhook`
# PRODUCTION (AWS): Create webhook in Stripe Dashboard pointing to https://yourdomain.com/api/stripe/webhook
STRIPE_WEBHOOK_SECRET=whsec_3bb80f2227a234d74df7f425b2ceca823f9b520b2a370bcfaa95627f28ad5d60

# Stripe Price IDs
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_1RrzP8B7AgNnIs3i4tHoMluA  # Includes 7-day free trial
NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID=price_1RrzQFB7AgNnIs3iQ1pZ63NS

# Stripe Customer Portal URL (for fallback)
NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/test_aFa5kC4VkbNwdsSbAe2VG00
```

---

## 3. Payment Flow Implementation

### 3.1 Checkout Session Creation
**File**: `src/app/api/stripe/create-checkout/route.ts`

**Features**:
- Creates or retrieves existing Stripe customer
- Stores customer ID in Clerk metadata
- Handles both subscription and one-time payment modes
- Automatic mode detection based on price ID

**Key Logic**:
```typescript
// Determine the mode based on the price ID
const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID;
const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID;

let mode: 'subscription' | 'payment';
if (priceId === monthlyPriceId) {
  mode = 'subscription'; // Monthly recurring subscription
} else if (priceId === lifetimePriceId) {
  mode = 'payment'; // One-time lifetime payment
}
```

### 3.2 Success Page
**File**: `src/app/success/page.tsx`

**Purpose**: Redirect page after successful payment with session verification.

### Free Trial Implementation

The monthly subscription plan includes a 7-day free trial. This is implemented in the `create-checkout` API route.

**File**: `src/app/api/stripe/create-checkout/route.ts`

When a user selects the monthly subscription, the following code is executed to add a trial period to the Stripe checkout session:

```typescript
// Add trial period for monthly subscriptions
if (mode === 'subscription') {
  sessionConfig.subscription_data = {
    trial_period_days: 7, // 7-day free trial
    metadata: {
      clerkUserId: user.id,
      priceId: priceId,
    },
  };
}
```

This ensures that users are not charged for the first 7 days of their monthly subscription, giving them a chance to try out the service before committing.

---

## 4. Webhook Implementation

### 4.1 Webhook Handler
**File**: `src/app/api/stripe/webhook/route.ts`

**Supported Events**:
- `checkout.session.completed`: Updates user subscription status after payment
- `customer.subscription.updated`: Handles subscription changes
- `customer.subscription.deleted`: Handles subscription cancellations
- `invoice.payment_succeeded`: Confirms successful payments

**Key Features**:
- Webhook signature verification
- Automatic user metadata updates in Clerk
- Plan type detection (monthly vs lifetime)
- Error handling and logging

### 4.2 Subscription Status Update Logic
```typescript
// Plan type detection
const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID;
const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID;

let planType: 'monthly' | 'lifetime' | null = null;
if (priceId === lifetimePriceId) {
  planType = 'lifetime';
} else if (priceId === monthlyPriceId) {
  planType = 'monthly';
}
```

---

## 5. Customer Portal Integration

### 5.1 Portal Session API
**File**: `src/app/api/stripe/customer-portal/route.ts`

**Features**:
- Creates secure portal sessions
- Auto-login for existing customers
- Fallback to manual portal URL
- Automatic return URL to main app

### 5.2 Email Pre-fill Implementation
**Key Improvement**: Customer email is automatically included in portal access

**Implementation Details**:
- **Primary Method**: API-generated portal session (auto-login, no email needed)
- **Fallback Method**: Direct portal URL with email parameter for manual login

**Code Implementation**:
```typescript
// In useSubscriptionStatus.tsx - openCustomerPortal function
export const openCustomerPortal = async (userEmail?: string) => {
  try {
    // Primary: API-generated portal session (seamless auto-login)
    const response = await fetch('/api/stripe/customer-portal', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });
    
    const { url } = await response.json();
    window.open(url, '_blank'); // Opens directly to customer's portal
    
  } catch (error) {
    // Fallback: Manual portal with email pre-filled
    const portalUrl = process.env.NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL;
    const urlWithEmail = userEmail 
      ? `${portalUrl}?email=${encodeURIComponent(userEmail)}`
      : portalUrl;
    window.open(urlWithEmail, '_blank');
  }
};
```

### 5.3 Portal Access Methods
1. **Primary**: API-generated portal session (auto-login)
2. **Fallback**: Direct portal URL with email pre-fill

**User Experience**:
- **Best Case**: Click "Manage" → Instantly in their billing portal (no login required)
- **Fallback**: Click "Manage" → Portal login page with email already filled in

### 5.4 Portal Session Creation
```typescript
// API endpoint creates secure portal session
const portalSession = await stripe.billingPortal.sessions.create({
  customer: stripeCustomerId,
  return_url: `${request.nextUrl.origin}/`, // Returns to main app
});
```

---

## 6. Subscription Status Management

### 6.1 Status Hook
**File**: `src/hooks/useSubscriptionStatus.tsx`

**Features**:
- Real-time subscription status from Clerk metadata
- Loading states
- Plan type detection
- Expiration handling for monthly subscriptions

### 6.2 Manual Sync Functionality
**File**: `src/app/api/stripe/sync-status/route.ts`

**Purpose**: Manual subscription status synchronization for failed webhooks or development

**Process**:
1. Retrieves all Stripe subscriptions for customer
2. Checks for completed lifetime payments
3. Updates Clerk metadata
4. Forces page reload to reflect changes

---

## 7. UI Integration

### 7.1 Account Menu Integration
**File**: `src/components/AccountMenu.tsx`

**Features**:
- Subscription status display
- Upgrade buttons for non-subscribers
- Manage button for active subscribers
- Sync status functionality

### 7.2 Email Pre-fill Feature
**User Request**: "for new subscribers, i would like to use the portal and include their email somehow"

**Implementation**: 
- Customer email is passed from Clerk user object to portal functions
- Email is automatically included in both primary and fallback portal access methods

**Code Flow**:
```typescript
// In AccountMenu.tsx - Manage button click
<button
  onClick={() => openCustomerPortal(user?.primaryEmailAddress?.emailAddress)}
  className="bg-slate-600 hover:bg-slate-500 text-white text-xs py-1 px-2 rounded transition-colors"
>
  Manage
</button>

// In useSubscriptionStatus.tsx - Email handling
export const openCustomerPortal = async (userEmail?: string) => {
  // Primary: API session (email not needed - auto-login)
  // Fallback: Portal URL with email pre-filled for easy login
  const urlWithEmail = userEmail 
    ? `${portalUrl}?email=${encodeURIComponent(userEmail)}`
    : portalUrl;
};
```

### 7.3 Subscription Gating
**Files**: `src/app/page.tsx`

**Implementation**:
- Script generation gating
- Audio synthesis gating
- Clean subscription warnings
- Profile menu redirects

---

## 8. Testing Process

### 8.1 Local Development Testing
1. **Start Stripe CLI webhook listener**:
   ```bash
   stripe listen --forward-to localhost:3000/api/stripe/webhook
   ```

2. **Test payment flows**:
   - Monthly subscription checkout
   - Lifetime payment checkout
   - Webhook event processing
   - Customer portal access

3. **Use Stripe test cards**:
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`

### 8.2 Webhook Testing
- Monitor webhook events in terminal
- Verify user metadata updates in Clerk
- Test subscription status changes
- Verify portal access

---

## 9. Deployment Considerations

### 9.1 Environment Variables for Production
```bash
# Production Stripe Keys (when ready for live mode)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...

# Production webhook secret (from Stripe Dashboard)
STRIPE_WEBHOOK_SECRET=whsec_[NEW_PRODUCTION_SECRET]

# Production customer portal URL
NEXT_PUBLIC_STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/[LIVE_URL]
```

### 9.2 AWS Deployment Steps
1. **Create production webhook in Stripe Dashboard**
2. **Configure environment variables in AWS**
3. **Test webhook connectivity**
4. **Update price IDs if using live mode**
5. **Configure customer portal for production**

---

## 10. Troubleshooting

### 10.1 Common Issues

#### Webhook 400 Errors
- **Cause**: Incorrect webhook secret
- **Solution**: Update `STRIPE_WEBHOOK_SECRET` with correct value from Stripe CLI or Dashboard

#### Subscription Status Not Updating
- **Cause**: Webhook not processing correctly
- **Solution**: Use manual sync button or check webhook logs

#### Customer Portal Not Auto-Login
- **Cause**: Missing Stripe customer ID or API failure
- **Solution**: System falls back to manual portal with email pre-fill

#### Mode Mismatch Error
- **Cause**: Using payment mode with recurring price or vice versa
- **Solution**: Verify price ID mapping in checkout creation

### 10.2 Debug Tools
- Stripe CLI webhook monitoring
- Browser console for client-side errors
- Server logs for API responses
- Stripe Dashboard webhook logs

---

## 11. Key Files Summary

| File | Purpose |
|------|---------|
| `src/app/api/stripe/create-checkout/route.ts` | Checkout session creation |
| `src/app/api/stripe/webhook/route.ts` | Webhook event processing |
| `src/app/api/stripe/customer-portal/route.ts` | Portal session creation |
| `src/app/api/stripe/sync-status/route.ts` | Manual status synchronization |
| `src/hooks/useSubscriptionStatus.tsx` | Subscription status management |
| `src/components/AccountMenu.tsx` | UI subscription controls |
| `src/app/success/page.tsx` | Post-payment success page |

---

## 12. Security Considerations

- Webhook signature verification implemented
- Server-side API key usage
- Client-side publishable key usage
- Secure customer portal session creation
- Proper error handling without data exposure

---

*Last updated: August 3, 2025*
*Integration status: Development complete, ready for production deployment*
