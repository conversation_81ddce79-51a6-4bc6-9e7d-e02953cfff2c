#!/usr/bin/env node

/**
 * Script to update models.json with latest models from Google's API
 * Usage: node scripts/update-models.js [API_KEY]
 * 
 * For pricing information, see: https://ai.google.dev/gemini-api/docs/pricing
 */

const fs = require('fs').promises;
const path = require('path');

// Configuration
const MODELS_CONFIG_PATH = path.join(__dirname, '../src/config/models.json');
const API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models';

/**
 * Fetch models from Google's API
 */
async function fetchModelsFromAPI(apiKey) {
  console.log('Fetching models from Google API...');
  
  const response = await fetch(API_ENDPOINT, {
    headers: {
      'x-goog-api-key': apiKey,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch models: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  if (!data.models || !Array.isArray(data.models)) {
    throw new Error('Invalid response format from models API');
  }

  return data.models;
}

/**
 * Categorize a model based on its name
 */
function categorizeModel(modelName) {
  if (modelName.includes('pro')) return 'premium';
  if (modelName.includes('lite')) return 'lite';
  return 'standard';
}

/**
 * Filter and format models for our configuration
 * Only include Gemini and Gemma models
 */
function processModels(apiModels) {
  const allModels = apiModels.map(model => ({
    name: model.baseModelId || model.name,
    apiName: model.baseModelId || model.name,
    description: model.description || '',
    category: categorizeModel(model.baseModelId || model.name),
  }));

  // Filter script models (only Gemini and Gemma, exclude TTS and specialized models)
  const scriptModels = allModels.filter(model => {
    const modelName = model.apiName.toLowerCase();
    
    // Must be Gemini or Gemma model
    const isGeminiOrGemma = modelName.includes('gemini') || modelName.includes('gemma');
    
    // Exclude specialized models
    const isSpecialized = modelName.includes('tts') ||
                         modelName.includes('image-generation') ||
                         modelName.includes('embedding') ||
                         modelName.includes('live');
    
    return isGeminiOrGemma && !isSpecialized;
  });

  // Filter TTS models (only Gemini and Gemma with TTS capability)
  const ttsModels = allModels.filter(model => {
    const modelName = model.apiName.toLowerCase();
    
    // Must be Gemini or Gemma model with TTS capability
    const isGeminiOrGemma = modelName.includes('gemini') || modelName.includes('gemma');
    const isTTS = modelName.includes('tts');
    
    return isGeminiOrGemma && isTTS;
  });

  return { scriptModels, ttsModels };
}

/**
 * Create the updated configuration object
 */
function createConfig(scriptModels, ttsModels, voices) {
  return {
    scriptModels: scriptModels.sort((a, b) => a.apiName.localeCompare(b.apiName)),
    ttsModels: ttsModels.sort((a, b) => a.apiName.localeCompare(b.apiName)),
    voices: voices.sort((a, b) => a.apiName.localeCompare(b.apiName)),
    lastUpdated: new Date().toISOString(),
    source: 'api'
  };
}

/**
 * Get real voice data from Google's API - these are the actual supported voices
 * Voice names are derived from Google's official documentation and voice_samples directory
 */
async function getVoicesFromAPI() {
  // These are all 30 voices supported by Google's Gemini TTS API as per official documentation
  // Source: https://ai.google.dev/gemini-api/docs/speech-generation
  const googleVoices = [
    "Achernar", "Achird", "Algenib", "Algieba", "Alnilam", "Aoede", "Autonoe", "Callirrhoe", "Charon",
    "Despina", "Enceladus", "Erinome", "Fenrir", "Gacrux", "Iapetus", "Kore", "Laomedeia",
    "Leda", "Orus", "Puck", "Pulcherrima", "Rasalgethi", "Sadachbia", "Sadaltager", "Schedar",
    "Sulafat", "Umbriel", "Vindemiatrix", "Zephyr", "Zubenelgenubi"
  ];

  // Convert to our configuration format with only factual data
  // Google doesn't provide metadata like gender, age, accent through their API
  return googleVoices.map(voiceName => ({
    name: voiceName,
    apiName: voiceName.toLowerCase(),
    description: `${voiceName} voice for text-to-speech`,
    category: "standard" // All voices are standard category
    // Removed fabricated metadata: gender, accent, ageRange
    // These characteristics exist but aren't provided by Google's API
  }));
}

/**
 * Get voices configuration - now pulls from real API data
 */
async function getVoicesConfig() {
  return await getVoicesFromAPI();
}

/**
 * Main function
 */
async function main() {
  try {
    // Get API key from command line or environment
    const apiKey = process.argv[2] || process.env.GEMINI_API_KEY;
    
    if (!apiKey) {
      console.error('Error: API key is required');
      console.error('Usage: node scripts/update-models.js [API_KEY]');
      console.error('Or set GEMINI_API_KEY environment variable');
      process.exit(1);
    }

    // Fetch models from API
    const apiModels = await fetchModelsFromAPI(apiKey);
    console.log(`Found ${apiModels.length} models from API`);

    // Process and filter models
    const { scriptModels, ttsModels } = processModels(apiModels);
    console.log(`Processed ${scriptModels.length} script models and ${ttsModels.length} TTS models`);

    // Get voices configuration from real API data
    const voices = await getVoicesConfig();
    console.log(`Added ${voices.length} voice configurations (from Google's actual supported voices)`);

    // Create configuration
    const config = createConfig(scriptModels, ttsModels, voices);

    // Read existing config to preserve any manual customizations
    let existingConfig = {};
    try {
      const existingData = await fs.readFile(MODELS_CONFIG_PATH, 'utf8');
      existingConfig = JSON.parse(existingData);
      console.log('Loaded existing configuration');
    } catch (error) {
      console.log('No existing configuration found, creating new one');
    }

    // Write updated configuration
    await fs.writeFile(MODELS_CONFIG_PATH, JSON.stringify(config, null, 2), 'utf8');
    console.log(`Updated models configuration written to ${MODELS_CONFIG_PATH}`);

    // Show summary
    console.log('\nSummary:');
    console.log(`- Script models: ${scriptModels.length}`);
    scriptModels.forEach(model => console.log(`  • ${model.apiName}`));
    console.log(`- TTS models: ${ttsModels.length}`);
    ttsModels.forEach(model => console.log(`  • ${model.apiName}`));
    console.log(`- Voices: ${voices.length}`);
    voices.forEach(voice => console.log(`  • ${voice.apiName} (${voice.category})`));
    
    console.log('\n✅ Models configuration updated successfully!');
    console.log('Remember to restart your development server to see the changes.');

  } catch (error) {
    console.error('❌ Error updating models:', error.message);
    process.exit(1);
  }
}

// Only run if this script is executed directly
if (require.main === module) {
  main();
}
