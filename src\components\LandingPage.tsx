import React from 'react';
import './LandingPage.css';

const LandingPage: React.FC = () => {
  return (
    <>
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-logo">
            <h1>WordWave Studio</h1>
          </div>
          <div className="nav-links">
            <a href="/pricing">Pricing</a>
            <a href="/app" className="nav-link">🚀 Launch App</a>
            <a href="/app" className="cta-button-nav">Get Started Free</a>
          </div>
          <div className="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </nav>

      {/* Hero + 3 Steps Combined Section */}
      <section className="hero-steps-combined">
        <div className="container">
          <div className="hero-content-combined">
            <div className="hero-text-center">
              <h1 className="hero-title-simple">
                Create Professional Podcasts with AI
              </h1>
              <p className="hero-subtitle-simple">
                Generate engaging scripts and realistic speech synthesis in minutes. 
                No recording studio required.
              </p>
            </div>
            
            <div className="steps-header">
              <h2>Get Started in 3 Simple Steps</h2>
              <p>From signup to awesome content in minutes</p>
            </div>
            
            <div className="steps-container-simple">
              <div className="step-simple">
                <div className="step-number-large">1</div>
                <div className="step-icon">🔑</div>
                <h3>Get Your Free API Key</h3>
                <p>Visit <strong>aistudio.google.com</strong> and get your free Google AI API key. Google provides generous free usage for text-to-speech, so you can create plenty of content without extra costs.</p>
                <div className="step-cta">
                  <a href="https://aistudio.google.com" target="_blank" rel="noopener noreferrer" className="cta-button secondary">Get API Key</a>
                </div>
              </div>
              
              <div className="step-simple">
                <div className="step-number-large">2</div>
                <div className="step-icon">🚀</div>
                <h3>Start Creating</h3>
                <p>Sign up for free and start creating immediately! For advanced features like cloud storage and project management, consider our affordable <strong>Monthly Plan ($4.99/month)</strong> or <strong>Lifetime Plan ($29.99 once)</strong>.</p>
                <div className="step-cta">
                  <a href="/app" className="cta-button primary">Start Creating</a>
                </div>
              </div>
              
              <div className="step-simple">
                <div className="step-number-large">3</div>
                <div className="step-icon">🎵</div>
                <h3>Create Amazing Content</h3>
                <p>Generate professional podcasts, engaging monologues, and realistic conversations. Use 30+ natural-sounding voices to bring your scripts to life!</p>
                <div className="step-cta">
                  <a href="/app" className="cta-button primary">Start Creating</a>
                </div>
              </div>
            </div>
            
            {/* Main CTA */}
            <div className="hero-main-cta">
              <a href="/app" className="cta-button primary large">Get Started Free</a>
              <a href="/pricing" className="cta-button secondary large">View Pricing</a>
            </div>
            
            <p className="hero-cta-note">✅ Start instantly with your Google API key • 🔑 Free Google AI quota included • 🎯 30+ AI voices • ⚡ Generate content in minutes</p>
          </div>
        </div>
      </section>

      {/* Quick Demo Audio */}
      <section className="demo-audio-section">
        <div className="container">
          <div className="demo-audio-simple">
            <h3>🎧 Listen to a Sample</h3>
            <p>Here's what you can create with WordWave Studio:</p>
            <div className="youtube-playlist-container">
              <iframe
                width="560"
                height="315"
                src="https://www.youtube.com/embed/videoseries?list=PLKSpzvBdlcl4-ePxp0w2kp0jM3__Ml0bV&loop=1"
                title="WordWave Studio Sample Playlist"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                className="youtube-playlist"
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Simplified CTA Section */}
      <section className="cta-section-final">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Start Creating?</h2>
            <p>Join creators using AI to produce professional audio content</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content-simple">
            <div className="footer-brand">
              <h3>WordWave Studio</h3>
              <p>AI-powered podcast and audio content creation platform.</p>
            </div>
            <div className="footer-links-simple">
              <a href="/pricing">Pricing</a>
              <a 
                href="https://www.youtube.com/@WordWaveStudio/videos" 
                target="_blank" 
                rel="noopener noreferrer"
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <svg style={{ width: '16px', height: '16px' }} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
                Voice Samples
              </a>
              <a href="/privacy">Privacy</a>
              <a href="/terms">Terms</a>
              <a href="/contact">Contact</a>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; {new Date().getFullYear()} WordWave Studio. All rights reserved.</p>
            <p style={{ fontSize: '0.9rem', marginTop: '8px' }}>
              PJE Trading | ABN: **************
            </p>
          </div>
        </div>
      </footer>
    </>
  );
};

export default LandingPage;
