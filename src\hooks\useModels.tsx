// Hook for managing dynamic model loading
import { useState, useEffect } from 'react';
import { getAvailableModels, getScriptModels, getTTSModels, type ModelInfo } from '../services/ModelService';

interface UseModelsOptions {
  useAPI?: boolean;
  apiKey?: string;
  autoRefresh?: boolean;
}

interface UseModelsResult {
  scriptModels: ModelInfo[];
  ttsModels: ModelInfo[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
  refreshModels: () => Promise<void>;
}

export function useModels(options: UseModelsOptions = {}): UseModelsResult {
  const { useAPI = false, apiKey, autoRefresh = false } = options;
  
  const [scriptModels, setScriptModels] = useState<ModelInfo[]>([]);
  const [ttsModels, setTtsModels] = useState<ModelInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  const loadModels = async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const config = await getAvailableModels({
        useAPI,
        apiKey,
        forceRefresh,
      });

      setScriptModels(config.scriptModels);
      setTtsModels(config.ttsModels);
      setLastUpdated(config.lastUpdated);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load models';
      setError(errorMessage);
      console.error('Error loading models:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshModels = async () => {
    await loadModels(true);
  };

  useEffect(() => {
    loadModels();
  }, [useAPI, apiKey]);

  // Auto-refresh every 24 hours if enabled
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (useAPI && apiKey) {
        loadModels(true);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours

    return () => clearInterval(interval);
  }, [autoRefresh, useAPI, apiKey]);

  return {
    scriptModels,
    ttsModels,
    isLoading,
    error,
    lastUpdated,
    refreshModels,
  };
}
