// Client-side types for cloud storage operations

import { ProjectData, ProjectSummary } from '../lib/types';

export interface SaveProjectOptions {
  compress?: boolean;
  includeAudio?: boolean;
  tags?: string[];
  description?: string;
}

export interface LoadProjectOptions {
  includeAudio?: boolean;
}

export interface ListProjectsOptions {
  limit?: number;
  offset?: number;
  tags?: string[];
  search?: string;
}

export interface DeleteProjectOptions {
  confirmDelete?: boolean;
}

// Re-export commonly used types for convenience
export type { ProjectData, ProjectSummary };

// Cloud storage service interface
export interface CloudStorageServiceInterface {
  saveProject(projectData: ProjectData, options?: SaveProjectOptions): Promise<{
    projectId: string;
    cloudPath: string;
    fileCount: number;
    message: string;
  }>;
  
  loadProject(projectId: string, options?: LoadProjectOptions): Promise<ProjectData>;
  
  listProjects(options?: ListProjectsOptions): Promise<{
    projects: ProjectSummary[];
    total: number;
    limit: number;
    offset: number;
  }>;
  
  deleteProject(projectId: string, options?: DeleteProjectOptions): Promise<{
    message: string;
    projectId: string;
  }>;
  
  checkHealth(): Promise<{
    status: string;
    service: string;
    timestamp: string;
  }>;
}

// Upload progress tracking
export interface UploadProgressCallback {
  (progress: {
    fileName: string;
    bytesUploaded: number;
    totalBytes: number;
    percentage: number;
    status: 'pending' | 'uploading' | 'completed' | 'error';
    error?: string;
  }[]): void;
}

// Project organization utilities
export interface ProjectOrganizationOptions {
  sortBy?: 'name' | 'date' | 'size';
  sortOrder?: 'asc' | 'desc';
  filterBy?: {
    tags?: string[];
    dateRange?: {
      start: Date;
      end: Date;
    };
    sizeRange?: {
      min: number;
      max: number;
    };
  };
}

export interface ProjectSearchOptions {
  query: string;
  searchIn?: ('name' | 'description' | 'tags')[];
  caseSensitive?: boolean;
}
