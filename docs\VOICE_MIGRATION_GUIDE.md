# Migration Guide: From Hardcoded Voices to Dynamic Voice Management

This guide shows how to migrate existing components from the hardcoded voice system to the new dynamic voice management system.

## Migration Overview

### Before (Hardcoded)
```tsx
import { AVAILABLE_VOICES } from "@/lib/constants";

// Simple dropdown with hardcoded voices
<select value={voice} onChange={(e) => setVoice(e.target.value)}>
  {AVAILABLE_VOICES.map(voice => (
    <option key={voice.apiName} value={voice.apiName}>
      {voice.displayName}
    </option>
  ))}
</select>
```

### After (Dynamic)
```tsx
import VoiceSelector from "@/components/VoiceSelector";

// Enhanced selector with search, filtering, and metadata
<VoiceSelector
  value={voice}
  onChange={setVoice}
  showFilters={true}
  placeholder="Select a voice..."
/>
```

## Step-by-Step Migration

### 1. Simple Voice Dropdown

**Current Implementation:**
```tsx
// In src/app/page.tsx (around line 1480)
<select
  id="current-voice-1"
  value={currentVoice1}
  onChange={(e) => setCurrentVoice1(e.target.value)}
  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
>
  {AVAILABLE_VOICES.map(voice => (
    <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
  ))}
</select>
```

**New Implementation:**
```tsx
import VoiceSelector from "@/components/VoiceSelector";

<VoiceSelector
  value={currentVoice1}
  onChange={setCurrentVoice1}
  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
  placeholder="Select voice for Speaker 1..."
/>
```

### 2. Voice Selection with Categories

**New Implementation:**
```tsx
import { useVoices } from "@/hooks/useVoices";

function VoiceSelection() {
  const { voices } = useVoices();
  const [selectedVoice, setSelectedVoice] = useState('');

  // Group voices by category
  const premiumVoices = voices.filter(v => v.category === 'premium');
  const standardVoices = voices.filter(v => v.category === 'standard');

  return (
    <div>
      <h3>Premium Voices ⭐</h3>
      <select value={selectedVoice} onChange={(e) => setSelectedVoice(e.target.value)}>
        <option value="">Choose a premium voice...</option>
        {premiumVoices.map(voice => (
          <option key={voice.apiName} value={voice.apiName}>
            {voice.name} - {voice.description}
          </option>
        ))}
      </select>

      <h3>Standard Voices</h3>
      <select value={selectedVoice} onChange={(e) => setSelectedVoice(e.target.value)}>
        <option value="">Choose a standard voice...</option>
        {standardVoices.map(voice => (
          <option key={voice.apiName} value={voice.apiName}>
            {voice.name} - {voice.description}
          </option>
        ))}
      </select>
    </div>
  );
}
```

### 3. Voice Validation (Enhanced)

**Current Implementation:**
```tsx
// Basic validation
const validVoice1 = currentVoice1 && AVAILABLE_VOICES.some(v => v.apiName === currentVoice1)
  ? currentVoice1
  : project.defaultVoice1 || AVAILABLE_VOICES[0]?.apiName || 'zephyr';
```

**New Implementation:**
```tsx
import { useVoices } from "@/hooks/useVoices";

function useVoiceValidation() {
  const { voices } = useVoices();

  const validateVoice = useCallback((voiceApiName: string, fallback: string) => {
    // Check if voice exists in current configuration
    const voice = voices.find(v => v.apiName === voiceApiName);
    if (voice) return voiceApiName;

    // Try fallback
    const fallbackVoice = voices.find(v => v.apiName === fallback);
    if (fallbackVoice) return fallback;

    // Use first available voice
    return voices[0]?.apiName || 'zephyr';
  }, [voices]);

  return { validateVoice, voices };
}

// Usage
const { validateVoice } = useVoiceValidation();
const validVoice1 = validateVoice(currentVoice1, project.defaultVoice1);
```

## Specific Component Migrations

### 1. Main App Component (`src/app/page.tsx`)

**Replace these sections:**

```tsx
// Lines ~1480-1490 (Voice for Speaker 1)
<VoiceSelector
  value={currentVoice1}
  onChange={setCurrentVoice1}
  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
  placeholder="Select voice for Speaker 1..."
/>

// Lines ~1495-1505 (Voice for Speaker 2)
{project.synthesisMode === 'podcast' && (
  <VoiceSelector
    value={currentVoice2}
    onChange={setCurrentVoice2}
    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
    placeholder="Select voice for Speaker 2..."
  />
)}
```

### 2. Project Configuration

**Replace default voice dropdowns:**

```tsx
// Lines ~1450-1460 (Default Voice 1)
<VoiceSelector
  value={project.defaultVoice1}
  onChange={(value) => setProject(prev => ({ ...prev, defaultVoice1: value }))}
  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
  placeholder="Default voice for Speaker 1..."
  showFilters={true}
/>

// Lines ~1465-1475 (Default Voice 2)
{project.synthesisMode === 'podcast' && (
  <VoiceSelector
    value={project.defaultVoice2}
    onChange={(value) => setProject(prev => ({ ...prev, defaultVoice2: value }))}
    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
    placeholder="Default voice for Speaker 2..."
    showFilters={true}
  />
)}
```

## Testing the Migration

### 1. Functionality Tests

- ✅ Voice selection works in all dropdowns
- ✅ Voice validation still functions
- ✅ Default voices are preserved
- ✅ Audio generation uses correct voices

### 2. UI Tests

- ✅ Dropdowns show voice names and descriptions
- ✅ Premium voices are marked with ⭐
- ✅ Filtering works (if enabled)
- ✅ Search functionality works

### 3. Performance Tests

- ✅ Voice loading is fast
- ✅ No unnecessary re-renders
- ✅ Smooth filtering/searching

## Benefits After Migration

1. **Enhanced UX**: Users see descriptive voice names and descriptions
2. **Better Organization**: Voices are categorized and filterable  
3. **Future-Ready**: System ready for API-based voice discovery
4. **Maintainable**: Centralized voice configuration
5. **Scalable**: Easy to add new voices and metadata

## Rollback Plan

If issues arise, you can easily rollback:

1. **Keep old imports**: The `AVAILABLE_VOICES` constant still works
2. **Gradual rollback**: Revert components one by one
3. **Configuration fallback**: System automatically falls back to hardcoded values

```tsx
// Emergency fallback
import { AVAILABLE_VOICES } from "@/lib/constants";

// Use the old hardcoded system temporarily
<select>
  {AVAILABLE_VOICES.map(voice => (
    <option key={voice.apiName} value={voice.apiName}>
      {voice.displayName}
    </option>
  ))}
</select>
```

## Next Steps

1. **Migrate main components** (voice selectors in main app)
2. **Add voice previews** (future enhancement)
3. **Implement API discovery** (when available)
4. **Add user preferences** (remember favorite voices)
5. **Analytics integration** (track voice usage)

---

This migration provides immediate benefits while maintaining backward compatibility and preparing for future API-based voice discovery.
