@echo off
REM Update WordWave models and voices from Google's API
REM Usage: update-models.bat [API_KEY]

echo.
echo ========================================
echo WordWave Models & Voices Updater
echo ========================================
echo.

REM Check if API key is provided as argument
if "%~1"=="" (
    echo Error: API key is required!
    echo.
    echo Usage: update-models.bat YOUR_API_KEY
    echo.
    echo Example: update-models.bat AIzaSyABC123...
    echo.
    pause
    exit /b 1
)

echo Using API key: %~1
echo.
echo Fetching latest models and voices from Google's API...
echo.

REM Run the npm script with the provided API key
npm run update-models %~1

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ SUCCESS! Models updated successfully!
    echo ========================================
    echo.
    echo The models.json file has been updated with the latest models.
    echo Remember to restart your development server to see the changes.
    echo.
    echo Press any key to restart the dev server now, or Ctrl+C to exit...
    pause >nul
    
    echo.
    echo Stopping any existing dev servers...
    taskkill /F /IM node.exe >nul 2>&1
    
    echo Starting development server...
    npm run dev
) else (
    echo.
    echo ========================================
    echo ❌ ERROR: Failed to update models
    echo ========================================
    echo.
    echo Please check:
    echo - Your API key is valid
    echo - You have internet connection
    echo - Your Google AI Studio quota is not exceeded
    echo.
    pause
    exit /b %errorlevel%
)
