import React from 'react';
import PageLayout from '@/components/PageLayout';
import GDPRDataRights from '@/components/GDPRDataRights';
import { Shield, Lock, Eye, Settings } from 'lucide-react';

export default function PrivacyDashboardPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Shield className="w-12 h-12 text-blue-400" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                Privacy Dashboard
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Manage your data rights and privacy preferences in compliance with GDPR
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid md:grid-cols-4 gap-6 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Lock className="w-8 h-8 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Data Encrypted</h3>
              <p className="text-sm text-gray-300">All your data is encrypted at rest and in transit</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Eye className="w-8 h-8 text-blue-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Full Transparency</h3>
              <p className="text-sm text-gray-300">Request and download all your data anytime</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Settings className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Your Control</h3>
              <p className="text-sm text-gray-300">Manage what data we collect and how we use it</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Shield className="w-8 h-8 text-purple-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">GDPR Compliant</h3>
              <p className="text-sm text-gray-300">Fully compliant with European data protection laws</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10">
            <GDPRDataRights />
          </div>

          {/* Additional Information */}
          <div className="mt-12 bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
            <h2 className="text-2xl font-semibold text-white mb-6">Data Protection Information</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">What Data We Collect</h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• Account information (name, email)</li>
                  <li>• Project files and audio content</li>
                  <li>• Usage analytics and preferences</li>
                  <li>• Billing and subscription data</li>
                  <li>• Support communications</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">How We Protect Your Data</h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• End-to-end encryption</li>
                  <li>• Secure cloud storage (Backblaze B2)</li>
                  <li>• Regular security audits</li>
                  <li>• Minimal data collection principle</li>
                  <li>• Automatic data anonymization</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-8 pt-6 border-t border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">Your Rights Under GDPR</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Access</h4>
                  <p className="text-sm text-gray-300">Get a copy of all your personal data</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Rectification</h4>
                  <p className="text-sm text-gray-300">Correct inaccurate personal data</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Erasure</h4>
                  <p className="text-sm text-gray-300">Request deletion of your data</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Portability</h4>
                  <p className="text-sm text-gray-300">Transfer your data to another service</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Object</h4>
                  <p className="text-sm text-gray-300">Object to certain data processing</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Right to Restrict</h4>
                  <p className="text-sm text-gray-300">Limit how we process your data</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="mt-8 text-center">
            <p className="text-gray-300">
              Questions about your privacy or data rights? 
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 ml-1">
                Contact our Privacy Team
              </a>
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
