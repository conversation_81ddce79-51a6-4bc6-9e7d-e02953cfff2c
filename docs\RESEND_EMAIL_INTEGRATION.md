# Resend Email Service Integration Guide

## Overview

This guide documents how to integrate Resend email service into your Next.js application for reliable email delivery. This implementation is used for GDPR data export notifications and can be extended for other email features.

## Prerequisites

- Next.js application
- Resend account and API key
- Environment variables configured

## Setup Instructions

### 1. Install Resend Package

```bash
npm install resend
```

### 2. Environment Configuration

Add these environment variables to your `.env.local` file:

```bash
# Email Service Configuration
RESEND_API_KEY=re_your_resend_api_key_here
FROM_EMAIL=<EMAIL>  # For testing, or your verified domain
```

#### Email Domain Options:

**For Testing:**
- Use `<EMAIL>` - This is provided by Resend for immediate testing
- No domain verification required

**For Production:**
- Add and verify your own domain in Resend dashboard
- Use format: `<EMAIL>` or `<EMAIL>`
- Follow Resend's domain verification process

### 3. Email Service Implementation

Create a reusable email service class:

```typescript
// src/lib/EmailService.ts
export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export class EmailService {
  private static instance: EmailService;

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Send email using Resend with enhanced error handling
   */
  private async sendWithResend(template: EmailTemplate): Promise<boolean> {
    try {
      console.log('🔧 Attempting to import Resend...');
      const { Resend } = await import('resend');
      console.log('✅ Resend imported successfully');
      
      const resend = new Resend(process.env.RESEND_API_KEY);
      
      const result = await resend.emails.send({
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: template.to,
        subject: template.subject,
        html: template.html,
      });

      console.log('✅ Resend API response:', result);
      console.log(`✅ Email sent successfully via Resend to: ${template.to}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email via Resend:', error);
      if (error instanceof Error) {
        console.error('❌ Error details:', {
          message: error.message,
          name: error.name
        });
      }
      return false;
    }
  }

  /**
   * Main email sending method with fallback handling
   */
  async sendEmail(template: EmailTemplate): Promise<boolean> {
    if (process.env.RESEND_API_KEY) {
      return await this.sendWithResend(template);
    } else {
      console.log('📧 Email service not configured. Would send:', {
        to: template.to,
        subject: template.subject,
        htmlLength: template.html.length
      });
      console.log('💡 Add RESEND_API_KEY to enable email sending');
      return true; // Return true for development
    }
  }
}
```

## Usage Examples

### Basic Email Sending

```typescript
import { EmailService } from '@/lib/EmailService';

const emailService = EmailService.getInstance();

await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome to WordWave',
  html: '<h1>Welcome!</h1><p>Thanks for signing up.</p>'
});
```

### Email Templates

Create reusable email templates:

```typescript
export class EmailTemplates {
  static welcomeEmail(userName: string, userEmail: string) {
    return {
      to: userEmail,
      subject: 'Welcome to WordWave Studio',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Welcome to WordWave</title>
        </head>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Welcome ${userName}!</h1>
          <p>Thanks for joining WordWave Studio. Get started by creating your first project.</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}" 
             style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Get Started
          </a>
        </body>
        </html>
      `
    };
  }

  static passwordResetEmail(userEmail: string, resetUrl: string) {
    return {
      to: userEmail,
      subject: 'Reset Your Password',
      html: `
        <h1>Password Reset Request</h1>
        <p>Click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link expires in 1 hour.</p>
      `
    };
  }
}
```

### API Route Integration

Use in API routes for automated emails:

```typescript
// src/app/api/send-welcome/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { EmailService, EmailTemplates } from '@/lib/EmailService';

export async function POST(req: NextRequest) {
  try {
    const { userEmail, userName } = await req.json();
    
    const emailService = EmailService.getInstance();
    const template = EmailTemplates.welcomeEmail(userName, userEmail);
    
    const success = await emailService.sendEmail(template);
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Best Practices

### 1. Environment-Specific Configuration

```bash
# Development
RESEND_API_KEY=re_test_key
FROM_EMAIL=<EMAIL>

# Production
RESEND_API_KEY=re_live_key
FROM_EMAIL=<EMAIL>
```

### 2. Error Handling

- Always wrap email sending in try-catch blocks
- Log detailed errors for debugging
- Provide fallback behavior for development
- Return boolean success indicators

### 3. Rate Limiting

Resend has rate limits. For high-volume applications:

```typescript
// Add rate limiting for bulk emails
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function sendBulkEmails(templates: EmailTemplate[]) {
  for (const template of templates) {
    await emailService.sendEmail(template);
    await delay(100); // 100ms delay between emails
  }
}
```

### 4. Email Validation

```typescript
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Use before sending
if (!isValidEmail(userEmail)) {
  throw new Error('Invalid email address');
}
```

## Testing

### 1. Development Testing

```typescript
// Test in development with console logs
console.log('Testing email service...');
const result = await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  html: '<p>This is a test</p>'
});
console.log('Email sent:', result);
```

### 2. Resend Dashboard Monitoring

- Check https://resend.com/emails for sent emails
- Monitor delivery rates and bounces
- View email content and status

### 3. Email Preview

Use tools like:
- Resend's email preview feature
- Email testing services (Mailtrap, Email on Acid)
- Browser email preview extensions

## Troubleshooting

### Common Issues:

1. **"Email not sent" but no errors:**
   - Check API key is correct
   - Verify FROM_EMAIL domain is authorized
   - Check Resend dashboard for delivery status

2. **Dynamic import errors:**
   - Ensure `resend` package is installed
   - Check Next.js configuration allows dynamic imports

3. **Rate limiting:**
   - Add delays between bulk emails
   - Use Resend's batch API for large volumes

4. **Domain verification:**
   - Add DNS records for your domain
   - Wait for verification to complete
   - Use `<EMAIL>` for testing

### Debug Logging:

Add detailed logging to troubleshoot:

```typescript
console.log('Email config:', {
  hasApiKey: !!process.env.RESEND_API_KEY,
  fromEmail: process.env.FROM_EMAIL,
  toEmail: template.to
});
```

## Production Checklist

- [ ] Resend API key configured
- [ ] Domain verified (if using custom domain)
- [ ] FROM_EMAIL set to verified domain
- [ ] Error handling implemented
- [ ] Email templates tested
- [ ] Rate limiting considered
- [ ] Monitoring set up in Resend dashboard

## Integration Examples

This Resend setup can be extended for:

### Authentication Emails
- Welcome emails
- Password reset
- Email verification
- Account notifications

### Business Emails
- Order confirmations
- Subscription updates
- Support notifications
- Marketing campaigns

### System Emails
- GDPR data exports (implemented)
- System alerts
- Backup notifications
- Error reports

## Cost Considerations

Resend pricing (as of 2025):
- Free tier: 3,000 emails/month
- Pro: $20/month for 50,000 emails
- Scale: $85/month for 250,000 emails

Monitor usage in Resend dashboard to stay within limits.

---

## Summary

This Resend integration provides:
- ✅ Reliable email delivery
- ✅ Easy development testing
- ✅ Production-ready error handling
- ✅ Scalable architecture
- ✅ Comprehensive logging
- ✅ Environment-specific configuration

The implementation is battle-tested with the GDPR data export system and ready for use across your application.
