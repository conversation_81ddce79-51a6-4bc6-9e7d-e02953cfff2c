// Test All Email Services
// This script tests the email service configuration and providers

async function testEmailServiceConfiguration() {
  console.log('🧪 Testing email service configuration...\n');
  
  console.log('🔍 Environment Check:');
  console.log('SENDER_API_KEY:', process.env.SENDER_API_KEY ? '✅ Present' : '❌ Missing');
  console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? '✅ Present' : '❌ Missing');
  console.log('SENDGRID_API_KEY:', process.env.SENDGRID_API_KEY ? '✅ Present' : '❌ Missing');
  console.log('FROM_EMAIL:', process.env.FROM_EMAIL || '⚠️ Not set (will use default)');
  console.log('');
  
  console.log('📧 Email Provider Priority Order:');
  console.log('1. 🥇 Sender (15,000 free emails/month) - RECOMMENDED');
  console.log('2. 🥈 Resend (3,000 free emails/month)');
  console.log('3. 🥉 SendGrid (100 free emails/month)');
  console.log('4. 🔧 Development mode (logs only)\n');
  
  // Determine which provider would be used
  let activeProvider = 'Development mode (no emails sent)';
  let freeEmailsPerMonth = 0;
  
  if (process.env.SENDER_API_KEY) {
    activeProvider = 'Sender';
    freeEmailsPerMonth = 15000;
    console.log('🎯 ACTIVE PROVIDER: Sender');
    console.log('✅ You have the best email service configured!');
    console.log('📊 Free emails per month: 15,000');
    console.log('💰 Cost effective: $8/month for 60,000 emails');
  } else if (process.env.RESEND_API_KEY) {
    activeProvider = 'Resend';
    freeEmailsPerMonth = 3000;
    console.log('🎯 ACTIVE PROVIDER: Resend');
    console.log('✅ Good email service configured');
    console.log('📊 Free emails per month: 3,000');
    console.log('💡 Consider upgrading to Sender for 5x more free emails');
  } else if (process.env.SENDGRID_API_KEY) {
    activeProvider = 'SendGrid';
    freeEmailsPerMonth = 100;
    console.log('🎯 ACTIVE PROVIDER: SendGrid');
    console.log('⚠️ Limited free tier');
    console.log('📊 Free emails per month: 100');
    console.log('💡 Consider upgrading to Sender for 150x more free emails');
  } else {
    console.log('🎯 ACTIVE PROVIDER: Development mode');
    console.log('⚠️ No email service configured');
    console.log('💡 Add SENDER_API_KEY to get started with 15,000 free emails/month');
  }
  
  console.log('\n📋 Setup Instructions:');
  
  if (!process.env.SENDER_API_KEY) {
    console.log('\n🚀 To set up Sender (recommended):');
    console.log('1. Sign up at https://www.sender.net/');
    console.log('2. Go to Settings → API & Webhooks');
    console.log('3. Create an API key');
    console.log('4. Add to .env.local: SENDER_API_KEY=sender_your_key_here');
    console.log('5. Add to .env.local: FROM_EMAIL=<EMAIL>');
  }
  
  if (!process.env.RESEND_API_KEY && !process.env.SENDER_API_KEY) {
    console.log('\n📧 Alternative - Resend:');
    console.log('1. Sign up at https://resend.com');
    console.log('2. Create an API key');
    console.log('3. Add to .env.local: RESEND_API_KEY=re_your_key_here');
  }
  
  console.log('\n🧪 Testing Complete!');
  console.log(`📈 Current configuration provides ${freeEmailsPerMonth} free emails per month`);
  
  return {
    activeProvider,
    freeEmailsPerMonth,
    hasEmailConfigured: !!(process.env.SENDER_API_KEY || process.env.RESEND_API_KEY || process.env.SENDGRID_API_KEY)
  };
}

// Run the test
testEmailServiceConfiguration()
  .then((result) => {
    console.log('\n🎉 Email service configuration test completed!');
    if (result.hasEmailConfigured) {
      console.log(`✅ Ready to send emails using ${result.activeProvider}`);
    } else {
      console.log('⚠️ Set up an email service to start sending emails');
    }
  })
  .catch((error) => {
    console.error('\n💥 Configuration test failed:', error);
  });
