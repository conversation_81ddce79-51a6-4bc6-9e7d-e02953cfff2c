# WordWave Hostinger Deployment Script (PowerShell)
# This script helps prepare your Next.js app for Hostinger deployment

Write-Host "🚀 WordWave Deployment Preparation Script" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Error: package.json not found. Make sure you're in the project root directory." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install

# Build the application
Write-Host "🔨 Building the application..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed. Please fix the errors and try again." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build successful!" -ForegroundColor Green

# Create deployment package (optional)
Write-Host "📦 Creating deployment package..." -ForegroundColor Yellow

# Create a list of files to include in deployment
$filesToInclude = @(
    ".next",
    "public",
    "package.json",
    "package-lock.json",
    "next.config.ts"
)

# Check if files exist
foreach ($file in $filesToInclude) {
    if (-not (Test-Path $file)) {
        Write-Host "⚠️  Warning: $file not found" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "✅ Deployment preparation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps for Hostinger Deployment:" -ForegroundColor Cyan
Write-Host "1. Upload the following files/folders to your Hostinger server:" -ForegroundColor White
foreach ($file in $filesToInclude) {
    Write-Host "   - $file" -ForegroundColor Gray
}
Write-Host "2. Run 'npm install --production' on the server" -ForegroundColor White
Write-Host "3. Set up your environment variables (.env.local)" -ForegroundColor White
Write-Host "4. Start the application with 'npm start' or PM2" -ForegroundColor White
Write-Host ""
Write-Host "📖 See docs/HOSTINGER_DEPLOYMENT.md for detailed instructions" -ForegroundColor Cyan
