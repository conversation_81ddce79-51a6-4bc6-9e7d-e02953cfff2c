# Backblaze B2 Cloud Storage Integration Guide

## Overview
This document outlines the complete Backblaze B2 cloud storage integration implemented in WordWave Studio for storing and managing user-generated audio files. This integration provides secure, scalable, and cost-effective file storage with direct upload/download capabilities.

## Table of Contents
1. [Backblaze B2 Account Setup](#backblaze-b2-account-setup)
2. [Environment Configuration](#environment-configuration)
3. [Service Implementation](#service-implementation)
4. [API Endpoints](#api-endpoints)
5. [Frontend Integration](#frontend-integration)
6. [File Management Features](#file-management-features)
7. [Security Implementation](#security-implementation)
8. [Testing Process](#testing-process)
9. [Deployment Considerations](#deployment-considerations)
10. [Troubleshooting](#troubleshooting)

---

## 1. Backblaze B2 Account Setup

### 1.1 Account Creation and Configuration
1. **Create Backblaze B2 Account**: [https://www.backblaze.com/b2/cloud-storage.html](https://www.backblaze.com/b2/cloud-storage.html)
2. **Navigate to App Keys**: [https://secure.backblaze.com/app_keys.htm](https://secure.backblaze.com/app_keys.htm)
3. **Create Application Key**:
   - Key Name: `WordWaveStudio-Production` (or appropriate name)
   - Capabilities: `listBuckets`, `listFiles`, `readFiles`, `shareFiles`, `writeFiles`, `deleteFiles`
   - Bucket Access: Specific bucket (recommended) or All buckets

### 1.2 Bucket Configuration
**Bucket Details**:
- **Bucket Name**: `WordWaveStudio`
- **Bucket Type**: Private (recommended for user-generated content)
- **Lifecycle Rules**: Configure based on your data retention needs
- **CORS Rules**: Configure for web access if needed

### 1.3 Application Keys Generated
```
Application Key ID: 00573578cb83fa20000000006
Application Key: K005CSLJv16JIIYc/9cs4HHZgGRhDCY
```
⚠️ **Security Note**: These are example values. Use your actual keys and keep them secure.

---

## 2. Environment Configuration

### 2.1 Environment Variables (.env)
```bash
# Backblaze B2 Configuration (Server-side - secure, not exposed to browser)
# Replace these with your actual Backblaze B2 credentials
# Get them from: https://secure.backblaze.com/app_keys.htm
B2_APPLICATION_KEY_ID=00573578cb83fa20000000006
B2_APPLICATION_KEY=K005CSLJv16JIIYc/9cs4HHZgGRhDCY
B2_BUCKET_NAME=WordWaveStudio
```

### 2.2 Security Configuration
- **Server-side Only**: B2 credentials are never exposed to the browser
- **Environment Specific**: Different credentials for development/staging/production
- **Scope Limited**: Application keys have minimal required permissions

---

## 3. Service Implementation

### 3.1 CloudStorageService Class
**File**: `src/lib/CloudStorageService.ts`

**Purpose**: Core service class for all B2 operations with comprehensive error handling and logging.

#### Key Methods

**`initialize()`**

This method authorizes with Backblaze B2 and ensures that the configured bucket exists. It's called before any other operations are performed.

```typescript
async initialize(): Promise<void> {
  try {
    await this.authorize();
    await this.ensureBucket();
  } catch (error) {
    console.error('❌ Failed to initialize CloudStorageService:', error);
    throw new CloudStorageError(
      'Failed to initialize cloud storage service',
      500,
      'INIT_ERROR',
      { originalError: error }
    );
  }
}
```

**`saveProject()`**

This method saves a project to Backblaze B2. It uploads the project's metadata as a `project.json` file and any associated audio files.

```typescript
async saveProject(
  projectData: ProjectData,
  userId: string,
  onProgress?: (progress: UploadProgress[]) => void
): Promise<CloudProject> {
  // ... implementation details ...
}
```

**`loadProject()`**

This method loads a project from Backblaze B2 by downloading and parsing the `project.json` file.

```typescript
async loadProject(projectId: string, userId: string): Promise<ProjectData> {
  // ... implementation details ...
}
```

**`deleteProject()`**

This method deletes a project from Backblaze B2, including all associated files.

```typescript
async deleteProject(projectId: string, userId: string): Promise<void> {
  // ... implementation details ...
}
```

### 3.2 Type Definitions
**File**: `src/types/CloudStorage.ts`

```typescript
export interface UploadResult {
  fileId: string;
  fileName: string;
  fileUrl: string;
  contentLength: number;
  contentType: string;
  uploadTimestamp: string;
}

export interface FileInfo {
  fileId: string;
  fileName: string;
  size: number;
  uploadTimestamp: string;
  contentType: string;
}

export interface CloudStorageConfig {
  applicationKeyId: string;
  applicationKey: string;
  bucketName: string;
}
```

---

## 4. API Endpoints

### 4.1 File Upload Endpoint
**File**: `src/app/api/cloud-storage/upload/route.ts`

**Features**:
- Handles multipart file uploads
- User authentication validation
- File type validation
- Automatic file naming with user context
- Progress tracking capabilities
- Error handling and validation

**Implementation**:
```typescript
export async function POST(request: NextRequest) {
  // 1. Validate user authentication
  const user = await currentUser();
  if (!user) return unauthorized();
  
  // 2. Parse multipart form data
  const formData = await request.formData();
  const file = formData.get('file') as File;
  
  // 3. Validate file
  if (!file || !isValidAudioFile(file)) return badRequest();
  
  // 4. Upload to B2
  const result = await cloudStorage.uploadFile(buffer, file.name, user.id);
  
  // 5. Return success response
  return NextResponse.json(result);
}
```

### 4.2 File Download Endpoint
**File**: `src/app/api/cloud-storage/download/[fileName]/route.ts`

**Features**:
- Secure file access with user validation
- Stream-based downloads for large files
- Proper content-type headers
- Download progress support
- Access logging

### 4.3 File Deletion Endpoint
**File**: `src/app/api/cloud-storage/delete/route.ts`

**Features**:
- User ownership validation
- Cascading deletion support
- Cleanup of related metadata
- Audit logging

### 4.4 File Listing Endpoint
**File**: `src/app/api/cloud-storage/list/route.ts`

**Features**:
- User-specific file filtering
- Pagination support
- Metadata inclusion
- Search and filtering capabilities

---

## 5. Frontend Integration

### 5.1 AudioGallery Component
**File**: `src/components/AudioGallery.tsx`

**Features**:
- Visual file browser with thumbnails
- Upload progress indicators
- Download and delete actions
- File metadata display
- Responsive grid layout

**Key Functions**:
```typescript
// File upload with progress
const handleFileUpload = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/cloud-storage/upload', {
    method: 'POST',
    body: formData,
  });
  
  return await response.json();
};

// File download
const handleFileDownload = async (fileName: string) => {
  const response = await fetch(`/api/cloud-storage/download/${fileName}`);
  const blob = await response.blob();
  
  // Trigger browser download
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  a.click();
};
```

### 5.2 Upload Progress Integration
**Implementation**:
- Real-time upload progress bars
- Cancel upload functionality
- Error state handling
- Success notifications

### 5.3 File Management UI
**Features**:
- Drag and drop upload zones
- File preview capabilities
- Batch operations (select multiple files)
- Metadata editing
- File sharing controls

---

## 6. File Management Features

### 6.1 File Naming Convention
**Pattern**: `{timestamp}_{userId}_{sanitizedOriginalName}.{extension}`

**Example**: `20250803_user_2ydPQl0gh8x5iVgRR5fYJ3zYFtm_my_audio_file.wav`

**Benefits**:
- Unique file names prevent conflicts
- User association for security
- Timestamp for organization
- Original name preservation

### 6.2 File Organization
**Directory Structure** (logical, using prefixes):
```
WordWaveStudio/
├── audio/
│   ├── user_2ydPQl0gh8x5iVgRR5fYJ3zYFtm/
│   │   ├── 20250803_212445_podcast_episode.wav
│   │   └── 20250803_212512_monologue_script.wav
│   └── user_other_user_id/
└── temp/
    └── processing_files/
```

### 6.3 File Metadata Management
**Stored Metadata**:
- Upload timestamp
- User ID association
- Original filename
- File size and type
- Processing status
- Download count (if tracking needed)

---

## 7. Security Implementation

### 7.1 Access Control
**User Authentication**:
- All API endpoints require valid Clerk user session
- File ownership validation before any operation
- User-specific file listing and access

**Implementation**:
```typescript
// User validation middleware
const user = await currentUser();
if (!user) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}

// File ownership validation
const isOwner = fileName.includes(`_${user.id}_`);
if (!isOwner) {
  return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
}
```

### 7.2 File Validation
**Security Checks**:
- File type validation (audio files only)
- File size limits
- Filename sanitization
- Content scanning (if required)

```typescript
const isValidAudioFile = (file: File): boolean => {
  const allowedTypes = ['audio/wav', 'audio/mpeg', 'audio/mp3', 'audio/ogg'];
  const maxSize = 50 * 1024 * 1024; // 50MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
};
```

### 7.3 API Security
**Protection Measures**:
- Rate limiting on upload endpoints
- Request size limits
- CORS configuration
- Error message sanitization
- Audit logging for sensitive operations

---

## 8. Testing Process

### 8.1 Local Development Testing
1. **Environment Setup**:
   ```bash
   # Add B2 credentials to .env.local
   B2_APPLICATION_KEY_ID=your_key_id
   B2_APPLICATION_KEY=your_application_key
   B2_BUCKET_NAME=your_test_bucket
   ```

2. **Test Upload Flow**:
   - Generate test audio files
   - Test various file formats and sizes
   - Verify user authentication
   - Check file naming and organization

3. **Test Download Flow**:
   - Verify file integrity after upload/download cycle
   - Test large file downloads
   - Check download permissions

### 8.2 Integration Testing
**Test Scenarios**:
- User uploads audio file → File appears in gallery
- User downloads file → File downloads correctly
- User deletes file → File removed from storage and gallery
- Unauthorized access attempts → Properly rejected
- Large file uploads → Progress tracking works
- Network interruptions → Proper error handling

### 8.3 Test Scripts
**File**: `test-b2-bucket.js`

```javascript
// Basic B2 connectivity test
const testB2Connection = async () => {
  try {
    const result = await cloudStorage.listFiles();
    console.log('✅ B2 connection successful');
    console.log(`Found ${result.length} files`);
  } catch (error) {
    console.error('❌ B2 connection failed:', error);
  }
};
```

---

## 9. Deployment Considerations

### 9.1 Environment Variables for Production
```bash
# Production B2 Configuration
B2_APPLICATION_KEY_ID=your_production_key_id
B2_APPLICATION_KEY=your_production_key
B2_BUCKET_NAME=your_production_bucket

# Optional: Separate buckets for different environments
B2_BUCKET_NAME_DEV=wordwave-dev
B2_BUCKET_NAME_STAGING=wordwave-staging  
B2_BUCKET_NAME_PROD=wordwave-production
```

### 9.2 AWS Deployment Considerations
**Configuration Management**:
- Use AWS Secrets Manager for B2 credentials
- Environment-specific bucket configurations
- CDN integration (CloudFront + B2) for better performance
- Monitoring and alerting for storage operations

### 9.3 Scaling Considerations
**Performance Optimizations**:
- Implement file compression for storage efficiency
- CDN integration for faster global access
- Caching layer for frequently accessed files
- Background processing for large uploads
- Database integration for file metadata

---

## 10. Troubleshooting

### 10.1 Common Issues

#### Authentication Failures
- **Cause**: Invalid or expired B2 credentials
- **Solution**: Verify credentials in Backblaze dashboard, regenerate if needed
- **Debug**: Check B2 API response messages in server logs

#### Upload Failures
- **Cause**: Network issues, file size limits, or invalid file types
- **Solution**: Implement retry logic, validate files client-side
- **Debug**: Monitor upload progress and check for partial uploads

#### Download Issues
- **Cause**: File not found, permission issues, or network problems
- **Solution**: Verify file existence, check user permissions
- **Debug**: Test direct B2 API access vs. application endpoint

#### Performance Issues
- **Cause**: Large files, network latency, or concurrent operations
- **Solution**: Implement chunked uploads, compression, CDN
- **Debug**: Monitor upload/download speeds and B2 API response times

### 10.2 Debug Tools and Monitoring
**Logging Implementation**:
```typescript
// Comprehensive logging for B2 operations
console.log('🔄 B2 Operation:', {
  operation: 'upload',
  fileName,
  userId,
  fileSize: file.size,
  timestamp: new Date().toISOString()
});
```

**Monitoring Metrics**:
- Upload success/failure rates
- Average upload/download times
- Storage usage per user
- API error rates
- File access patterns

---

## 11. Key Files Summary

| File | Purpose |
|------|---------|
| `src/lib/CloudStorageService.ts` | Core B2 service implementation |
| `src/types/CloudStorage.ts` | TypeScript type definitions |
| `src/app/api/cloud-storage/upload/route.ts` | File upload API endpoint |
| `src/app/api/cloud-storage/download/[fileName]/route.ts` | File download API endpoint |
| `src/app/api/cloud-storage/delete/route.ts` | File deletion API endpoint |
| `src/app/api/cloud-storage/list/route.ts` | File listing API endpoint |
| `src/components/AudioGallery.tsx` | Frontend file management UI |
| `test-b2-bucket.js` | B2 connectivity testing script |

---

## 12. Cost Optimization

### 12.1 B2 Pricing Considerations
**Storage Costs**:
- $0.005 per GB per month for storage
- $0.01 per GB for downloads
- Free uploads and first 1GB download per month

**Optimization Strategies**:
- Implement file compression
- Set up lifecycle policies for old files
- Monitor and alert on unusual usage
- Consider file archiving for rarely accessed content

### 12.2 Usage Monitoring
**Implementation**:
- Track storage usage per user
- Monitor bandwidth consumption
- Set up alerts for cost thresholds
- Implement usage analytics

---

## 13. Advanced Features

### 13.1 File Processing Pipeline
**Potential Enhancements**:
- Audio format conversion
- Automatic transcription
- Thumbnail generation for audio waveforms
- Metadata extraction
- Virus scanning

### 13.2 Content Delivery Network (CDN)
**Benefits**:
- Faster global file access
- Reduced B2 bandwidth costs
- Better user experience
- Built-in caching

**Implementation Options**:
- CloudFront + B2
- Cloudflare + B2
- B2 native CDN features

---

*Last updated: August 3, 2025*
*Integration status: Production ready with comprehensive error handling and security*
