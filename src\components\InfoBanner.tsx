import React from 'react';

interface InfoBannerProps {
  type?: 'info' | 'success' | 'warning' | 'error' | 'promo';
  title?: string;
  message: string;
  icon?: string;
  className?: string;
  children?: React.ReactNode;
}

const InfoBanner: React.FC<InfoBannerProps> = ({
  type = 'info',
  title,
  message,
  icon,
  className = '',
  children
}) => {
  const getBannerStyles = () => {
    // Exact same styling as the main Banner component for consistency
    const baseStyles = "rounded-xl p-4 border backdrop-blur-sm";

    switch (type) {
      case 'info':
        return `${baseStyles} bg-blue-500/10 border-blue-500/20 text-blue-200`;
      case 'success':
        return `${baseStyles} bg-green-500/10 border-green-500/20 text-green-200`;
      case 'warning':
        return `${baseStyles} bg-yellow-500/10 border-yellow-500/20 text-yellow-200`;
      case 'error':
        return `${baseStyles} bg-red-500/10 border-red-500/20 text-red-200`;
      case 'promo':
        return `${baseStyles} bg-purple-500/10 border-purple-500/20 text-purple-200`;
      default:
        return `${baseStyles} bg-blue-500/10 border-blue-500/20 text-blue-200`;
    }
  };

  return (
    <div className={`${getBannerStyles()} ${className}`}>
      {title && (
        <h3 className="font-semibold mb-2 flex items-center gap-2 text-xl">
          {icon && <span>{icon}</span>}
          {title}
        </h3>
      )}
      {!title && icon && message && (
        <div className="flex items-start gap-2">
          <span className="flex-shrink-0">{icon}</span>
          <div className="flex-1">
            <p className="text-sm">{message}</p>
            {children}
          </div>
        </div>
      )}
      {!title && !icon && message && (
        <>
          <p className="text-sm">{message}</p>
          {children}
        </>
      )}
      {title && message && (
        <>
          <p className="text-sm">{message}</p>
          {children}
        </>
      )}
      {title && !message && children}
      {!title && icon && !message && (
        <div className="flex items-start gap-2">
          <span className="flex-shrink-0">{icon}</span>
          <div className="flex-1">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

export default InfoBanner;
