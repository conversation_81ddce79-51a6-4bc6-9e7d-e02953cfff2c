/**
 * GDPR Account Deletion Service
 * Handles complete and irreversible deletion of user accounts and all associated data
 * 
 * WARNING: This service performs PERMANENT deletion that cannot be undone
 */

import { clerkClient } from '@clerk/nextjs/server';
import Stripe from 'stripe';
import { ServerCloudStorageService } from './CloudStorageService';
import { EmailService } from './EmailService';

export interface AccountDeletionRequest {
  id: string;
  userId: string;
  requestedAt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  completedAt?: string;
  errorMessage?: string;
  deletionSteps: {
    stripeData: 'pending' | 'completed' | 'failed' | 'skipped';
    cloudFiles: 'pending' | 'completed' | 'failed' | 'skipped';
    clerkAccount: 'pending' | 'completed' | 'failed' | 'skipped';
    emailNotification: 'pending' | 'completed' | 'failed' | 'skipped';
  };
}

export class GDPRAccountDeletionService {
  private stripe: Stripe;
  private cloudStorage: ServerCloudStorageService;
  private emailService: EmailService;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

    this.cloudStorage = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
      applicationKey: process.env.B2_APPLICATION_KEY!,
      bucketName: process.env.B2_BUCKET_NAME!,
    });

    this.emailService = EmailService.getInstance();
  }

  /**
   * Process complete account deletion
   * WARNING: This is IRREVERSIBLE and PERMANENT
   */
  async processAccountDeletion(userId: string, requestId: string): Promise<AccountDeletionRequest> {
    console.log(`🚨 STARTING PERMANENT ACCOUNT DELETION for user: ${userId}`);
    console.log(`⚠️  This action is IRREVERSIBLE and will delete ALL data`);

    const deletionRequest: AccountDeletionRequest = {
      id: requestId,
      userId,
      requestedAt: new Date().toISOString(),
      status: 'processing',
      deletionSteps: {
        stripeData: 'pending',
        cloudFiles: 'pending',
        clerkAccount: 'pending',
        emailNotification: 'pending'
      }
    };

    try {
      // Get user details for final email before deletion
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);
      const userEmail = user.emailAddresses.find(email => 
        email.id === user.primaryEmailAddressId
      )?.emailAddress || user.emailAddresses[0]?.emailAddress;
      const userName = user.firstName || 'User';

      // Step 1: Cancel and delete Stripe data
      console.log(`💳 Step 1: Processing Stripe account deletion...`);
      try {
        await this.deleteStripeData(userId);
        deletionRequest.deletionSteps.stripeData = 'completed';
        console.log(`✅ Stripe data deletion completed`);
      } catch (error) {
        console.error(`❌ Stripe deletion failed:`, error);
        deletionRequest.deletionSteps.stripeData = 'failed';
        // Continue with other deletions even if Stripe fails
      }

      // Step 2: Delete all files from cloud storage
      console.log(`🗂️ Step 2: Deleting all cloud files and projects...`);
      try {
        await this.deleteAllCloudFiles(userId);
        deletionRequest.deletionSteps.cloudFiles = 'completed';
        console.log(`✅ Cloud files deletion completed`);
      } catch (error) {
        console.error(`❌ Cloud files deletion failed:`, error);
        deletionRequest.deletionSteps.cloudFiles = 'failed';
        // Continue with other deletions
      }

      // Step 3: Send deletion confirmation email BEFORE deleting Clerk account
      console.log(`📧 Step 3: Sending final deletion confirmation email...`);
      try {
        if (userEmail) {
          await this.sendAccountDeletionConfirmationEmail(userEmail, userName, requestId);
          deletionRequest.deletionSteps.emailNotification = 'completed';
          console.log(`✅ Deletion confirmation email sent`);
        } else {
          console.warn(`⚠️ No email address found for user ${userId}`);
          deletionRequest.deletionSteps.emailNotification = 'skipped';
        }
      } catch (error) {
        console.error(`❌ Email notification failed:`, error);
        deletionRequest.deletionSteps.emailNotification = 'failed';
        // Continue with account deletion even if email fails
      }

      // Step 4: Delete Clerk user account (FINAL STEP - No going back after this)
      console.log(`👤 Step 4: FINAL STEP - Deleting Clerk user account...`);
      console.log(`🚨 WARNING: After this step, the user account will be completely deleted`);
      try {
        await clerk.users.deleteUser(userId);
        deletionRequest.deletionSteps.clerkAccount = 'completed';
        console.log(`✅ Clerk account deletion completed`);
        console.log(`🗑️ ACCOUNT DELETION COMPLETE - User ${userId} has been permanently deleted`);
      } catch (error) {
        console.error(`❌ Clerk account deletion failed:`, error);
        deletionRequest.deletionSteps.clerkAccount = 'failed';
        throw error; // This is critical - if Clerk deletion fails, the process should fail
      }

      deletionRequest.status = 'completed';
      deletionRequest.completedAt = new Date().toISOString();

      return deletionRequest;

    } catch (error) {
      console.error(`❌ Account deletion failed for user ${userId}:`, error);
      deletionRequest.status = 'failed';
      deletionRequest.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      deletionRequest.completedAt = new Date().toISOString();
      
      throw error;
    }
  }

  /**
   * Delete all Stripe-related data for a user
   */
  private async deleteStripeData(userId: string): Promise<void> {
    try {
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);
      const stripeCustomerId = user.unsafeMetadata?.stripeCustomerId as string;

      if (!stripeCustomerId) {
        console.log(`ℹ️ No Stripe customer ID found for user ${userId}`);
        return;
      }

      console.log(`💳 Processing Stripe data for customer: ${stripeCustomerId}`);

      // 1. Handle active subscriptions with customer-friendly cancellation
      const subscriptions = await this.stripe.subscriptions.list({ 
        customer: stripeCustomerId,
        status: 'active',
        limit: 100 
      });

      for (const subscription of subscriptions.data) {
        console.log(`📋 Processing subscription: ${subscription.id}`);
        
        // Check if subscription was recently created (within last 7 days)
        const subscriptionAge = Date.now() - (subscription.created * 1000);
        const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
        
        if (subscriptionAge < sevenDaysInMs) {
          console.log(`🔄 Recent subscription detected - cancelling with refund`);
          
          // Cancel subscription and issue refund for recent subscriptions
          await this.stripe.subscriptions.cancel(subscription.id, {
            prorate: true, // Calculate prorated refund
          });
          
          // Get the latest invoice to refund
          const invoices = await this.stripe.invoices.list({
            customer: stripeCustomerId,
            subscription: subscription.id,
            limit: 1
          });
          
          if (invoices.data.length > 0 && (invoices.data[0] as any).payment_intent) {
            try {
              console.log(`💰 Issuing refund for recent payment`);
              await this.stripe.refunds.create({
                payment_intent: (invoices.data[0] as any).payment_intent as string,
                reason: 'requested_by_customer',
                metadata: {
                  reason: 'GDPR account deletion - recent subscription',
                  userId: userId,
                  deletionDate: new Date().toISOString()
                }
              });
              console.log(`✅ Refund issued for subscription ${subscription.id}`);
            } catch (refundError) {
              console.warn(`⚠️ Failed to issue refund for ${subscription.id}:`, refundError);
              // Continue with deletion even if refund fails
            }
          }
        } else {
          console.log(`📅 Older subscription - cancelling at period end`);
          
          // For older subscriptions, cancel at period end (customer keeps access until then)
          await this.stripe.subscriptions.update(subscription.id, {
            cancel_at_period_end: true,
            metadata: {
              cancellation_reason: 'GDPR account deletion',
              userId: userId,
              deletionDate: new Date().toISOString()
            }
          });
        }
      }

      // 2. Delete all payment methods
      const paymentMethods = await this.stripe.paymentMethods.list({ 
        customer: stripeCustomerId,
        limit: 100 
      });

      for (const paymentMethod of paymentMethods.data) {
        console.log(`💳 Detaching payment method: ${paymentMethod.id}`);
        await this.stripe.paymentMethods.detach(paymentMethod.id);
      }

      // 3. Update customer record with deletion info before deleting
      await this.stripe.customers.update(stripeCustomerId, {
        metadata: {
          deleted_for_gdpr: 'true',
          deletion_date: new Date().toISOString(),
          original_user_id: userId
        }
      });

      // 4. Delete the customer (this also deletes associated data)
      console.log(`👤 Deleting Stripe customer: ${stripeCustomerId}`);
      await this.stripe.customers.del(stripeCustomerId);

      console.log(`✅ Stripe data deletion completed for user ${userId}`);

    } catch (error) {
      console.error(`❌ Failed to delete Stripe data for user ${userId}:`, error);
      throw new Error(`Stripe deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete all cloud files and projects for a user
   */
  private async deleteAllCloudFiles(userId: string): Promise<void> {
    try {
      await this.cloudStorage.initialize();

      console.log(`🗂️ Deleting all cloud files for user: ${userId}`);

      // Delete all user files by deleting the entire user directory
      const userPath = `users/${userId}/`;
      
      console.log(`📁 Deleting user directory: ${userPath}`);
      
      // List all files under the user path
      const userFiles = await this.cloudStorage.listUserFiles(userId);
      
      console.log(`📋 Found ${userFiles.length} files to delete for user ${userId}`);

      // Delete files in batches to avoid overwhelming the API
      const batchSize = 10;
      for (let i = 0; i < userFiles.length; i += batchSize) {
        const batch = userFiles.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (file) => {
          try {
            console.log(`🗑️ Deleting file: ${file.fileName}`);
            await this.cloudStorage.deleteUserFile(file.fileName);
          } catch (error) {
            console.warn(`⚠️ Failed to delete file ${file.fileName}:`, error);
            // Continue deleting other files even if one fails
          }
        }));

        console.log(`✅ Deleted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(userFiles.length / batchSize)}`);
      }

      // Also delete any export files for this user
      const exportPath = `exports/${userId}/`;
      try {
        const exportFiles = await this.cloudStorage.listUserExportFiles(userId);
        console.log(`📋 Found ${exportFiles.length} export files to delete`);
        
        for (const file of exportFiles) {
          try {
            console.log(`🗑️ Deleting export file: ${file.fileName}`);
            await this.cloudStorage.deleteUserFile(file.fileName);
          } catch (error) {
            console.warn(`⚠️ Failed to delete export file ${file.fileName}:`, error);
          }
        }
      } catch (error) {
        console.log(`ℹ️ No export files found for user ${userId} (this is normal)`);
      }

      console.log(`✅ Cloud files deletion completed for user ${userId}`);

    } catch (error) {
      console.error(`❌ Failed to delete cloud files for user ${userId}:`, error);
      throw new Error(`Cloud files deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send account deletion confirmation email
   */
  private async sendAccountDeletionConfirmationEmail(
    userEmail: string, 
    userName: string, 
    requestId: string
  ): Promise<void> {
    try {
      console.log(`📧 Sending account deletion confirmation to: ${userEmail}`);

      const subject = 'WordWave Studio - Account Deletion Completed';
      
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { background: white; padding: 20px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 14px; color: #666; }
            .deletion-complete { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Account Deletion Completed</h1>
            </div>
            
            <div class="content">
              <p>Hello ${userName || 'there'},</p>
              
              <div class="deletion-complete">
                <strong>✅ Your WordWave Studio account has been permanently deleted.</strong><br>
                This action was completed on ${new Date().toLocaleDateString()} and is irreversible.
              </div>
              
              <h3>🗑️ What Has Been Deleted:</h3>
              <ul>
                <li><strong>Account Information:</strong> Your profile, email addresses, and account settings</li>
                <li><strong>All Projects:</strong> Every project you created has been permanently removed</li>
                <li><strong>Audio Files:</strong> All generated audio content has been deleted from our servers</li>
                <li><strong>Subscription Data:</strong> All billing subscriptions have been processed according to our refund policy</li>
                <li><strong>Usage Data:</strong> All usage patterns and activity logs have been purged</li>
                <li><strong>Support History:</strong> Any support tickets or communications have been deleted</li>
              </ul>
              
              <h3>💰 Billing & Refunds:</h3>
              <div class="warning">
                <strong>Recent Subscriptions (within 7 days):</strong><br>
                • Automatic refund issued for unused portion<br>
                • You should see the refund in 5-10 business days<br><br>
                <strong>Older Subscriptions:</strong><br>
                • Cancelled at end of current billing period<br>
                • No refund for partially used subscription periods<br>
                • This follows our standard cancellation policy
              </div>
              
              <div class="warning">
                <strong>⚠️ Important:</strong><br>
                • This deletion is permanent and cannot be undone<br>
                • You will no longer be able to log into your account<br>
                • All subscriptions have been cancelled (no further charges)<br>
                • You will need to create a new account if you wish to use WordWave Studio again
              </div>
              
              <h3>📋 Deletion Details:</h3>
              <p>
                <strong>Request ID:</strong> ${requestId}<br>
                <strong>Completed:</strong> ${new Date().toISOString()}<br>
                <strong>Compliance:</strong> GDPR Article 17 (Right to Erasure)
              </p>
              
              <p>Thank you for using WordWave Studio. We're sorry to see you go.</p>
              
              <div class="footer">
                <p>This email confirms your GDPR data deletion request.<br>
                No further emails will be sent to this address regarding your former WordWave Studio account.</p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `;

      const text = `
Account Deletion Completed - WordWave Studio

Hello ${userName || 'there'},

Your WordWave Studio account has been permanently deleted on ${new Date().toLocaleDateString()}.

What Has Been Deleted:
• Account Information: Your profile, email addresses, and account settings
• All Projects: Every project you created has been permanently removed
• Audio Files: All generated audio content has been deleted from our servers
• Subscription Data: All billing subscriptions have been processed according to our refund policy
• Usage Data: All usage patterns and activity logs have been purged
• Support History: Any support tickets or communications have been deleted

Billing & Refunds:
Recent Subscriptions (within 7 days):
- Automatic refund issued for unused portion
- You should see the refund in 5-10 business days

Older Subscriptions:
- Cancelled at end of current billing period
- No refund for partially used subscription periods
- This follows our standard cancellation policy

IMPORTANT:
- This deletion is permanent and cannot be undone
- You will no longer be able to log into your account
- All subscriptions have been cancelled (no further charges)
- You will need to create a new account if you wish to use WordWave Studio again

Deletion Details:
Request ID: ${requestId}
Completed: ${new Date().toISOString()}
Compliance: GDPR Article 17 (Right to Erasure)

Thank you for using WordWave Studio. We're sorry to see you go.

This email confirms your GDPR data deletion request.
No further emails will be sent to this address regarding your former WordWave Studio account.
      `;

      await this.emailService.sendEmailPublic({
        to: userEmail,
        subject,
        html,
        text
      });

      console.log(`✅ Account deletion confirmation email sent to: ${userEmail}`);

    } catch (error) {
      console.error(`❌ Failed to send deletion confirmation email:`, error);
      throw new Error(`Email notification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
