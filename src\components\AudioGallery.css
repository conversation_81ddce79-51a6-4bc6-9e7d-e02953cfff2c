/* Audio Gallery Styles */
.audio-gallery {
  width: 100%;
  margin-top: 1rem;
}

.audio-gallery-empty {
  width: 100%;
  margin-top: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h3 {
  color: #e2e8f0;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-family: 'Fredoka', sans-serif;
}

.empty-state p {
  color: #94a3b8;
  font-size: 0.875rem;
}

.gallery-header {
  margin-bottom: 1.5rem;
}

.gallery-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #f8fafc;
  font-size: 1.25rem;
  font-weight: 600;
  font-family: 'Fredoka', sans-serif;
  margin: 0;
}

.gallery-icon {
  font-size: 1.5rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.audio-card {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 12px;
  padding: 1.25rem;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.audio-card:hover {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(148, 163, 184, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 0.75rem;
}

.audio-info {
  flex: 1;
  min-width: 0;
}

.audio-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.name-text {
  color: #f1f5f9;
  font-weight: 500;
  font-size: 0.95rem;
  word-break: break-word;
  line-height: 1.4;
}

.edit-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 0.75rem;
  opacity: 0.7;
}

.edit-btn:hover {
  color: #94a3b8;
  background: rgba(148, 163, 184, 0.1);
  opacity: 1;
}

.edit-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.name-input {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 6px;
  padding: 0.5rem;
  color: #f1f5f9;
  font-size: 0.875rem;
  flex: 1;
  min-width: 0;
}

.name-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.edit-actions {
  display: flex;
  gap: 0.25rem;
}

.save-btn, .cancel-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.save-btn {
  color: #10b981;
}

.save-btn:hover {
  background: rgba(16, 185, 129, 0.1);
}

.cancel-btn {
  color: #ef4444;
}

.cancel-btn:hover {
  background: rgba(239, 68, 68, 0.1);
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.download-btn, .delete-btn, .regenerate-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  opacity: 0.7;
}

.download-btn {
  color: #3b82f6;
  text-decoration: none;
}

.download-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  opacity: 1;
}

.regenerate-btn {
  color: #10b981;
}

.regenerate-btn:hover {
  background: rgba(16, 185, 129, 0.1);
  opacity: 1;
}

.delete-btn {
  color: #ef4444;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  opacity: 1;
}

.waveform-container {
  position: relative;
  height: 60px;
  margin: 1rem 0;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  height: 100%;
  width: 100%;
  padding: 0.75rem;
}

.wave-bar {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  width: 3px;
  border-radius: 2px;
  transition: all 0.3s ease;
  opacity: 0.6;
  height: var(--wave-height, 50%);
  animation-delay: var(--wave-delay, 0s);
}

.wave-bar.playing {
  animation: wave-pulse 1.5s ease-in-out infinite;
  opacity: 1;
}

@keyframes wave-pulse {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  transition: width 0.1s ease;
  pointer-events: none;
  width: var(--progress-width, 0%);
}

/* Interactive Progress Bar */
.progress-bar-container {
  margin: 1rem 0 0.5rem 0;
  padding: 0 0.5rem;
}

.progress-bar-track {
  position: relative;
  height: 6px;
  background: rgba(100, 116, 139, 0.3);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.progress-bar-track:hover {
  background: rgba(100, 116, 139, 0.4);
  height: 8px;
  margin-top: -1px;
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 3px;
  transition: width 0.1s ease;
  pointer-events: none;
  width: var(--progress-width, 0%);
}

.progress-bar-thumb {
  position: absolute;
  top: 50%;
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
  opacity: 0;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  left: var(--thumb-position, 0%);
}

.progress-bar-track:hover .progress-bar-thumb {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.progress-bar-track.dragging {
  background: rgba(100, 116, 139, 0.5);
  height: 8px;
  margin-top: -1px;
}

.progress-bar-track.dragging .progress-bar-thumb {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
}

.progress-bar-track.dragging .progress-bar-fill {
  background: linear-gradient(90deg, #2563eb, #3b82f6);
}

/* Focus styles for keyboard navigation */
.progress-bar-track:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  background: rgba(100, 116, 139, 0.4);
}

.progress-bar-track:focus .progress-bar-thumb {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.play-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.play-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.play-btn:disabled {
  background: rgba(100, 116, 139, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.play-btn.playing {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.time-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #94a3b8;
  font-family: 'JetBrains Mono', monospace;
}

.separator {
  opacity: 0.5;
}

.script-preview {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
}

.script-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.script-label {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
}

.script-topic {
  color: #60a5fa;
  font-size: 0.75rem;
  font-weight: 500;
  background: rgba(96, 165, 250, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.script-content {
  color: #cbd5e1;
  font-size: 0.75rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 80px;
  overflow-y: auto;
  padding: 0.5rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.script-content::-webkit-scrollbar {
  width: 4px;
}

.script-content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 2px;
}

.script-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.script-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

.audio-metadata {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.metadata-label {
  color: #64748b;
  font-weight: 500;
}

.metadata-value {
  color: #cbd5e1;
  text-align: right;
  word-break: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .audio-card {
    padding: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .card-actions {
    justify-content: flex-end;
  }
}
