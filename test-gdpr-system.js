#!/usr/bin/env node

/**
 * GDPR Data Export System Test Script
 * Run this to test the automated data export functionality
 */

const { BackgroundJobQueue } = require('./src/lib/BackgroundJobQueue');
const { EmailService } = require('./src/lib/EmailService');

async function testGDPRSystem() {
  console.log('🧪 Testing GDPR Automated Data Export System\n');

  try {
    // Test 1: Background Job Queue
    console.log('1️⃣ Testing Background Job Queue...');
    const jobQueue = BackgroundJobQueue.getInstance();
    
    const testUserId = 'test-user-123';
    const testRequestId = 'test-request-' + Date.now();
    
    console.log(`   Queueing test export for user: ${testUserId}`);
    const job = await jobQueue.queueDataExport(testUserId, testRequestId);
    console.log(`   ✅ Job queued: ${job.id}`);
    
    // Check queue stats
    const stats = jobQueue.getQueueStats();
    console.log(`   📊 Queue stats:`, stats);
    
    // Test 2: Email Service
    console.log('\n2️⃣ Testing Email Service...');
    const emailService = EmailService.getInstance();
    
    const testResult = await emailService.sendDataExportCompleteEmail(
      '<EMAIL>',
      'Test User',
      'https://example.com/download/test',
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    );
    
    if (testResult) {
      console.log('   ✅ Email service test passed');
    } else {
      console.log('   ❌ Email service test failed');
    }
    
    // Test 3: Check environment variables
    console.log('\n3️⃣ Testing Environment Configuration...');
    
    const requiredEnvVars = [
      'B2_APPLICATION_KEY_ID',
      'B2_APPLICATION_KEY',
      'B2_BUCKET_NAME',
      'NEXT_PUBLIC_APP_URL'
    ];
    
    const optionalEnvVars = [
      'RESEND_API_KEY',
      'SENDGRID_API_KEY',
      'FROM_EMAIL'
    ];
    
    let missingRequired = [];
    let missingOptional = [];
    
    requiredEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        missingRequired.push(envVar);
      } else {
        console.log(`   ✅ ${envVar}: configured`);
      }
    });
    
    optionalEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        missingOptional.push(envVar);
      } else {
        console.log(`   ✅ ${envVar}: configured`);
      }
    });
    
    if (missingRequired.length > 0) {
      console.log(`   ❌ Missing required environment variables: ${missingRequired.join(', ')}`);
    }
    
    if (missingOptional.length > 0) {
      console.log(`   ⚠️  Missing optional environment variables: ${missingOptional.join(', ')}`);
      console.log('      (Email notifications will be logged instead of sent)');
    }
    
    // Summary
    console.log('\n📋 Test Summary:');
    console.log('================');
    
    if (missingRequired.length === 0) {
      console.log('✅ All required components are configured');
      console.log('✅ Background job processing: Ready');
      console.log('✅ Data export system: Ready');
      
      if (missingOptional.length === 0) {
        console.log('✅ Email notifications: Ready');
        console.log('🎉 GDPR Data Export system is fully operational!');
      } else {
        console.log('⚠️  Email notifications: Development mode (logging only)');
        console.log('💡 Add email service environment variables for production');
      }
    } else {
      console.log('❌ System not ready - missing required environment variables');
      console.log('📖 See GDPR_AUTOMATED_EXPORT_SETUP.md for configuration instructions');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Only run if this script is executed directly
if (require.main === module) {
  testGDPRSystem();
}

module.exports = { testGDPRSystem };
