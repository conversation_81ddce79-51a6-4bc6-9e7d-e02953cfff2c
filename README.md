# WordWave Studio

An AI-powered audio content generation platform built with Next.js that creates professional podcasts and monologues using Google Gemini AI and advanced text-to-speech synthesis.

## 🚀 Features

### Core Functionality
- **🎙️ Podcast Generation**: Multi-speaker podcast conversations with AI-generated scripts
- **🗣️ Monologue Creation**: Single-speaker audio content generation
- **🤖 AI Script Generation**: Powered by Google Gemini AI with multiple model options
- **🔊 Advanced Text-to-Speech**: High-quality synthesis with 31+ voice options
- **☁️ Cloud Storage**: Project management with Backblaze B2 integration
- **🔐 User Authentication**: Secure login with Clerk
- **💳 Subscription Management**: Usage tracking and billing integration

### UI/UX Features
- **Interactive Wave Animations**: Responsive wave patterns that react to mouse movement
- **Dynamic Noise Background**: Subtle noise texture that adds depth to the design
- **Shiny Text Effects**: Eye-catching text animations with gradient effects
- **Responsive Design**: Optimized for all screen sizes
- **Modern UI**: Clean, dark theme with glassmorphism effects

## 🛠️ Tech Stack

### Frontend
- **Next.js 15.3.4** - React framework with App Router
- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions

### Backend & Services
- **Google Gemini AI** - Script generation and text-to-speech
- **Clerk** - User authentication and management
- **Backblaze B2** - Cloud file storage
- **Stripe** - Payment processing and subscriptions

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Google Gemini API key
- Clerk account (for authentication)
- Backblaze B2 account (for cloud storage)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/pjecuacion/WordWaveNextJs.git
   cd WordWaveNextJs
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.local.example .env.local
   ```

   Fill in your API keys in `.env.local`:
   ```env
   # Authentication (Clerk)
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
   CLERK_SECRET_KEY=sk_test_your_key_here

   # AI Services (Google Gemini)
   GOOGLE_GEMINI_API_KEY=your_gemini_api_key_here

   # Cloud Storage (Backblaze B2)
   B2_APPLICATION_KEY_ID=your_b2_key_id_here
   B2_APPLICATION_KEY=your_b2_application_key_here
   B2_BUCKET_NAME=your_bucket_name_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

### 1. Authentication
- Sign up or sign in using Clerk authentication
- Configure your Google Gemini API key in the settings

### 2. Project Setup
- Choose between Podcast (2 speakers) or Monologue (1 speaker)
- Set project name and speaker personas
- Select project style and tone

### 3. Script Generation
- Enter your topic or use the random topic generator
- Add relevant reference links (optional)
- Choose your preferred AI model
- Generate your script with one click

### 4. Audio Synthesis
- Select voices for each speaker
- Choose TTS model
- Generate high-quality audio from your script
- Download or save to cloud storage

## 🔧 Configuration

See [docs/environment.md](docs/environment.md) for detailed environment setup instructions.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
