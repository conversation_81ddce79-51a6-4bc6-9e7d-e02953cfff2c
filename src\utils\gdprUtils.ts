/**
 * GDPR Compliance Utilities
 * Handles cookie consent, data rights, and privacy management
 */

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export interface GDPRConsent {
  preferences: CookiePreferences;
  timestamp: string;
  version: string;
}

const STORAGE_KEYS = {
  CONSENT: 'cookie-consent',
  CONSENT_TIMESTAMP: 'cookie-consent-timestamp',
  CONSENT_VERSION: 'gdpr-consent-version',
} as const;

const CURRENT_CONSENT_VERSION = '1.0';

/**
 * Get current cookie consent preferences
 */
export function getCookieConsent(): GDPRConsent | null {
  if (typeof window === 'undefined') return null;

  try {
    const consent = localStorage.getItem(STORAGE_KEYS.CONSENT);
    const timestamp = localStorage.getItem(STORAGE_KEYS.CONSENT_TIMESTAMP);
    const version = localStorage.getItem(STORAGE_KEYS.CONSENT_VERSION);

    if (!consent || !timestamp) return null;

    return {
      preferences: JSON.parse(consent),
      timestamp,
      version: version || '1.0'
    };
  } catch (error) {
    console.error('Error reading cookie consent:', error);
    return null;
  }
}

/**
 * Save cookie consent preferences
 */
export function saveCookieConsent(preferences: CookiePreferences): void {
  if (typeof window === 'undefined') return;

  try {
    const timestamp = new Date().toISOString();
    
    localStorage.setItem(STORAGE_KEYS.CONSENT, JSON.stringify(preferences));
    localStorage.setItem(STORAGE_KEYS.CONSENT_TIMESTAMP, timestamp);
    localStorage.setItem(STORAGE_KEYS.CONSENT_VERSION, CURRENT_CONSENT_VERSION);

    // Dispatch event for other components
    window.dispatchEvent(new CustomEvent('cookiePreferencesUpdated', {
      detail: { preferences, timestamp, version: CURRENT_CONSENT_VERSION }
    }));
  } catch (error) {
    console.error('Error saving cookie consent:', error);
  }
}

/**
 * Clear all cookie consent data
 */
export function clearCookieConsent(): void {
  if (typeof window === 'undefined') return;

  localStorage.removeItem(STORAGE_KEYS.CONSENT);
  localStorage.removeItem(STORAGE_KEYS.CONSENT_TIMESTAMP);
  localStorage.removeItem(STORAGE_KEYS.CONSENT_VERSION);
}

/**
 * Check if user has given consent for a specific cookie type
 */
export function hasConsentFor(cookieType: keyof CookiePreferences): boolean {
  const consent = getCookieConsent();
  if (!consent) return false;
  
  // Necessary cookies are always allowed
  if (cookieType === 'necessary') return true;
  
  return consent.preferences[cookieType] || false;
}

/**
 * Check if consent needs to be renewed (e.g., after policy updates)
 */
export function needsConsentRenewal(): boolean {
  const consent = getCookieConsent();
  if (!consent) return true;
  
  // Check if consent version is outdated
  if (consent.version !== CURRENT_CONSENT_VERSION) return true;
  
  // Check if consent is older than 1 year
  const consentDate = new Date(consent.timestamp);
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
  
  return consentDate < oneYearAgo;
}

/**
 * Apply cookie preferences to actual tracking services
 */
export function applyCookiePreferences(preferences: CookiePreferences): void {
  // Analytics
  if (preferences.analytics) {
    enableAnalytics();
  } else {
    disableAnalytics();
  }

  // Marketing
  if (preferences.marketing) {
    enableMarketing();
  } else {
    disableMarketing();
  }

  // Functional
  if (preferences.functional) {
    enableFunctional();
  } else {
    disableFunctional();
  }
}

/**
 * Enable analytics tracking (Google Analytics, Vercel Analytics, etc.)
 */
function enableAnalytics(): void {
  // Example: Enable Google Analytics
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('consent', 'update', {
      analytics_storage: 'granted'
    });
  }
  
  // Enable Vercel Analytics if using it
  console.log('Analytics enabled');
}

/**
 * Disable analytics tracking
 */
function disableAnalytics(): void {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('consent', 'update', {
      analytics_storage: 'denied'
    });
  }
  
  console.log('Analytics disabled');
}

/**
 * Enable marketing cookies
 */
function enableMarketing(): void {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('consent', 'update', {
      ad_storage: 'granted',
      ad_user_data: 'granted',
      ad_personalization: 'granted'
    });
  }
  
  console.log('Marketing cookies enabled');
}

/**
 * Disable marketing cookies
 */
function disableMarketing(): void {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('consent', 'update', {
      ad_storage: 'denied',
      ad_user_data: 'denied',
      ad_personalization: 'denied'
    });
  }
  
  console.log('Marketing cookies disabled');
}

/**
 * Enable functional cookies
 */
function enableFunctional(): void {
  // Enable features like chat widgets, embedded content, etc.
  console.log('Functional cookies enabled');
}

/**
 * Disable functional cookies
 */
function disableFunctional(): void {
  // Disable non-essential functional features
  console.log('Functional cookies disabled');
}

/**
 * Get default cookie preferences (all disabled except necessary)
 */
export function getDefaultPreferences(): CookiePreferences {
  return {
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  };
}

/**
 * Get all-accepted cookie preferences
 */
export function getAllAcceptedPreferences(): CookiePreferences {
  return {
    necessary: true,
    analytics: true,
    marketing: true,
    functional: true,
  };
}
