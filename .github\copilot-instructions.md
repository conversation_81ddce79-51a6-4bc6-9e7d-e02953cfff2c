# Enhanced Coding Vibe Guidelines

## 🚀 Server & Environment Management
- After making changes, ALWAYS make sure to start up a new server so I can test it.
- Always kill all existing related servers that may have been created in previous testing before trying to start a new server.
- Write code that takes into account the different environments: dev, test, and prod.
- Never overwrite my .env file without first asking and confirming.
- Always use python .venv.
- Use environment-specific configuration files and never hardcode environment values.

## 🔄 Code Evolution & Patterns
- Always look for existing code to iterate on instead of creating new code.
- Do not drastically change the patterns before trying to iterate on existing patterns.
- When fixing an issue or bug, do not introduce a new pattern or technology without first exhausting all options for the existing implementation. If you must, ensure the old implementation is removed to avoid duplicate logic.
- Avoid major changes to the architecture of a working feature unless explicitly instructed.
- Follow established naming conventions and project structure consistently.

## 🎯 Simplicity & Focus
- Always prefer simple solutions.
- Focus on the areas of code relevant to the task. Do not touch unrelated code.
- You are careful to only make changes that are requested or you are confident are well understood and related to the change being requested.
- Write self-documenting code with clear variable and function names.
- Implement the minimum viable solution first, then iterate.

## 🧹 Code Quality & Organization
- Avoid duplication of code. Check for existing functionality before writing new code.
- Keep the codebase very clean and organized.
- Avoid having files over 200-300 lines of code. Refactor at that point.
- Extract reusable components and utilities into separate modules.
- Use consistent indentation and formatting throughout the project.
- Remove commented-out code and unused imports regularly.

## 📝 Scripts & File Management
- Avoid writing scripts in files if possible, especially if the script is likely only to be run once.
- Create a dedicated `scripts` directory for one-time utilities if absolutely necessary.
- Document any scripts with clear usage instructions and purpose.

## 🧪 Testing & Data Management
- Mocking data is only for tests. Never mock data for dev or prod environments.
- Never add stubbing or fake data patterns to code that affects the dev or prod environments.
- Write thorough tests for all major functionality.
- Use proper test data factories and fixtures for consistent testing.
- Implement integration tests for critical user flows.
- Always write test automation for parts of your codebase where the cost of failure is high (e.g., payments, subscription management, usage tracking).
- When writing tests for third-party integrations like Stripe, ALWAYS reference official examples and docs.

## 🔍 Impact Assessment & Dependencies
- Always think about what other methods and areas of code might be affected by code changes.
- Run the full test suite before committing changes.
- Consider backward compatibility when modifying existing APIs.
- Document breaking changes clearly in commit messages.

## 📚 Documentation & Communication
- Write clear commit messages that explain the "why," not just the "what."
- Add inline comments for complex business logic.
- Update README files when adding new features or changing setup procedures.
- Keep API documentation in sync with code changes.

## 🔧 Performance & Optimization
- Profile before optimizing—measure actual performance bottlenecks.
- Implement lazy loading where appropriate.
- Use database indexing strategically.
- Cache expensive operations thoughtfully.

## 🛡️ Security & Best Practices
- Validate all user inputs at entry points.
- Use parameterized queries to prevent SQL injection.
- Implement proper error handling without exposing sensitive information.
- Follow the principle of least privilege.
- Rate limit all API endpoints.
- Use Row-Level Security (RLS) always.
- Use Captcha on all auth routes/signup pages.
- If using a hosting solution like Vercel, enable Attack Challenge on their WAF.
- Do not share environment variables in videos or streams; know the difference between client & server-side variables.
- Before deploying a Node.js/web app, always run `npm run audit` and resolve vulnerabilities.
- Do not roll your own auth; use managed solutions like Clerk and read their documentation carefully.
- Always set up IP + user-based rate limiting, DDoS protection, firewalls, monitoring, and analytics.

## 🔄 API & Backend Specific (FastAPI/Flask)
- Always validate request/response schemas explicitly.
- Implement proper HTTP status codes for all endpoints.
- Use dependency injection patterns for database connections and external services.
- Log meaningful information for debugging without exposing sensitive data.

## 🌐 Frontend Integration (Next.js)
- Keep API calls centralized in service layers, not scattered throughout components.
- Implement proper error boundaries for graceful failure handling.
- Use TypeScript interfaces consistently between frontend and backend.
- Cache API responses appropriately to reduce server load.

## 📊 Business Logic & Analytics
- Separate business logic from framework-specific code.
- Document business rules and requirements clearly in code comments.
- Implement feature flags for gradual rollouts.
- Track key metrics and user interactions for data-driven decisions.

## 🔀 Version Control & Collaboration
- Make atomic commits: one logical change per commit.
- Use meaningful branch names that reflect the feature or fix.
- Squash commits before merging to keep history clean.
- Review your own code before requesting reviews from others.
