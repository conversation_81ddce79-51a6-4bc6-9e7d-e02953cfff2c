# Google Gemini Integration Guide

## Overview
This document outlines the Google Gemini integration in WordWave Studio, which provides AI-powered script generation.

## Table of Contents
1. [Google Gemini API Setup](#google-gemini-api-setup)
2. [Environment Configuration](#environment-configuration)
3. [API Interaction](#api-interaction)
4. [Model Configuration](#model-configuration)
5. [Key Files](#key-files)

---

## 1. Google Gemini API Setup

To use the Google Gemini API, you'll need to create a project in the [Google AI Studio](https://aistudio.google.com/apikey) and get an API key.

---

## 2. Environment Configuration

The following environment variable needs to be set in your `.env.local` file:

```bash
# AI Services (Google Gemini)
GOOGLE_GEMINI_API_KEY=your_gemini_api_key_here
```

---

## 3. API Interaction

The primary interaction with the Google Gemini API happens in the `generateScript` function in `src/app/app/page.tsx`. This function constructs a detailed prompt based on the user's input and uses the `@google/generative-ai` library to generate the script.

```typescript
// src/app/app/page.tsx

const generateScript = async (
  apiKey: string,
  topic: string,
  synthesisMode: SynthesisMode,
  speaker1Name: string,
  speaker2Name: string,
  projectStyle: string,
  scriptLinks: string,
  wordCount: number,
  selectedModel: string
): Promise<{ content: string; tokenCount?: any }> => {
  // ... implementation details ...
};
```

---

## 4. Model Configuration

The application uses different models for script generation and text-to-speech. The available models are defined in `src/lib/constants.ts`.

*   **Script Generation Models**: `gemini-1.5-flash`, `gemini-1.5-pro`, etc.
*   **Text-to-Speech Models**: `tts-1`, `tts-1-hd`, etc.

The user can select which model to use from the UI.

---

## 5. Key Files

| File | Purpose |
|------|---------|
| `src/app/app/page.tsx` | Contains the `generateScript` function and handles the main interaction with the Google Gemini API. |
| `src/lib/constants.ts` | Defines the available AI models. |
