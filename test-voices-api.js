#!/usr/bin/env node

/**
 * Test script to discover available voices from Google's API
 * Usage: node test-voices-api.js [API_KEY]
 */

const fs = require('fs').promises;

async function testVoicesAPI(apiKey) {
  console.log('Testing Google API for voice information...\n');

  // 1. First, let's see what the full models API returns
  console.log('1. Fetching all models to see full response structure...');
  const modelsResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
    headers: { 'x-goog-api-key': apiKey }
  });
  
  if (!modelsResponse.ok) {
    throw new Error(`Models API failed: ${modelsResponse.status}`);
  }
  
  const modelsData = await modelsResponse.json();
  console.log(`Found ${modelsData.models.length} total models\n`);
  
  // 2. Look for TTS models specifically
  console.log('2. TTS Models found:');
  const ttsModels = modelsData.models.filter(model => 
    model.name.toLowerCase().includes('tts')
  );
  
  ttsModels.forEach(model => {
    console.log(`- ${model.name}`);
    console.log(`  Description: ${model.description || 'No description'}`);
    console.log(`  Supported methods: ${model.supportedGenerationMethods?.join(', ') || 'None listed'}`);
    console.log('');
  });

  // 3. Try to test TTS with different voice names to see what works
  if (ttsModels.length > 0) {
    console.log('3. Testing voice names with TTS model...');
    const ttsModel = ttsModels[0].name;
    
    // Test with known voice names from voice_samples directory
    const testVoices = ['achernar', 'aoede', 'charon', 'zephyr', 'invalid_voice'];
    
    for (const voiceName of testVoices) {
      try {
        console.log(`Testing voice: ${voiceName}`);
        
        const ttsResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/${ttsModel}:generateSpeech`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-goog-api-key': apiKey
          },
          body: JSON.stringify({
            text: 'Hello, this is a test.',
            voice: voiceName,
            config: {
              audioEncoding: 'MP3'
            }
          })
        });
        
        if (ttsResponse.ok) {
          console.log(`  ✅ ${voiceName} - VALID voice`);
        } else {
          const errorData = await ttsResponse.json();
          console.log(`  ❌ ${voiceName} - ${ttsResponse.status}: ${errorData.error?.message || 'Unknown error'}`);
        }
      } catch (error) {
        console.log(`  ❌ ${voiceName} - Error: ${error.message}`);
      }
    }
  }

  // 4. Check if there's any way to list available voices
  console.log('\n4. Checking for dedicated voices endpoint...');
  try {
    const voicesResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/voices', {
      headers: { 'x-goog-api-key': apiKey }
    });
    
    if (voicesResponse.ok) {
      const voicesData = await voicesResponse.json();
      console.log('✅ Found voices endpoint!');
      console.log(JSON.stringify(voicesData, null, 2));
    } else {
      console.log(`❌ No voices endpoint (${voicesResponse.status})`);
    }
  } catch (error) {
    console.log(`❌ No voices endpoint - ${error.message}`);
  }

  // 5. Let's also check the voice_samples directory to see what we actually have
  console.log('\n5. Checking voice_samples directory...');
  try {
    const voiceSamplesDir = './voice_samples';
    const files = await fs.readdir(voiceSamplesDir);
    const voiceFiles = files.filter(file => file.endsWith('_sample.wav'));
    
    console.log(`Found ${voiceFiles.length} voice sample files:`);
    const availableVoices = voiceFiles.map(file => {
      const voiceName = file.replace('_sample.wav', '');
      console.log(`  - ${voiceName}`);
      return voiceName;
    });
    
    console.log('\nThese are the actual voice names we should use in our configuration.');
    
    return availableVoices;
  } catch (error) {
    console.log(`Error reading voice_samples: ${error.message}`);
    return [];
  }
}

async function main() {
  const apiKey = process.argv[2];
  
  if (!apiKey) {
    console.error('Usage: node test-voices-api.js [API_KEY]');
    process.exit(1);
  }

  try {
    const voices = await testVoicesAPI(apiKey);
    console.log('\n=== SUMMARY ===');
    console.log('Google does not provide voice metadata like gender, age, accent through their API.');
    console.log('We need to use only the voice names that actually work with the TTS API.');
    console.log(`Found ${voices.length} voice samples in our directory.`);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
