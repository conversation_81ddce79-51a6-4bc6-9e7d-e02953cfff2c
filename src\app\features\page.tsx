import React from 'react';
import PageLayout from '@/components/PageLayout';

export default function FeaturesPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            Features
          </h1>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">AI Script Generation</h2>
              <p className="text-gray-300 leading-relaxed">
                Generate professional podcast scripts and monologues using advanced AI models. 
                Create engaging content tailored to your specific topics and style preferences.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">High-Quality Text-to-Speech</h2>
              <p className="text-gray-300 leading-relaxed">
                Convert your scripts into natural-sounding audio using state-of-the-art TTS technology. 
                Choose from multiple voices and languages for diverse content creation.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Multi-Speaker Conversations</h2>
              <p className="text-gray-300 leading-relaxed">
                Create realistic conversations between multiple speakers with distinct voices. 
                Perfect for interview-style podcasts and dialogue-based content.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Cloud Storage Integration</h2>
              <p className="text-gray-300 leading-relaxed">
                Save and manage your projects in the cloud. Access your audio content from anywhere 
                and collaborate with team members seamlessly.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Professional Audio Export</h2>
              <p className="text-gray-300 leading-relaxed">
                Export your generated audio in high-quality formats ready for publishing. 
                Compatible with all major podcast platforms and audio editing software.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Customizable Personas</h2>
              <p className="text-gray-300 leading-relaxed">
                Define unique personas for your speakers with specific characteristics, 
                speaking styles, and personality traits for consistent content creation.
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <a 
              href="/app" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get Started Free
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
