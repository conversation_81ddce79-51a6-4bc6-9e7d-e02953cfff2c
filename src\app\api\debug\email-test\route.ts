import { NextRequest, NextResponse } from 'next/server';
// Update the import path to match the actual location of EmailService
import { EmailService } from '../../../../lib/EmailService';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Email test endpoint called');
    
    // Check which email service is configured
    const emailServiceConfig = {
      resendApiKey: process.env.RESEND_API_KEY ? 'Present' : 'Missing',
      senderApiKey: process.env.SENDER_API_KEY ? 'Present' : 'Missing',
      sendgridApiKey: process.env.SENDGRID_API_KEY ? 'Present' : 'Missing',
      fromEmail: process.env.FROM_EMAIL || 'Not set',
      adminEmail: process.env.ADMIN_EMAIL || 'Not set',
      appUrl: process.env.NEXT_PUBLIC_APP_URL || 'Not set'
    };
    
    console.log('📧 Email service configuration:', emailServiceConfig);
    
    // Test the email service
    const emailService = EmailService.getInstance();
    const testResult = await emailService.testEmailService();
    
    console.log('🧪 Email service test result:', testResult);
    
    return NextResponse.json({
      success: true,
      config: emailServiceConfig,
      testResult
    });
    
  } catch (error) {
    console.error('❌ Email test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
