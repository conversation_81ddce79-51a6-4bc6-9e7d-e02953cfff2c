// Server-side CloudStorageService for Next.js - <PERSON>les Backblaze B2 operations
import B2 from 'backblaze-b2';
import { 
  ProjectData, 
  CloudProject, 
  ProjectSummary, 
  CloudStorageFile,
  UploadProgress,
  CloudStorageError,
  ValidationError,
  NotFoundError
} from './types';

export interface B2Config {
  applicationKeyId: string;
  applicationKey: string;
  bucketName: string;
}

export class ServerCloudStorageService {
  private b2: B2;
  private config: B2Config;
  private isAuthorized: boolean = false;
  private bucketId: string | null = null;
  private authExpiry: number = 0;

  constructor(config: B2Config) {
    this.config = config;
    this.b2 = new B2({
      applicationKeyId: config.applicationKeyId,
      applicationKey: config.applicationKey,
      retry: {
        retries: 3
      }
    });
  }

  /**
   * Utility function to add delays for rate limiting
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Initialize the service and authorize with B2
   */
  async initialize(): Promise<void> {
    try {
      await this.authorize();
      await this.ensureBucket();
    } catch (error) {
      console.error('❌ Failed to initialize CloudStorageService:', error);
      
      // Re-throw specific errors without wrapping them
      if ((error as any)?.code === 'INVALID_CREDENTIALS') {
        throw error;
      }
      
      throw new CloudStorageError(
        'Failed to initialize cloud storage service',
        500,
        'INIT_ERROR',
        { originalError: error }
      );
    }
  }

  /**
   * Authorize with Backblaze B2 (valid for 24 hours) and ensure bucket exists
   */
  private async authorize(): Promise<void> {
    try {
      const now = Date.now();

      // Check if we're still authorized (with 1 hour buffer)
      if (this.isAuthorized && now < this.authExpiry - 3600000 && this.bucketId) {
        console.log('🔄 Using existing B2 authorization');
        return;
      }

      console.log('🔐 Authorizing with Backblaze B2...');
      await this.b2.authorize();
      this.isAuthorized = true;
      this.authExpiry = now + 24 * 60 * 60 * 1000; // 24 hours from now

      console.log('✅ Successfully authorized with Backblaze B2');

      // Ensure bucket exists if not already set
      if (!this.bucketId) {
        console.log('🪣 Ensuring bucket exists...');
        await this.ensureBucket();
      }
    } catch (error: any) {
      this.isAuthorized = false;
      console.error('❌ Failed to authorize with Backblaze B2:', error);

      // Log more details about the error
      if (error?.response) {
        console.error('❌ B2 API Response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      }

      // Check for specific error types
      if (error?.response?.status === 401 || error?.status === 401) {
        throw new CloudStorageError(
          'Invalid Backblaze B2 credentials. Please check your B2_APPLICATION_KEY_ID and B2_APPLICATION_KEY.',
          401,
          'INVALID_CREDENTIALS',
          { originalError: error }
        );
      }

      throw new CloudStorageError(
        'Failed to authorize with Backblaze B2',
        401,
        'AUTH_ERROR',
        { originalError: error }
      );
    }
  }

  /**
   * Ensure the bucket exists and get its ID
   * Works with restricted application keys that only have access to specific buckets
   */
  private async ensureBucket(): Promise<void> {
    // For restricted application keys, we need to work around the limitation
    // that we can't list all buckets. We'll use a known bucket ID or try to
    // get it through other means.

    console.log(`🔍 Attempting to access bucket: ${this.config.bucketName}`);

    // Add delay to avoid rate limiting
    await this.delay(300);

    // Since we know the bucket name is "WordWaveStudio" and it should exist,
    // let's try to get the bucket info using getBucket with just the bucket name
    try {
      const bucketResponse = await this.b2.getBucket({
        bucketName: this.config.bucketName
      });

      if (bucketResponse.data && bucketResponse.data.buckets && bucketResponse.data.buckets.length > 0) {
        const bucket = bucketResponse.data.buckets.find((b: any) => b.bucketName === this.config.bucketName);
        if (bucket) {
          this.bucketId = bucket.bucketId;
          console.log(`✅ Found existing bucket: ${this.config.bucketName} (ID: ${this.bucketId})`);
          return;
        }
      }
    } catch (error: any) {
      console.error('❌ getBucket failed:', error);

      // Handle rate limiting
      if (error?.response?.status === 429) {
        console.log('⏳ Rate limited on getBucket, waiting 3 seconds...');
        await this.delay(3000);
      }

      // If getBucket fails with 401, the key might be restricted
      // Let's try a different approach - use a hardcoded bucket ID if we know it
      if (error?.response?.status === 401) {
        console.log('🔄 Restricted key detected, trying alternative approach...');

        // From your previous logs, we know the bucket ID is c72335d7a81c8bd8937f0a12
        // Let's try using this known bucket ID
        const knownBucketId = 'c72335d7a81c8bd8937f0a12';

        try {
          // Test if we can access this bucket by trying to list files
          const testResponse = await this.b2.listFileNames({
            bucketId: knownBucketId,
            maxFileCount: 1,
            startFileName: '',
            delimiter: '',
            prefix: ''
          });

          if (testResponse.data) {
            this.bucketId = knownBucketId;
            console.log(`✅ Successfully accessed bucket using known ID: ${this.config.bucketName} (ID: ${this.bucketId})`);
            return;
          }
        } catch (testError: any) {
          console.error('❌ Failed to access bucket with known ID:', testError);
        }

        throw new CloudStorageError(
          `Insufficient permissions to access bucket '${this.config.bucketName}'. Please check that your B2 application key has access to this bucket.`,
          401,
          'BUCKET_PERMISSION_ERROR',
          { originalError: error }
        );
      }
    }

    // If we get here, try to create the bucket (for unrestricted keys)
    console.log(`📦 Bucket not found, attempting to create: ${this.config.bucketName}`);
    try {
      const createResponse = await this.b2.createBucket({
        bucketName: this.config.bucketName,
        bucketType: 'allPrivate'
      });
      this.bucketId = createResponse.data.bucketId;
      console.log(`✅ Successfully created bucket: ${this.config.bucketName} (ID: ${this.bucketId})`);
      return;
    } catch (createError: any) {
      console.error('❌ Failed to create bucket:', createError);

      if (createError?.response?.status === 401) {
        throw new CloudStorageError(
          `Cannot create bucket '${this.config.bucketName}'. Your B2 application key may be restricted to existing buckets only.`,
          401,
          'BUCKET_CREATE_PERMISSION_ERROR',
          { originalError: createError }
        );
      }

      throw new CloudStorageError(
        'Failed to create bucket',
        500,
        'BUCKET_CREATE_ERROR',
        { originalError: createError }
      );
    }
  }

  /**
   * Save project configuration only (new separated approach)
   */
  async saveProjectConfig(
    projectData: ProjectData,
    userId: string
  ): Promise<CloudProject> {
    try {
      await this.authorize();

      const projectPath = `users/${userId}/projects/${projectData.id}`;
      const metadataPath = `${projectPath}/project.json`;

      // Upload project configuration
      const projectJson = JSON.stringify(projectData, null, 2);
      const projectBuffer = Buffer.from(projectJson, 'utf-8');

      await this.uploadFile(metadataPath, projectBuffer, 'application/json');

      console.log(`✅ Successfully saved project configuration ${projectData.id}`);

      return {
        projectId: projectData.id,
        projectName: projectData.configuration.projectName,
        userId,
        version: 1,
        createdAt: projectData.metadata.createdAt,
        updatedAt: projectData.metadata.updatedAt,
        files: [{
          fileName: 'project.json',
          filePath: metadataPath,
          size: projectBuffer.length,
          mimeType: 'application/json',
          uploadedAt: new Date().toISOString(),
        }],
        metadata: projectData.metadata,
        cloudPath: projectPath,
      };
    } catch (error) {
      console.error('❌ Failed to save project configuration:', error);
      throw new CloudStorageError(
        'Failed to save project configuration',
        500,
        'SAVE_PROJECT_CONFIG_ERROR',
        { originalError: error, projectId: projectData.id, userId }
      );
    }
  }

  /**
   * Update project configuration
   */
  async updateProjectConfig(
    projectId: string,
    userId: string,
    newConfig: any
  ): Promise<CloudProject> {
    try {
      await this.authorize();

      const projectPath = `users/${userId}/projects/${projectId}`;
      const metadataPath = `${projectPath}/project.json`;

      // Load existing project data
      const existingProjectData = await this.loadProjectConfig(projectId, userId);

      // Update configuration and metadata
      const updatedProjectData: ProjectData = {
        ...existingProjectData,
        configuration: {
          ...existingProjectData.configuration,
          ...newConfig,
        },
        metadata: {
          ...existingProjectData.metadata,
          updatedAt: new Date().toISOString(),
        },
      };

      // Upload updated project configuration
      const projectJson = JSON.stringify(updatedProjectData, null, 2);
      const projectBuffer = Buffer.from(projectJson, 'utf-8');

      await this.uploadFile(metadataPath, projectBuffer, 'application/json');

      console.log(`✅ Successfully updated project configuration ${projectId}`);

      return {
        projectId: projectId,
        projectName: updatedProjectData.configuration.projectName,
        userId,
        version: Number(existingProjectData.metadata.version || 1) + 1,
        createdAt: existingProjectData.metadata.createdAt,
        updatedAt: updatedProjectData.metadata.updatedAt,
        files: [], // Not handling file lists in this update
        metadata: updatedProjectData.metadata,
        cloudPath: projectPath,
      };
    } catch (error) {
      console.error('❌ Failed to update project configuration:', error);
      throw new CloudStorageError(
        'Failed to update project configuration',
        500,
        'UPDATE_PROJECT_CONFIG_ERROR',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * Save a project to B2 (legacy method for backward compatibility)
   */
  async saveProject(
    projectData: ProjectData,
    userId: string,
    onProgress?: (progress: UploadProgress[]) => void
  ): Promise<CloudProject> {
    try {
      await this.authorize();
      
      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const projectPath = `users/${userId}/projects/${projectData.id}`;
      const files: CloudStorageFile[] = [];
      const progressMap = new Map<string, UploadProgress>();

      // Helper function to update progress
      const updateProgress = (fileName: string, update: Partial<UploadProgress>) => {
        const current = progressMap.get(fileName) || {
          fileName,
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 0,
          status: 'pending' as const
        };
        
        const updated = { ...current, ...update };
        progressMap.set(fileName, updated);
        
        if (onProgress) {
          onProgress(Array.from(progressMap.values()));
        }
      };

      // 1. Upload project metadata
      const metadataFileName = 'project.json';
      const metadataContent = JSON.stringify(projectData, null, 2);
      const metadataBuffer = Buffer.from(metadataContent, 'utf-8');
      
      updateProgress(metadataFileName, { 
        totalBytes: metadataBuffer.length, 
        status: 'uploading' 
      });

      const metadataUpload = await this.uploadFile(
        `${projectPath}/${metadataFileName}`,
        metadataBuffer,
        'application/json'
      );

      files.push({
        fileName: metadataFileName,
        filePath: `${projectPath}/${metadataFileName}`,
        fileId: metadataUpload.fileId,
        size: metadataBuffer.length,
        mimeType: 'application/json',
        uploadedAt: new Date().toISOString(),
        sha1Hash: metadataUpload.contentSha1
      });

      updateProgress(metadataFileName, { 
        bytesUploaded: metadataBuffer.length,
        percentage: 100,
        status: 'completed' 
      });

      // 2. Upload audio files if they exist (legacy support)
      const audioFiles = (projectData as any).audioFiles || [];
      for (const audioFile of audioFiles) {
        if (audioFile.url && audioFile.url.startsWith('blob:')) {
          try {
            // Fetch the blob data
            const response = await fetch(audioFile.url);
            const arrayBuffer = await response.arrayBuffer();
            const buffer = Buffer.from(arrayBuffer);
            
            const audioFileName = audioFile.name || `audio_${audioFile.id}.wav`;
            
            updateProgress(audioFileName, { 
              totalBytes: buffer.length, 
              status: 'uploading' 
            });

            const audioUpload = await this.uploadFile(
              `${projectPath}/audio/${audioFileName}`,
              buffer,
              audioFile.processedMimeType || 'audio/wav'
            );

            files.push({
              fileName: audioFileName,
              filePath: `${projectPath}/audio/${audioFileName}`,
              fileId: audioUpload.fileId,
              size: buffer.length,
              mimeType: audioFile.processedMimeType || 'audio/wav',
              uploadedAt: new Date().toISOString(),
              sha1Hash: audioUpload.contentSha1
            });

            updateProgress(audioFileName, { 
              bytesUploaded: buffer.length,
              percentage: 100,
              status: 'completed' 
            });
          } catch (error) {
            console.error(`Failed to upload audio file ${audioFile.name}:`, error);
            updateProgress(audioFile.name || audioFile.id, { 
              status: 'error',
              error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
          }
        }
      }

      const cloudProject: CloudProject = {
        projectId: projectData.id,
        projectName: projectData.configuration.projectName,
        userId,
        version: 1,
        createdAt: projectData.metadata.createdAt,
        updatedAt: new Date().toISOString(),
        files,
        metadata: {
          ...projectData.metadata,
          userId,
          updatedAt: new Date().toISOString()
        },
        cloudPath: projectPath
      };

      console.log(`✅ Successfully saved project ${projectData.id} with ${files.length} files`);
      return cloudProject;
    } catch (error) {
      console.error('❌ Failed to save project:', error);
      throw new CloudStorageError(
        'Failed to save project to cloud storage',
        500,
        'SAVE_ERROR',
        { originalError: error, projectId: projectData.id }
      );
    }
  }

  /**
   * Load a project from B2
   */
  async loadProject(projectId: string, userId: string): Promise<ProjectData> {
    try {
      await this.authorize();

      const projectPath = `users/${userId}/projects/${projectId}`;
      const metadataPath = `${projectPath}/project.json`;

      // Download project metadata
      const metadataFile = await this.downloadFile(metadataPath);
      const projectData: ProjectData = JSON.parse(metadataFile.toString('utf-8'));

      console.log(`✅ Successfully loaded project ${projectId}`);
      return projectData;
    } catch (error) {
      console.error('❌ Failed to load project:', error);
      throw new NotFoundError(
        'Project not found or failed to load',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * List projects for a user
   */
  async listProjects(userId: string, limit: number = 50): Promise<ProjectSummary[]> {
    try {
      await this.authorize();

      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const userPath = `users/${userId}/projects/`;

      const response = await this.b2.listFileNames({
        bucketId: this.bucketId!,
        startFileName: userPath,
        maxFileCount: limit * 2,
        prefix: userPath,
        delimiter: ''
      });

      const projects: ProjectSummary[] = [];

      if (response.data && response.data.files) {
        const projectFiles = response.data.files.filter((file: any) =>
          file.fileName.endsWith('/project.json')
        );

        for (const file of projectFiles) {
          try {
            const projectData = await this.downloadFile(file.fileName);
            const parsed: ProjectData = JSON.parse(projectData.toString('utf-8'));

            projects.push({
              id: parsed.id,
              projectId: parsed.id,
              name: parsed.configuration.projectName,
              projectName: parsed.configuration.projectName,
              updatedAt: parsed.metadata.updatedAt,
              fileCount: ((parsed as any).audioFiles?.length || 0) + 1,
              totalSize: file.size || 0,
              tags: parsed.metadata.tags,
              description: parsed.metadata.description
            });
          } catch (error) {
            console.warn(`Failed to parse project file ${file.fileName}:`, error);
          }
        }
      }

      console.log(`✅ Found ${projects.length} projects for user ${userId}`);
      return projects;
    } catch (error) {
      console.error('❌ Failed to list projects:', error);
      throw new CloudStorageError(
        'Failed to list projects',
        500,
        'LIST_ERROR',
        { originalError: error, userId }
      );
    }
  }

  /**
   * Delete a project from B2
   */
  async deleteProject(projectId: string, userId: string): Promise<void> {
    try {
      await this.authorize();

      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const projectPath = `users/${userId}/projects/${projectId}/`;

      const response = await this.b2.listFileNames({
        bucketId: this.bucketId!,
        startFileName: projectPath,
        maxFileCount: 1000,
        prefix: projectPath,
        delimiter: ''
      });

      if (response.data && response.data.files) {
        for (const file of response.data.files) {
          await this.b2.deleteFileVersion({
            fileId: file.fileId,
            fileName: file.fileName
          });
        }
      }

      console.log(`✅ Successfully deleted project ${projectId}`);
    } catch (error: any) {
      console.error('❌ Failed to delete project:', error);
      throw new CloudStorageError(
        'Failed to delete project',
        500,
        'DELETE_ERROR',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * Delete an audio file from B2
   */
  async deleteAudio(audioId: string, projectId: string, userId: string): Promise<void> {
    try {
      await this.authorize();

      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const audioPath = `users/${userId}/projects/${projectId}/audio/${audioId}`;
      console.log(`🔍 Searching for files to delete at path: ${audioPath}`);

      const response = await this.b2.listFileNames({
        bucketId: this.bucketId!,
        startFileName: audioPath,
        maxFileCount: 100,
        prefix: audioPath,
        delimiter: ''
      });

      console.log(`📋 List files response:`, {
        filesCount: response.data?.files?.length || 0,
        files: response.data?.files?.map((f: { fileName: string; fileId: string; size?: number }) => ({
          fileName: f.fileName,
          fileId: f.fileId,
          size: f.size
        })) || []
      });

      if (response.data && response.data.files && response.data.files.length > 0) {
        console.log(`🗑️ Deleting ${response.data.files.length} files...`);

        for (const file of response.data.files) {
          console.log(`🗑️ Deleting file: ${file.fileName} (ID: ${file.fileId})`);

          try {
            await this.b2.deleteFileVersion({
              fileId: file.fileId,
              fileName: file.fileName
            });
            console.log(`✅ Successfully deleted file: ${file.fileName}`);
          } catch (deleteError) {
            console.error(`❌ Failed to delete individual file ${file.fileName}:`, deleteError);
            // Continue with other files instead of failing completely
          }
        }
      } else {
        console.log(`⚠️ No files found to delete at path: ${audioPath}`);
      }

      console.log(`✅ Successfully deleted audio ${audioId} for project ${projectId}, user ${userId}`);
    } catch (error) {
      console.error('❌ Failed to delete audio:', error);
      throw new CloudStorageError(
        'Failed to delete audio',
        500,
        'DELETE_AUDIO_ERROR',
        { originalError: error, audioId, projectId, userId }
      );
    }
  }

  /**
   * Upload a single file to B2
   */
  private async uploadFile(
    fileName: string,
    buffer: Buffer,
    mimeType: string
  ): Promise<{ fileId: string; contentSha1: string }> {
    try {
      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const uploadUrlResponse = await this.b2.getUploadUrl({
        bucketId: this.bucketId
      });

      const uploadResponse = await this.b2.uploadFile({
        uploadUrl: uploadUrlResponse.data.uploadUrl,
        uploadAuthToken: uploadUrlResponse.data.authorizationToken,
        fileName: fileName,
        data: buffer,
        mime: mimeType
      });

      return {
        fileId: uploadResponse.data.fileId,
        contentSha1: uploadResponse.data.contentSha1
      };
    } catch (error) {
      console.error(`❌ Failed to upload file ${fileName}:`, error);
      throw new CloudStorageError(
        'Failed to upload file',
        500,
        'UPLOAD_ERROR',
        { originalError: error, fileName }
      );
    }
  }

  /**
   * Download a file from B2
   */
  private async downloadFile(fileName: string): Promise<Buffer> {
    try {
      const response = await this.b2.downloadFileByName({
        bucketName: this.config.bucketName,
        fileName: fileName,
        responseType: 'arraybuffer'
      });

      return Buffer.from(response.data);
    } catch (error) {
      console.error(`❌ Failed to download file ${fileName}:`, error);
      throw new CloudStorageError(
        'Failed to download file',
        500,
        'DOWNLOAD_ERROR',
        { originalError: error, fileName }
      );
    }
  }

  /**
   * Public method to download a file (for GDPR exports, etc.)
   */
  async downloadFilePublic(fileName: string): Promise<Buffer> {
    await this.authorize();
    return this.downloadFile(fileName);
  }

  /**
   * List files in a specific path
   */
  private async listFiles(path: string, maxFiles: number = 1000): Promise<any[]> {
    try {
      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      const response = await this.b2.listFileNames({
        bucketId: this.bucketId!,
        startFileName: path,
        maxFileCount: maxFiles,
        prefix: path,
        delimiter: ''
      });

      return response.data?.files || [];
    } catch (error) {
      console.error(`❌ Failed to list files in path ${path}:`, error);
      throw new CloudStorageError(
        'Failed to list files',
        500,
        'LIST_FILES_ERROR',
        { originalError: error, path }
      );
    }
  }

  /**
   * Save a script for a project
   */
  async saveScript(scriptData: any, userId: string): Promise<any> {
    try {
      await this.authorize();

      const scriptPath = `users/${userId}/projects/${scriptData.projectId}/scripts/${scriptData.id}`;
      const scriptFilePath = `${scriptPath}/script.json`;

      // Upload script data
      const scriptJson = JSON.stringify(scriptData, null, 2);
      const scriptBuffer = Buffer.from(scriptJson, 'utf-8');

      await this.uploadFile(scriptFilePath, scriptBuffer, 'application/json');

      console.log(`✅ Successfully saved script ${scriptData.id} for project ${scriptData.projectId}`);

      return {
        scriptId: scriptData.id,
        projectId: scriptData.projectId,
        cloudPath: scriptPath,
        savedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('❌ Failed to save script:', error);
      throw new CloudStorageError(
        'Failed to save script',
        500,
        'SAVE_SCRIPT_ERROR',
        { originalError: error, scriptId: scriptData.id, projectId: scriptData.projectId, userId }
      );
    }
  }

  /**
   * Save audio for a project
   */
  async saveAudio(audioData: any, userId: string): Promise<any> {
    try {
      await this.authorize();

      // Final validation - ensure we have files with actual audio data
      if (!audioData.files || audioData.files.length === 0) {
        console.error(`❌ CloudStorageService: No audio files provided for ${audioData.id}`);
        throw new CloudStorageError(
          'Cannot save audio: No audio files provided',
          400,
          'NO_AUDIO_FILES',
          { audioId: audioData.id, projectId: audioData.projectId }
        );
      }

      // Check if any files have base64 data (required for upload)
      const hasValidData = audioData.files.some((file: any) => file.base64Data && file.base64Data.length > 0);

      if (!hasValidData) {
        console.error(`❌ CloudStorageService: No valid audio data found for ${audioData.id}`);
        throw new CloudStorageError(
          'Cannot save audio: No valid audio data found in files',
          400,
          'NO_AUDIO_DATA',
          { audioId: audioData.id, projectId: audioData.projectId, fileCount: audioData.files.length }
        );
      }

      const audioPath = `users/${userId}/projects/${audioData.projectId}/audio/${audioData.id}`;
      const audioMetadataPath = `${audioPath}/audio.json`;

      console.log(`🎵 Saving audio ${audioData.id} with ${audioData.files?.length || 0} files`);

      // Log file details for debugging
      if (audioData.files) {
        audioData.files.forEach((file: any, index: number) => {
          console.log(`📁 File ${index + 1}: ${file.name}`);
          console.log(`   - Has URL: ${!!file.url}`);
          console.log(`   - Has base64Data: ${!!file.base64Data}`);
          console.log(`   - Size: ${file.size || 'unknown'}`);
          console.log(`   - MIME: ${file.processedMimeType}`);
        });
      }

      // Upload audio files first and track which ones were successfully uploaded
      const uploadedFiles = [];

      for (let i = 0; i < audioData.files.length; i++) {
        const audioFile = audioData.files[i];

        // Handle base64 data (new approach)
        if (audioFile.base64Data) {
          try {
            const buffer = Buffer.from(audioFile.base64Data, 'base64');
            const audioFilePath = `${audioPath}/${audioFile.name}`;
            await this.uploadFile(audioFilePath, buffer, audioFile.processedMimeType);

            console.log(`✅ Uploaded audio file ${audioFile.name} (${buffer.length} bytes)`);

            // Add to uploaded files list (without base64Data to save space)
            uploadedFiles.push({
              ...audioFile,
              base64Data: undefined, // Remove base64 data from metadata
              cloudPath: audioFilePath,
              uploadedAt: new Date().toISOString()
            });
          } catch (fileError) {
            console.error(`❌ Failed to upload audio file ${audioFile.name}:`, fileError);
          }
        }
        // Legacy support for blob URLs (will fail on server but kept for compatibility)
        else if (audioFile.url && audioFile.url.startsWith('blob:')) {
          try {
            console.warn(`⚠️ Attempting to fetch blob URL on server for ${audioFile.name} - this will likely fail`);
            const response = await fetch(audioFile.url);
            const arrayBuffer = await response.arrayBuffer();
            const buffer = Buffer.from(arrayBuffer);

            const audioFilePath = `${audioPath}/${audioFile.name}`;
            await this.uploadFile(audioFilePath, buffer, audioFile.processedMimeType);

            console.log(`✅ Uploaded audio file ${audioFile.name}`);

            // Add to uploaded files list
            uploadedFiles.push({
              ...audioFile,
              url: undefined, // Remove blob URL from metadata
              cloudPath: audioFilePath,
              uploadedAt: new Date().toISOString()
            });
          } catch (fileError) {
            console.error(`❌ Failed to upload audio file ${audioFile.name}:`, fileError);
          }
        }
        else {
          console.warn(`⚠️ Audio file ${audioFile.name} has no base64Data or valid URL - skipping upload`);
        }
      }

      // Only save metadata if at least one audio file was successfully uploaded
      if (uploadedFiles.length === 0) {
        console.error(`❌ No audio files were successfully uploaded for ${audioData.id}`);
        throw new Error(`Failed to upload any audio files for ${audioData.name || audioData.id}. No metadata will be saved.`);
      }

      // Update the audio metadata to only include successfully uploaded files
      const audioMetadataToSave = {
        ...audioData,
        files: uploadedFiles, // Only include uploaded files
        metadata: {
          ...audioData.metadata,
          fileCount: uploadedFiles.length,
          totalSize: uploadedFiles.reduce((sum, file) => sum + (file.size || 0), 0),
          uploadedAt: new Date().toISOString()
        }
      };

      // Upload the corrected metadata (only includes successfully uploaded files)
      const correctedAudioBuffer = Buffer.from(JSON.stringify(audioMetadataToSave, null, 2), 'utf-8');
      await this.uploadFile(audioMetadataPath, correctedAudioBuffer, 'application/json');
      console.log(`✅ Uploaded audio metadata with ${uploadedFiles.length} uploaded files to ${audioMetadataPath}`);

      console.log(`✅ Successfully saved audio ${audioData.id} for project ${audioData.projectId} with ${uploadedFiles.length} files`);

      return {
        audioId: audioData.id,
        projectId: audioData.projectId,
        cloudPath: audioPath,
        savedAt: new Date().toISOString(),
        uploadedFileCount: uploadedFiles.length,
        totalFileCount: audioData.files?.length || 0
      };
    } catch (error) {
      console.error('❌ Failed to save audio:', error);
      throw new CloudStorageError(
        'Failed to save audio',
        500,
        'SAVE_AUDIO_ERROR',
        { originalError: error, audioId: audioData.id, projectId: audioData.projectId, userId }
      );
    }
  }

  /**
   * Load project configuration only
   */
  async loadProjectConfig(projectId: string, userId: string): Promise<ProjectData> {
    try {
      await this.authorize();

      const projectPath = `users/${userId}/projects/${projectId}`;
      const metadataPath = `${projectPath}/project.json`;

      // Download project metadata
      const metadataFile = await this.downloadFile(metadataPath);
      const projectData: ProjectData = JSON.parse(metadataFile.toString('utf-8'));

      console.log(`✅ Successfully loaded project configuration ${projectId}`);
      return projectData;
    } catch (error) {
      console.error('❌ Failed to load project configuration:', error);
      throw new NotFoundError(
        'Project configuration not found or failed to load',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * List scripts for a project
   */
  async listScripts(projectId: string, userId: string): Promise<any[]> {
    try {
      await this.authorize();

      const scriptsPath = `users/${userId}/projects/${projectId}/scripts/`;
      const scriptFiles = await this.listFiles(scriptsPath);

      const scripts = [];
      for (const file of scriptFiles) {
        if (file.fileName.endsWith('/script.json')) {
          try {
            const scriptData = await this.downloadFile(file.fileName);
            const parsed = JSON.parse(scriptData.toString('utf-8'));
            scripts.push({
              id: parsed.id,
              name: parsed.name,
              createdAt: parsed.metadata.createdAt,
              updatedAt: parsed.metadata.updatedAt,
              wordCount: parsed.metadata.wordCount,
              description: parsed.metadata.description,
            });
          } catch (parseError) {
            console.warn(`Failed to parse script file ${file.fileName}:`, parseError);
          }
        }
      }

      console.log(`✅ Found ${scripts.length} scripts for project ${projectId}`);
      return scripts;
    } catch (error) {
      console.error('❌ Failed to list scripts:', error);
      throw new CloudStorageError(
        'Failed to list scripts',
        500,
        'LIST_SCRIPTS_ERROR',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * Load a specific script
   */
  async loadScript(scriptId: string, projectId: string, userId: string): Promise<any> {
    try {
      await this.authorize();

      const scriptPath = `users/${userId}/projects/${projectId}/scripts/${scriptId}/script.json`;
      const scriptData = await this.downloadFile(scriptPath);
      const parsed = JSON.parse(scriptData.toString('utf-8'));

      console.log(`✅ Successfully loaded script ${scriptId} for project ${projectId}`);
      return parsed;
    } catch (error) {
      console.error(`❌ Failed to load script ${scriptId}:`, error);
      throw new CloudStorageError(
        'Failed to load script',
        500,
        'LOAD_SCRIPT_ERROR',
        { originalError: error, scriptId, projectId, userId }
      );
    }
  }

  /**
   * Update a specific script
   */
  async updateScript(scriptId: string, projectId: string, userId: string, updates: any): Promise<any> {
    try {
      await this.authorize();

      // First load the existing script
      const existingScript = await this.loadScript(scriptId, projectId, userId);

      // Merge updates
      const updatedScript = {
        ...existingScript,
        ...updates,
        metadata: {
          ...existingScript.metadata,
          ...updates.metadata,
          updatedAt: new Date().toISOString(),
        }
      };

      // Save the updated script
      const result = await this.saveScript(updatedScript, userId);

      console.log(`✅ Successfully updated script ${scriptId} for project ${projectId}`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to update script ${scriptId}:`, error);
      throw new CloudStorageError(
        'Failed to update script',
        500,
        'UPDATE_SCRIPT_ERROR',
        { originalError: error, scriptId, projectId, userId }
      );
    }
  }

  /**
   * Delete a specific script
   */
  async deleteScript(scriptId: string, projectId: string, userId: string): Promise<void> {
    try {
      await this.authorize();

      const scriptPath = `users/${userId}/projects/${projectId}/scripts/${scriptId}/`;
      const scriptFiles = await this.listFiles(scriptPath);

      // Delete all files in the script directory
      for (const file of scriptFiles) {
        await this.deleteFile(file.fileName);
      }

      console.log(`✅ Successfully deleted script ${scriptId} for project ${projectId}`);
    } catch (error) {
      console.error(`❌ Failed to delete script ${scriptId}:`, error);
      throw new CloudStorageError(
        'Failed to delete script',
        500,
        'DELETE_SCRIPT_ERROR',
        { originalError: error, scriptId, projectId, userId }
      );
    }
  }

  /**
   * List audio for a project
   */
  async listAudio(projectId: string, userId: string, scriptId?: string): Promise<any[]> {
    try {
      await this.authorize();

      const audioPath = `users/${userId}/projects/${projectId}/audio/`;
      const audioFiles = await this.listFiles(audioPath);

      console.log(`📋 Found ${audioFiles.length} files in audio path, filtering for audio.json files...`);

      const audioList = [];
      for (const file of audioFiles) {
        if (file.fileName.endsWith('/audio.json')) {
          try {
            console.log(`📋 Processing audio metadata file: ${file.fileName}`);
            const audioData = await this.downloadFile(file.fileName);
            const parsed = JSON.parse(audioData.toString('utf-8'));

            console.log(`📋 Audio metadata parsed:`, {
              id: parsed.id,
              name: parsed.name,
              fileCount: parsed.files?.length || 0,
              files: parsed.files?.map((f: any) => f.name) || []
            });

            // Filter by scriptId if provided
            if (scriptId && parsed.scriptId !== scriptId) {
              console.log(`📋 Skipping audio ${parsed.id} - script filter mismatch`);
              continue;
            }

            // Validate that at least one audio file exists before including in results
            let hasValidAudioFile = false;
            const validatedFiles = [];

            if (parsed.files && parsed.files.length > 0) {
              for (const audioFile of parsed.files) {
                const audioFilePath = file.fileName.replace('/audio.json', `/${audioFile.name}`);

                try {
                  // Check if the audio file exists by attempting to get its info
                  const audioFileExists = audioFiles.some(f => f.fileName === audioFilePath);

                  if (audioFileExists) {
                    console.log(`✅ Validated audio file exists: ${audioFilePath}`);
                    validatedFiles.push(audioFile);
                    hasValidAudioFile = true;
                  } else {
                    console.warn(`⚠️ Audio file missing: ${audioFilePath}`);
                  }
                } catch (error) {
                  console.warn(`⚠️ Failed to validate audio file ${audioFilePath}:`, error);
                }
              }
            }

            // Only include audio entries that have at least one valid audio file
            if (hasValidAudioFile) {
              audioList.push({
                id: parsed.id,
                name: parsed.name,
                scriptId: parsed.scriptId,
                createdAt: parsed.metadata.createdAt,
                updatedAt: parsed.metadata.updatedAt,
                fileCount: validatedFiles.length, // Use validated file count
                duration: parsed.metadata.duration,
                voiceConfig: parsed.voiceConfig,
                description: parsed.metadata.description,
                // Add missing metadata fields
                files: validatedFiles, // Only include validated files
                ttsModel: parsed.ttsModel,
                synthesisMode: parsed.voiceConfig?.synthesisMode,
                totalSize: validatedFiles.reduce((sum, file) => sum + (file.size || 0), 0), // Recalculate size
                generatedAt: parsed.generatedAt,
                scriptContent: parsed.scriptContent, // Include script content
                scriptTopic: parsed.scriptTopic, // Include script topic
              });
              console.log(`✅ Added validated audio entry: ${parsed.id} with ${validatedFiles.length} files`);
            } else {
              console.warn(`⚠️ Skipping audio ${parsed.id} - no valid audio files found`);
            }
          } catch (parseError) {
            console.warn(`Failed to parse audio file ${file.fileName}:`, parseError);
          }
        }
      }

      console.log(`✅ Found ${audioList.length} audio files for project ${projectId}${scriptId ? ` (script ${scriptId})` : ''}`);
      return audioList;
    } catch (error) {
      console.error('❌ Failed to list audio:', error);
      throw new CloudStorageError(
        'Failed to list audio',
        500,
        'LIST_AUDIO_ERROR',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * Clean up orphaned audio metadata files that don't have corresponding audio files
   */
  async cleanupOrphanedAudioMetadata(projectId: string, userId: string): Promise<{ cleaned: number; errors: string[] }> {
    try {
      await this.authorize();

      const audioPath = `users/${userId}/projects/${projectId}/audio/`;
      const audioFiles = await this.listFiles(audioPath);

      console.log(`🧹 Starting cleanup for project ${projectId}, found ${audioFiles.length} files`);

      let cleanedCount = 0;
      const errors: string[] = [];

      // Find all audio.json files
      const metadataFiles = audioFiles.filter(file => file.fileName.endsWith('/audio.json'));

      for (const metadataFile of metadataFiles) {
        try {
          console.log(`🔍 Checking metadata file: ${metadataFile.fileName}`);

          // Download and parse the metadata
          const audioData = await this.downloadFile(metadataFile.fileName);
          const parsed = JSON.parse(audioData.toString('utf-8'));

          // Check if any of the referenced audio files exist
          let hasValidAudioFile = false;

          if (parsed.files && parsed.files.length > 0) {
            for (const audioFile of parsed.files) {
              const audioFilePath = metadataFile.fileName.replace('/audio.json', `/${audioFile.name}`);
              const audioFileExists = audioFiles.some(f => f.fileName === audioFilePath);

              if (audioFileExists) {
                hasValidAudioFile = true;
                break;
              }
            }
          }

          // If no valid audio files exist, delete the orphaned metadata
          if (!hasValidAudioFile) {
            console.log(`🗑️ Deleting orphaned metadata: ${metadataFile.fileName}`);
            await this.deleteFile(metadataFile.fileName);
            cleanedCount++;
            console.log(`✅ Deleted orphaned metadata: ${metadataFile.fileName}`);
          } else {
            console.log(`✅ Metadata file has valid audio files: ${metadataFile.fileName}`);
          }
        } catch (error) {
          const errorMsg = `Failed to process ${metadataFile.fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
      }

      console.log(`🧹 Cleanup completed: ${cleanedCount} orphaned metadata files removed, ${errors.length} errors`);

      return {
        cleaned: cleanedCount,
        errors
      };
    } catch (error) {
      console.error('❌ Failed to cleanup orphaned audio metadata:', error);
      throw new CloudStorageError(
        'Failed to cleanup orphaned audio metadata',
        500,
        'CLEANUP_ERROR',
        { originalError: error, projectId, userId }
      );
    }
  }

  /**
   * Delete a file from B2 storage
   */
  private async deleteFile(fileName: string): Promise<void> {
    try {
      if (!this.bucketId) {
        throw new CloudStorageError('Bucket not initialized', 500, 'BUCKET_ERROR');
      }

      // First, get file info to get the file ID
      const fileInfo = await this.b2.listFileNames({
        bucketId: this.bucketId,
        startFileName: fileName,
        maxFileCount: 1,
        prefix: fileName,
        delimiter: ''
      });

      if (!fileInfo.data?.files || fileInfo.data.files.length === 0) {
        throw new CloudStorageError(`File not found: ${fileName}`, 404, 'FILE_NOT_FOUND');
      }

      const file = fileInfo.data.files[0];

      // Delete the file
      await this.b2.deleteFileVersion({
        fileId: file.fileId,
        fileName: file.fileName
      });

      console.log(`🗑️ Successfully deleted file: ${fileName}`);
    } catch (error) {
      console.error(`❌ Failed to delete file ${fileName}:`, error);
      throw new CloudStorageError(
        `Failed to delete file: ${fileName}`,
        500,
        'DELETE_FILE_ERROR',
        { originalError: error, fileName }
      );
    }
  }

  /**
   * Public method for GDPR account deletion - List all files for a user
   */
  async listUserFiles(userId: string): Promise<any[]> {
    const userPath = `users/${userId}/`;
    return await this.listFiles(userPath);
  }

  /**
   * Public method for GDPR account deletion - List export files for a user  
   */
  async listUserExportFiles(userId: string): Promise<any[]> {
    const exportPath = `exports/${userId}/`;
    try {
      return await this.listFiles(exportPath);
    } catch (error) {
      // Return empty array if no export files exist
      return [];
    }
  }

  /**
   * Public method for GDPR account deletion - Delete a specific file
   */
  async deleteUserFile(fileName: string): Promise<void> {
    return await this.deleteFile(fileName);
  }
}
