import React from 'react';
import PageLayout from '@/components/PageLayout';
import GDPRJobMonitor from '@/components/GDPRJobMonitor';
import { Shield, Activity, Database, Clock } from 'lucide-react';

export default function GDPRAdminPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Shield className="w-12 h-12 text-blue-400" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                GDPR Admin Dashboard
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Monitor and manage GDPR data requests and background processing jobs
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid md:grid-cols-4 gap-6 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Activity className="w-8 h-8 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Active Jobs</h3>
              <p className="text-sm text-gray-300">Currently processing requests</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Clock className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Queue Status</h3>
              <p className="text-sm text-gray-300">Pending background jobs</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Database className="w-8 h-8 text-blue-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Data Exports</h3>
              <p className="text-sm text-gray-300">Completed export requests</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <Shield className="w-8 h-8 text-purple-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Compliance</h3>
              <p className="text-sm text-gray-300">GDPR compliance status</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10">
            <GDPRJobMonitor />
          </div>

          {/* Additional Information */}
          <div className="mt-12 bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
            <h2 className="text-2xl font-semibold text-white mb-6">System Information</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Background Processing</h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• Automated data collection from all sources</li>
                  <li>• Secure ZIP archive generation</li>
                  <li>• Email notifications upon completion</li>
                  <li>• Retry mechanism for failed jobs</li>
                  <li>• Automatic cleanup of old export files</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Data Sources</h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• Clerk user accounts and profiles</li>
                  <li>• Stripe subscription and billing data</li>
                  <li>• Backblaze B2 project files and audio</li>
                  <li>• Usage analytics and activity logs</li>
                  <li>• Support communications and tickets</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-8 pt-6 border-t border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">Security & Compliance</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-2">Data Export Security</h4>
                  <p className="text-sm text-gray-300">7-day expiration, encrypted storage, secure download links</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">GDPR Compliance</h4>
                  <p className="text-sm text-gray-300">Article 15 (Right of Access), Article 17 (Right to Erasure)</p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Audit Trail</h4>
                  <p className="text-sm text-gray-300">Complete logging of all privacy-related activities</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="mt-8 text-center">
            <p className="text-gray-300">
              Questions about GDPR compliance or system administration? 
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 ml-1">
                Contact the Privacy Team
              </a>
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
