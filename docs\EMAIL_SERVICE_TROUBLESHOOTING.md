# Email Service Configuration and Troubleshooting Guide

## Overview
This document details the complete process of setting up and troubleshooting the email notification system for GDPR data exports, including all the issues encountered and their solutions.

## Table of Contents
1. [Initial Problem](#initial-problem)
2. [Root Cause Analysis](#root-cause-analysis)
3. [Solution Implementation](#solution-implementation)
4. [Key Learnings](#key-learnings)
5. [Configuration Details](#configuration-details)
6. [Troubleshooting Steps](#troubleshooting-steps)
7. [Best Practices](#best-practices)

## Initial Problem

### Symptoms
- GDPR data export was working perfectly locally during development
- After deploying to Vercel production, the export functionality worked (files were generated and stored)
- Download links were created and functional
- **BUT: Email notifications were not being sent to users**
- Specific issue: Emails worked for `<EMAIL>` but not for `<EMAIL>`

### Error Manifestation
- Frontend showed success messages
- Export completed successfully with status 200
- No obvious errors in client-side logs
- Users received no email notifications about completed exports

## Root Cause Analysis

### Investigation Process

#### Step 1: Environment Variable Issues
**Issue:** Missing environment variables in Vercel
- `RESEND_API_KEY` was missing
- `FROM_EMAIL` was incorrectly set to just `"onboarding"` (invalid email address)
- `ADMIN_EMAIL` was missing

**Solution:** Added proper environment variables to Vercel:
```bash
npx vercel env add RESEND_API_KEY
npx vercel env add FROM_EMAIL
npx vercel env add ADMIN_EMAIL
```

#### Step 2: Architecture Compatibility Issues
**Issue:** Background job system incompatible with Vercel serverless
- Original implementation used `BackgroundJobQueue` for async processing
- Vercel serverless functions don't support persistent background processes
- Jobs were created but never executed

**Solution:** Replaced background processing with immediate processing:
- Removed `BackgroundJobQueue` dependency
- Modified GDPR export API to process exports immediately
- Updated download API to work without job queue

#### Step 3: Email Service Configuration Issues
**Issue 1:** Invalid FROM_EMAIL format
```typescript
// WRONG - Not a valid email address
FROM_EMAIL="onboarding"

// CORRECT - Valid email address format
FROM_EMAIL="<EMAIL>"
```

**Issue 2:** Resend domain verification requirements
```
Error: "You can only send testing emails to your own email address (<EMAIL>). 
To send emails to other recipients, please verify a domain at resend.com/domains, 
and change the `from` address to an email using this domain."
```

**Issue 3:** Domain verification complexity
- Initially tried to use `<EMAIL>`
- But `wordwave.studio` domain was not verified in Resend
- Even though `updates.wordwave.studio` subdomain was already verified

## Solution Implementation

### Final Working Configuration

#### Environment Variables (Vercel)
```bash
FROM_EMAIL="<EMAIL>"
RESEND_API_KEY="re_YYRR8jbV_EbyHtdwk9sNBnJ2bE5yBcajR"
ADMIN_EMAIL="<EMAIL>"
NEXT_PUBLIC_APP_URL="https://www.wordwave.studio"
```

#### Key Code Changes

1. **GDPRDataExportService.ts** - Enhanced logging and immediate processing
2. **EmailService.ts** - Detailed error handling and Resend response parsing
3. **GDPR Export API** - Removed background job dependency
4. **Download API** - File discovery without job queue

### Critical Discovery: Subdomain Usage

**The key insight:** Resend prefers and recommends using subdomains for transactional emails.

Instead of verifying the main domain `wordwave.studio`, we used the already-verified subdomain `updates.wordwave.studio`.

## Configuration Details

### Resend Dashboard Setup
1. Domain: `updates.wordwave.studio` ✅ Verified
2. Status: Active and ready for sending
3. Region: Tokyo (ap-northeast-1)

### Email Template Configuration
- **From:** `<EMAIL>`
- **Subject:** "Your WordWave Studio Data Export is Ready"
- **Format:** HTML with text fallback
- **Content:** Professional GDPR compliance email with download link

### Error Handling Implementation
```typescript
// Enhanced Resend response parsing
if (result.error) {
  console.error('❌ Resend API returned error:', result.error);
  console.error('❌ This might be due to unverified recipient email address');
  console.error('💡 Add the recipient email to verified recipients in Resend dashboard');
  return false;
}

if (result.data && result.data.id) {
  console.log(`✅ Email sent successfully via Resend! Email ID: ${result.data.id}`);
  return true;
} else {
  console.warn('⚠️ Resend API response missing expected data:', result);
  console.warn('💡 This might indicate the email was rejected due to unverified recipient');
  return false;
}
```

## Key Learnings

### 1. Resend Email Service Restrictions
- **Free Plan Limitation:** Can only send to verified recipients or your own email address
- **Domain Requirement:** Must use verified domains for FROM address
- **Subdomain Preference:** Subdomains are preferred for transactional emails

### 2. Vercel Serverless Limitations
- **No Background Jobs:** Vercel functions are stateless and don't support persistent background processes
- **Immediate Processing Required:** All work must be completed within the function execution
- **Environment Variables:** Must be configured in Vercel dashboard, not just local .env

### 3. Email Deliverability Best Practices
- **Use Subdomains:** `<EMAIL>` vs `<EMAIL>`
- **Verify Domains:** Always verify your sending domains in your email service
- **Professional Addresses:** Use professional email addresses like `noreply@`, `notifications@`, `updates@`

### 4. Debugging Strategies
- **Comprehensive Logging:** Add detailed logs at every step
- **Environment Validation:** Always validate environment variables are present
- **Error Response Parsing:** Parse and log detailed error responses from APIs
- **Test Multiple Recipients:** Test with different email addresses to catch verification issues

## Troubleshooting Steps

### If Emails Are Not Being Sent

1. **Check Environment Variables**
   ```bash
   npx vercel env ls
   ```
   Verify: `RESEND_API_KEY`, `FROM_EMAIL`, `ADMIN_EMAIL` are present

2. **Verify FROM_EMAIL Format**
   ```typescript
   // Must be a complete email address
   FROM_EMAIL="<EMAIL>"  ✅
   FROM_EMAIL="onboarding"                        ❌
   ```

3. **Check Resend Domain Verification**
   - Go to https://resend.com/domains
   - Ensure your domain/subdomain is verified
   - Use verified domain in FROM_EMAIL

4. **Test Email Service**
   ```
   GET https://your-app.com/api/debug/test-email-send
   ```

5. **Check Vercel Function Logs**
   ```bash
   npx vercel logs your-deployment-url
   ```

6. **Validate Recipient Email**
   - On Resend free plan, recipients must be verified
   - Or use your own email address for testing

### Common Error Messages and Solutions

#### Error: "You can only send testing emails to your own email address"
**Solution:** Verify your domain in Resend dashboard

#### Error: "The [domain] is not verified"
**Solution:** Add and verify the domain, or use a subdomain that's already verified

#### Error: "Missing environment variable"
**Solution:** Add the variable to Vercel environment variables

#### Error: "Background job not processing"
**Solution:** Remove background jobs, use immediate processing for Vercel

## Best Practices

### 1. Email Service Setup
- Use subdomains for transactional emails (`updates.`, `notifications.`, `noreply.`)
- Verify domains before production deployment
- Use professional email addresses
- Implement comprehensive error handling

### 2. Vercel Deployment
- Always configure environment variables in Vercel dashboard
- Use immediate processing instead of background jobs
- Test thoroughly in production environment
- Monitor function logs for issues

### 3. GDPR Compliance
- Send confirmation emails for all data exports
- Include clear download links with expiration dates
- Provide professional, branded email templates
- Log all email attempts for audit purposes

### 4. Development Workflow
- Test email functionality with multiple recipients
- Validate environment variables in both development and production
- Use debug endpoints for troubleshooting
- Implement detailed logging for production debugging

## Production Configuration Summary

### Working Setup (August 2025)
- **Email Service:** Resend
- **From Address:** `<EMAIL>`
- **Domain Status:** Verified in Resend
- **Environment:** Vercel Production
- **Processing:** Immediate (no background jobs)
- **Status:** ✅ Fully functional

### Test Results
- ✅ Export generation works
- ✅ File storage works (Backblaze B2)
- ✅ Download links work
- ✅ Email notifications work for all users
- ✅ GDPR compliance maintained

---

*This documentation was created after resolving the email service issues in August 2025. The resolution took significant debugging effort, hence this comprehensive documentation to prevent future issues.*
