import { NextResponse } from 'next/server';
import { ApiResponse } from '@/lib/types';

/**
 * GET /api/health
 * Application health check endpoint
 */
export async function GET() {
  try {
    const healthData = {
      status: 'healthy',
      service: 'WordWave Studio API',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
    };

    const response: ApiResponse = {
      success: true,
      data: healthData,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('❌ Health check failed:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: 'Health check failed',
        code: 'HEALTH_CHECK_ERROR',
        details: error.message,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response, { status: 500 });
  }
}
