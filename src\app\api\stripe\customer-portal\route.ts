import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import Strip<PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    // Initialize Stripe inside the handler to avoid build-time issues
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const stripeCustomerId = user.unsafeMetadata?.stripeCustomerId as string;

    if (!stripeCustomerId) {
      return NextResponse.json({ 
        error: 'No Stripe customer ID found',
        message: 'Please contact support if you believe this is an error.'
      }, { status: 400 });
    }

    console.log('🔗 Creating customer portal session for:', stripeCustomerId);

    // Create a customer portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: `${request.nextUrl.origin}/`, // Return to main app
    });

    console.log('✅ Created portal session:', portalSession.id);

    return NextResponse.json({ 
      url: portalSession.url 
    });

  } catch (error) {
    console.error('Error creating customer portal session:', error);
    return NextResponse.json(
      { error: 'Failed to create customer portal session' },
      { status: 500 }
    );
  }
}
