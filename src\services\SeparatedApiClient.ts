// API Client for the new separated project/script/audio structure
import React from 'react';
import { useAuth } from '@clerk/nextjs';
import { ProjectData, ScriptData, AudioData } from '@/lib/types';

export class SeparatedApiClient {
  private baseUrl: string;
  private getToken?: () => Promise<string | null>;
  private lastListProjectsCall: number = 0;
  private listProjectsDebounceMs: number = 1000; // Prevent calls more frequent than 1 second

  constructor(baseUrl: string = '/api', getToken?: () => Promise<string | null>) {
    this.baseUrl = baseUrl;
    this.getToken = getToken;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.getToken) {
      try {
        console.log(`🔑 SeparatedApiClient: Attempting to get auth token...`);
        const token = await this.getToken();
        if (token) {
          console.log(`✅ SeparatedApiClient: Got auth token (length: ${token.length})`);
          headers['Authorization'] = `Bearer ${token}`;
        } else {
          console.warn(`⚠️ SeparatedApiClient: Auth token is null/empty`);
        }
      } catch (error) {
        console.error('❌ SeparatedApiClient: Failed to get authentication token:', error);
      }
    } else {
      console.warn(`⚠️ SeparatedApiClient: No getToken function available`);
    }

    return headers;
  }

  // Project Configuration Methods
  async saveProject(projectData: ProjectData): Promise<any> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ projectData }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to save project');
    }

    return response.json();
  }

  async loadProject(projectId: string): Promise<ProjectData> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to load project');
    }

    const result = await response.json();
    return result.data;
  }

  async listProjects(): Promise<any[]> {
    const now = Date.now();

    // Debounce rapid calls
    if (now - this.lastListProjectsCall < this.listProjectsDebounceMs) {
      console.log(`⏳ SeparatedApiClient: listProjects() debounced (${now - this.lastListProjectsCall}ms since last call)`);
      throw new Error('Too many requests - please wait before trying again');
    }

    this.lastListProjectsCall = now;
    console.log(`🔍 SeparatedApiClient: listProjects() called`);

    const headers = await this.getAuthHeaders();
    console.log(`🔑 SeparatedApiClient: Auth headers prepared, has Authorization: ${!!headers.Authorization}`);

    // If no authorization token, don't make the request
    if (!headers.Authorization) {
      console.warn(`⚠️ SeparatedApiClient: No authorization token available, skipping API call`);
      throw new Error('Authentication required - please sign in');
    }

    const response = await fetch(`${this.baseUrl}/projects`, {
      headers,
    });

    console.log(`📡 SeparatedApiClient: Response status: ${response.status}`);

    if (!response.ok) {
      const error = await response.json();
      console.error(`❌ SeparatedApiClient: API error:`, error);
      throw new Error(error.error?.message || 'Failed to list projects');
    }

    const result = await response.json();
    console.log(`✅ SeparatedApiClient: Successfully loaded ${result.data?.length || 0} projects`);
    return result.data;
  }

  async updateProject(projectId: string, configuration: any): Promise<ProjectData> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({ configuration }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update project');
    }

    const result = await response.json();
    return result.data;
  }

  async deleteProject(projectId: string): Promise<void> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete project');
    }
  }

  // Script Methods
  async saveScript(projectId: string, scriptData: ScriptData): Promise<any> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/scripts`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ scriptData }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to save script');
    }

    return response.json();
  }

  async loadScript(projectId: string, scriptId: string): Promise<ScriptData> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/scripts/${scriptId}`, {
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to load script');
    }

    const result = await response.json();
    return result.data;
  }

  async listScripts(projectId: string): Promise<any[]> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/scripts`, {
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to list scripts');
    }

    const result = await response.json();
    return result.data;
  }

  async updateScript(projectId: string, scriptId: string, scriptData: Partial<ScriptData>): Promise<ScriptData> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/scripts/${scriptId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({ scriptData }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update script');
    }

    const result = await response.json();
    return result.data;
  }

  async deleteScript(projectId: string, scriptId: string): Promise<void> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/scripts/${scriptId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete script');
    }
  }

  // Audio Methods
  async saveAudio(projectId: string, audioData: AudioData): Promise<any> {
    // Validate that we have actual audio files before proceeding
    if (!audioData.files || audioData.files.length === 0) {
      console.warn(`⚠️ Skipping audio save for ${audioData.id} - no audio files provided`);
      throw new Error('Cannot save audio: No audio files provided');
    }

    // Check if any files have actual audio data (blob URL or base64)
    const hasAudioData = audioData.files.some(file =>
      (file.url && file.url.startsWith('blob:')) || file.base64Data
    );

    if (!hasAudioData) {
      console.warn(`⚠️ Skipping audio save for ${audioData.id} - no valid audio data found`);
      throw new Error('Cannot save audio: No valid audio data found in files');
    }

    console.log(`🎵 Proceeding with audio save for ${audioData.id} - ${audioData.files.length} files with valid data`);

    // Convert blob URLs to base64 data before sending to server
    const processedAudioData = { ...audioData };

    for (let i = 0; i < processedAudioData.files.length; i++) {
      const audioFile = processedAudioData.files[i];
      if (audioFile.url && audioFile.url.startsWith('blob:')) {
        try {
          // Check if blob URL is still valid by attempting to fetch
          const response = await fetch(audioFile.url);
          
          if (!response.ok) {
            throw new Error(`Blob fetch failed with status: ${response.status}`);
          }
          
          const blob = await response.blob();

          // Convert Blob to base64 using FileReader (more efficient for large files)
          const base64Data = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
              const result = reader.result as string;
              // Remove the data URL prefix (e.g., "data:audio/wav;base64,")
              const base64 = result.split(',')[1];
              resolve(base64);
            };
            reader.onerror = () => reject(reader.error);
            reader.readAsDataURL(blob);
          });

          // Replace the blob URL with base64 data
          processedAudioData.files[i] = {
            ...audioFile,
            url: undefined, // Remove blob URL
            base64Data, // Add base64 data
            size: blob.size
          };

          console.log(`✅ Converted blob URL to base64 for ${audioFile.name} (${blob.size} bytes)`);
        } catch (error) {
          console.error(`❌ Failed to convert blob URL for ${audioFile.name}:`, error);
          
          // Check if this is a blob URL that's been revoked
          if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            console.log(`⚠️ Blob URL for ${audioFile.name} has been revoked, skipping conversion`);
            // Skip this file as it's likely already been saved or the blob URL is no longer valid
            processedAudioData.files.splice(i, 1);
            i--; // Adjust index since we removed an item
            continue;
          }
          
          throw new Error(`Failed to process audio file: ${audioFile.name}`);
        }
      }
    }

    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/audio`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ audioData: processedAudioData }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to save audio');
    }

    return response.json();
  }

  async listAudio(projectId: string, scriptId?: string): Promise<any[]> {
    console.log(`🔍 SeparatedApiClient: listAudio() called for project ${projectId}${scriptId ? ` with scriptId ${scriptId}` : ''}`);
    const headers = await this.getAuthHeaders();
    const url = scriptId
      ? `${this.baseUrl}/projects/${projectId}/audio?scriptId=${scriptId}`
      : `${this.baseUrl}/projects/${projectId}/audio`;

    console.log(`📡 SeparatedApiClient: Making request to ${url}`);
    const response = await fetch(url, {
      headers,
    });

    console.log(`📡 SeparatedApiClient: Response status: ${response.status}`);
    if (!response.ok) {
      const error = await response.json();
      console.error(`❌ SeparatedApiClient: listAudio failed:`, error);
      throw new Error(error.error?.message || 'Failed to list audio');
    }

    const result = await response.json();
    console.log(`✅ SeparatedApiClient: Successfully loaded ${result.data?.length || 0} audio files`);
    return result.data;
  }

  async loadAudio(projectId: string, audioId: string): Promise<AudioData> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/audio/${audioId}`, {
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to load audio');
    }

    const result = await response.json();
    return result.data;
  }

  async deleteAudio(projectId: string, audioId: string): Promise<void> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/projects/${projectId}/audio/${audioId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete audio');
    }
  }
}

/**
 * React hook to get an authenticated SeparatedApiClient
 */
export const useSeparatedApiClient = (): SeparatedApiClient => {
  const { getToken } = useAuth();

  // Use useMemo to prevent creating new instance on every render
  return React.useMemo(() => {
    return new SeparatedApiClient(
      '/api', // Use relative paths for Next.js API routes
      getToken
    );
  }, [getToken]);
};

// Create a singleton instance (deprecated - use useSeparatedApiClient hook instead)
// This is kept for backward compatibility but should be replaced with the hook
export const separatedApiClient = new SeparatedApiClient();
