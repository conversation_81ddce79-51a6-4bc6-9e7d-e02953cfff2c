import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  console.log('🧪 Starting simplified checkout test...');
  
  try {
    // Test 1: Environment variables
    console.log('✅ Test 1: Checking environment variables...');
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY missing');
    }
    
    // Test 2: Stripe initialization
    console.log('✅ Test 2: Initializing Stripe...');
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
    
    // Test 3: Parse request
    console.log('✅ Test 3: Parsing request...');
    const { priceId } = await request.json();
    
    // Test 4: User authentication
    console.log('✅ Test 4: Getting user...');
    const user = await currentUser();
    if (!user) {
      throw new Error('No authenticated user');
    }
    
    // Test 5: Simple customer creation (without metadata storage)
    console.log('✅ Test 5: Creating simple customer...');
    const customer = await stripe.customers.create({
      email: user.emailAddresses[0]?.emailAddress || '<EMAIL>',
      name: user.fullName || 'Test User',
      metadata: {
        clerkUserId: user.id,
        testCustomer: 'true'
      },
    });
    
    // Test 6: Simple checkout session
    console.log('✅ Test 6: Creating simple checkout session...');
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: priceId === process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID ? 'subscription' : 'payment',
      success_url: `${request.nextUrl.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.nextUrl.origin}/pricing`,
      allow_promotion_codes: true, // Enable promotion codes in checkout
      metadata: {
        clerkUserId: user.id,
        testSession: 'true'
      },
    });
    
    // Test 7: Cleanup test customer
    console.log('✅ Test 7: Cleaning up test customer...');
    await stripe.customers.del(customer.id);
    
    console.log('🎉 All tests passed!');
    
    return NextResponse.json({
      status: 'success',
      message: 'All checkout components working correctly',
      sessionId: session.id,
      tests: {
        environmentVariables: '✅',
        stripeInit: '✅',
        requestParsing: '✅',
        userAuth: '✅',
        customerCreation: '✅',
        sessionCreation: '✅',
        cleanup: '✅'
      },
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Checkout test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
