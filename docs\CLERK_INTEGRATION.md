# Clerk Integration Guide

## Overview
This document outlines the Clerk integration in WordWave Studio, which provides robust user authentication and management.

## Table of Contents
1. [Clerk Account Setup](#clerk-account-setup)
2. [Environment Configuration](#environment-configuration)
3. [Authentication Flow](#authentication-flow)
4. [Protecting Routes](#protecting-routes)
5. [User Data Management](#user-data-management)
6. [UI Integration](#ui-integration)
7. [Key Files](#key-files)

---

## 1. Clerk Account Setup

To use Clerk, you'll need to create an account at [https://clerk.com/](https://clerk.com/) and create a new application. From your Clerk dashboard, you'll need to get your "Publishable Key" and "Secret Key".

---

## 2. Environment Configuration

The following environment variables need to be set in your `.env.local` file:

```bash
# Authentication (Clerk)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_key_here
```

---

## 3. Authentication Flow

The authentication flow is handled by the `<ClerkProvider>` component, which wraps the entire application in `src/app/layout.tsx`. This provides the authentication context to all pages and components.

```tsx
// src/app/layout.tsx
import { ClerkProvider } from '@clerk/nextjs';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>{children}</body>
      </html>
    </ClerkProvider>
  );
}
```

---

## 4. Protecting Routes

Routes are protected using the `clerkMiddleware` in `src/middleware.ts`. This middleware checks for an active session on every request and redirects unauthenticated users to the sign-in page.

```typescript
// src/middleware.ts
import { clerkMiddleware } from '@clerk/nextjs/server';

export default clerkMiddleware();

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

API routes are protected using the `auth()` and `currentUser()` functions from `@clerk/nextjs/server`.

---

## 5. User Data Management

User data is managed through the `useUser` hook on the client-side and the `currentUser()` function on the server-side. The `clerkClient` is used for server-side interactions with the Clerk API, such as updating user metadata. For example, the Stripe customer ID is stored in the user's Clerk metadata.

---

## 6. UI Integration

Clerk provides a set of pre-built UI components that are used throughout the application:

*   **`<UserButton>`**: A dropdown menu for signed-in users to manage their account.
*   **`<SignInButton>`**: A button that opens the sign-in modal.
*   **`<SignUpButton>`**: A button that opens the sign-up modal.

These components are used in `src/components/AuthSection.tsx` and `src/components/AccountMenu.tsx`.

---

## 7. Key Files

| File | Purpose |
|------|---------|
| `src/app/layout.tsx` | Wraps the application with `<ClerkProvider>`. |
| `src/middleware.ts` | Protects routes using `clerkMiddleware`. |
| `src/app/api/.../route.ts` | Uses `auth()` and `currentUser()` to protect API routes. |
| `src/components/AccountMenu.tsx` | Uses `<UserButton>` and `useUser` to display user information. |
| `src/components/AuthSection.tsx` | Uses `<SignInButton>` and `<SignUpButton>` for authentication UI. |
