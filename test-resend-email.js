// Test Resend Email Service
// Run this in the browser console or as a test to verify Resend integration

import { Resend } from 'resend';

export async function testResendEmail() {
  try {
    console.log('Testing Resend email service...');
    
    const resend = new Resend(process.env.RESEND_API_KEY);
    
    const result = await resend.emails.send({
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Test Email from WordWave GDPR System',
      html: '<h1>Test Email</h1><p>This is a test email to verify Resend integration.</p>',
    });

    console.log('✅ Test email sent successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Test email failed:', error);
    throw error;
  }
}
