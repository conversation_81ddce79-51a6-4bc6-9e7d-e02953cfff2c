// Test Sender Email Service
// This script tests the Sender email integration specifically

const axios = require('axios');

async function testSenderEmail() {
  try {
    console.log('🧪 Testing Sender email service...\n');
    
    if (!process.env.SENDER_API_KEY) {
      console.error('❌ SENDER_API_KEY not found in environment variables');
      console.log('\n🚀 To set up Sender:');
      console.log('1. Sign up at https://www.sender.net/');
      console.log('2. Go to Settings → API & Webhooks');
      console.log('3. Create an API key');
      console.log('4. Add to .env.local: SENDER_API_KEY=sender_your_key_here');
      return false;
    }
    
    console.log('✅ Sender API key found');
    console.log('🔧 Preparing test email...');
    
    const senderData = {
      from: {
        email: process.env.FROM_EMAIL || '<EMAIL>',
        name: 'WordWave Studio'
      },
      to: [
        {
          email: '<EMAIL>'
        }
      ],
      subject: 'Test Email from WordWave - Sender Integration',
      html: `
        <h1>🧪 Sender Email Test</h1>
        <p>This is a test email to verify Sender integration with WordWave Studio.</p>
        
        <h2>🎉 Why Sender is Great:</h2>
        <ul>
          <li><strong>15,000 free emails per month</strong> (vs 3,000 with Resend)</li>
          <li><strong>$8/month for 60,000 emails</strong> (vs $20/month for 50,000 with Resend)</li>
          <li>99% deliverability rate</li>
          <li>Advanced analytics and tracking</li>
          <li>GDPR compliant</li>
          <li>Drag & drop email builder</li>
          <li>A/B testing capabilities</li>
        </ul>
        
        <h2>📊 Configuration Details:</h2>
        <ul>
          <li>API Key: ✅ Configured</li>
          <li>From Email: ${process.env.FROM_EMAIL || '<EMAIL>'}</li>
          <li>Test sent at: ${new Date().toISOString()}</li>
        </ul>
        
        <p>If you received this email, Sender integration is working perfectly! 🎉</p>
      `,
      text: `
Sender Email Test

This is a test email to verify Sender integration with WordWave Studio.

Why Sender is Great:
- 15,000 free emails per month (vs 3,000 with Resend)
- $8/month for 60,000 emails (vs $20/month for 50,000 with Resend)
- 99% deliverability rate
- Advanced analytics and tracking
- GDPR compliant

Configuration Details:
- API Key: Configured
- From Email: ${process.env.FROM_EMAIL || '<EMAIL>'}
- Test sent at: ${new Date().toISOString()}

If you received this email, Sender integration is working perfectly!
      `
    };
    
    console.log('📤 Sending test email via Sender API...');
    console.log('To:', senderData.to[0].email);
    console.log('From:', senderData.from.email);
    console.log('Subject:', senderData.subject);
    
    const response = await axios.post(
      'https://api.sender.net/v2/email',
      senderData,
      {
        headers: {
          'Authorization': `Bearer ${process.env.SENDER_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    console.log('\n✅ Test email sent successfully via Sender!');
    console.log('📋 Response details:', response.data);
    console.log('📱 Check your <NAME_EMAIL>');
    console.log('\n🎉 Sender integration is working perfectly!');
    
    return response.data;
  } catch (error) {
    console.error('\n❌ Test email failed:', error.message);
    if (error.response) {
      console.error('❌ Sender API error response:');
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      
      // Provide helpful error messages
      if (error.response.status === 401) {
        console.log('\n💡 This looks like an authentication issue:');
        console.log('- Check that your SENDER_API_KEY is correct');
        console.log('- Make sure the API key hasn\'t expired');
        console.log('- Verify the key has email sending permissions');
      } else if (error.response.status === 422) {
        console.log('\n💡 This looks like a validation issue:');
        console.log('- Check that your FROM_EMAIL is properly formatted');
        console.log('- Make sure the recipient email is valid');
        console.log('- Verify your domain is configured in Sender');
      }
    }
    return false;
  }
}

// Environment check
console.log('🔍 Sender Configuration Check:');
console.log('SENDER_API_KEY:', process.env.SENDER_API_KEY ? '✅ Present' : '❌ Missing');
console.log('FROM_EMAIL:', process.env.FROM_EMAIL || '⚠️ Not set (will use default)');
console.log('');

// Run the test
testSenderEmail()
  .then((result) => {
    if (result) {
      console.log('\n🎉 Sender email test completed successfully!');
      console.log('📈 You now have access to 15,000 free emails per month!');
    } else {
      console.log('\n⚠️ Sender email test completed with issues');
      console.log('💡 Check the error messages above for troubleshooting steps');
    }
  })
  .catch((error) => {
    console.error('\n💥 Sender email test failed:', error);
  });
