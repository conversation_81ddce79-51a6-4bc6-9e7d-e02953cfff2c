'use client';

import React, { useState, useEffect } from 'react';
import { Activity, Clock, CheckCircle, AlertCircle, RefreshCw, Users, Download } from 'lucide-react';

interface JobStats {
  total: number;
  queued: number;
  processing: number;
  completed: number;
  failed: number;
}

interface Job {
  id: string;
  type: 'data-export' | 'account-deletion';
  status: 'queued' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  retryCount?: number;
}

const GDPRJobMonitor: React.FC = () => {
  const [stats, setStats] = useState<JobStats | null>(null);
  const [recentJobs, setRecentJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch job statistics and recent jobs
  const fetchJobData = async () => {
    try {
      const response = await fetch('/api/gdpr/admin/stats');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.stats);
          setRecentJobs(data.recentJobs || []);
          setLastUpdated(new Date());
        }
      }
    } catch (error) {
      console.error('Error fetching job data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh data
  useEffect(() => {
    fetchJobData();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchJobData, 5000); // Refresh every 5 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getStatusIcon = (status: Job['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <Activity className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'queued':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-500 border-t-transparent rounded-full animate-spin" />;
    }
  };

  const getStatusColor = (status: Job['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-900/50 text-green-300 border-green-500';
      case 'failed':
        return 'bg-red-900/50 text-red-300 border-red-500';
      case 'processing':
        return 'bg-blue-900/50 text-blue-300 border-blue-500';
      case 'queued':
        return 'bg-yellow-900/50 text-yellow-300 border-yellow-500';
      default:
        return 'bg-gray-900/50 text-gray-300 border-gray-500';
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  const calculateDuration = (startTime?: string, endTime?: string) => {
    if (!startTime || !endTime) return null;
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    
    if (diffSecs < 60) return `${diffSecs}s`;
    if (diffSecs < 3600) return `${Math.floor(diffSecs / 60)}m ${diffSecs % 60}s`;
    return `${Math.floor(diffSecs / 3600)}h ${Math.floor((diffSecs % 3600) / 60)}m`;
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Loading job monitoring data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-white">Job Monitoring</h2>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-gray-300">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Auto-refresh</span>
          </label>
          <button
            onClick={fetchJobData}
            disabled={isLoading}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Last updated timestamp */}
      {lastUpdated && (
        <p className="text-sm text-gray-400">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </p>
      )}

      {/* Job Statistics */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center">
            <div className="text-2xl font-bold text-white">{stats.total}</div>
            <div className="text-sm text-gray-300">Total Jobs</div>
          </div>
          
          <div className="bg-yellow-900/20 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/30 text-center">
            <div className="text-2xl font-bold text-yellow-300">{stats.queued}</div>
            <div className="text-sm text-gray-300">Queued</div>
          </div>
          
          <div className="bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-500/30 text-center">
            <div className="text-2xl font-bold text-blue-300">{stats.processing}</div>
            <div className="text-sm text-gray-300">Processing</div>
          </div>
          
          <div className="bg-green-900/20 backdrop-blur-sm rounded-lg p-4 border border-green-500/30 text-center">
            <div className="text-2xl font-bold text-green-300">{stats.completed}</div>
            <div className="text-sm text-gray-300">Completed</div>
          </div>
          
          <div className="bg-red-900/20 backdrop-blur-sm rounded-lg p-4 border border-red-500/30 text-center">
            <div className="text-2xl font-bold text-red-300">{stats.failed}</div>
            <div className="text-sm text-gray-300">Failed</div>
          </div>
        </div>
      )}

      {/* Recent Jobs */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Recent Jobs</h3>
        
        {recentJobs.length === 0 ? (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-400">No recent jobs found</p>
          </div>
        ) : (
          <div className="space-y-3">
            {recentJobs.map((job) => (
              <div key={job.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-white">
                          {job.type === 'data-export' ? (
                            <><Download className="w-4 h-4 inline mr-1" />Data Export</>
                          ) : (
                            'Account Deletion'
                          )}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(job.status)}`}>
                          {job.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-400">
                        Created: {formatRelativeTime(job.createdAt)}
                        {job.completedAt && (
                          <> • Duration: {calculateDuration(job.startedAt, job.completedAt)}</>
                        )}
                        {job.retryCount && job.retryCount > 0 && (
                          <> • Retries: {job.retryCount}</>
                        )}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm text-gray-400 font-mono">{job.id}</p>
                    {job.error && (
                      <p className="text-xs text-red-400 mt-1 max-w-xs truncate" title={job.error}>
                        Error: {job.error}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* System Health */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-white mb-4">System Health</h3>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-white mb-2">Queue Processing</h4>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Operational</span>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-2">Email Service</h4>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Not Configured</span>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-2">Storage Service</h4>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Connected</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GDPRJobMonitor;
