"use client";

import React from 'react';
import LandingPage from '@/components/LandingPage';
import Waves from '@/components/Waves';
import Noise from '@/components/Noise';

export default function Landing() {
  return (
    <div className="relative min-h-screen bg-slate-900 overflow-hidden">
      {/* Noise Background */}
      <Noise
        patternSize={250}
        patternScaleX={1}
        patternScaleY={1}
        patternRefreshInterval={2}
        patternAlpha={20}
      />

      {/* Waves Background */}
      <Waves
        lineColor="#1e293b"
        backgroundColor="transparent"
        waveSpeedX={0.02}
        waveSpeedY={0.01}
        waveAmpX={40}
        waveAmpY={20}
        friction={0.9}
        tension={0.01}
        maxCursorMove={120}
        xGap={12}
        yGap={36}
      />

      {/* Landing Page Content */}
      <div className="relative z-10">
        <LandingPage />
      </div>
    </div>
  );
}
