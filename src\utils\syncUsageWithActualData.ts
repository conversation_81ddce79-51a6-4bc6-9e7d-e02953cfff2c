/**
 * Utility to sync usage tracking with actual stored data
 * This fixes discrepancies between Clerk metadata and actual B2 storage
 */

import { UsageStats } from '@/hooks/useSubscription';

export interface ActualUsageData {
  totalScripts: number;
  totalAudio: number;
  totalTokensUsed: number;
}

/**
 * Calculate actual usage from stored data
 */
export async function calculateActualUsage(userId: string): Promise<ActualUsageData> {
  try {
    // Get all projects for the user
    const projectsResponse = await fetch('/api/projects');
    if (!projectsResponse.ok) {
      throw new Error('Failed to fetch projects');
    }
    
    const projectsResult = await projectsResponse.json();
    const projects = projectsResult.data || [];
    
    console.log(`📊 Found ${projects.length} projects for usage calculation`);
    
    let totalScripts = 0;
    let totalAudio = 0;
    
    // Count scripts and audio across all projects
    for (const project of projects) {
      const projectId = project.projectId || project.id;
      console.log(`🔍 Checking project: ${projectId}`);
      
      // Count scripts in this project
      try {
        const scriptsResponse = await fetch(`/api/projects/${projectId}/scripts`);
        if (scriptsResponse.ok) {
          const scriptsResult = await scriptsResponse.json();
          const scriptCount = scriptsResult.data?.length || 0;
          totalScripts += scriptCount;
          console.log(`📝 Project ${projectId}: ${scriptCount} scripts`);
        }
      } catch (error) {
        console.warn(`Failed to count scripts for project ${projectId}:`, error);
      }
      
      // Count audio in this project
      try {
        const audioResponse = await fetch(`/api/projects/${projectId}/audio`);
        if (audioResponse.ok) {
          const audioResult = await audioResponse.json();
          const audioCount = audioResult.data?.length || 0;
          totalAudio += audioCount;
          console.log(`🎵 Project ${projectId}: ${audioCount} audio files`);
        }
      } catch (error) {
        console.warn(`Failed to count audio for project ${projectId}:`, error);
      }
    }
    
    console.log(`📊 Total count: ${totalScripts} scripts, ${totalAudio} audio files`);
    
    // For tokens, we'll keep the current value since we can't retroactively calculate it
    // In the future, this could be stored per generation
    const totalTokensUsed = 0; // Will be preserved from current metadata
    
    return {
      totalScripts,
      totalAudio,
      totalTokensUsed
    };
  } catch (error) {
    console.error('Failed to calculate actual usage:', error);
    throw error;
  }
}

/**
 * Sync Clerk metadata with actual stored data
 */
export async function syncUsageWithActualData(
  user: any,
  setUsage: (usage: UsageStats) => void
): Promise<void> {
  if (!user) {
    console.error('No user provided for usage sync');
    return;
  }
  
  try {
    console.log('🔄 Syncing usage with actual stored data...');
    
    // Get current metadata - preserve generation counts as they represent actual user activity
    const currentUsage = user.unsafeMetadata?.usage as UsageStats || {
      scripts: 0,
      audioGeneration: 0,
      tokens: 0
    };
    
    console.log('📊 Current usage (preserving generation counts):', currentUsage);
    
    // For now, we preserve the existing usage counts since they represent actual generation activity
    // In the future, we could add separate fields for "generated" vs "saved" if needed
    const syncedUsage: UsageStats = {
      scripts: currentUsage.scripts, // Preserve - represents actual script generations
      audioGeneration: currentUsage.audioGeneration, // Preserve - represents actual audio generations  
      tokens: currentUsage.tokens // Preserve - represents actual token consumption
    };
    
    console.log('📊 Usage sync completed - preserved existing counts:', syncedUsage);
    
    // Update timestamp to indicate sync was performed
    await user.update({
      unsafeMetadata: {
        ...user.unsafeMetadata,
        usage: syncedUsage,
        lastUsageSync: new Date().toISOString()
      }
    });
    
    // Update local state
    setUsage(syncedUsage);
    
    console.log('✅ Usage sync completed - counts preserved (generation-based tracking)');
    
  } catch (error) {
    console.error('❌ Failed to sync usage with actual data:', error);
    throw error;
  }
}

/**
 * Check if usage sync is needed (e.g., if it's been more than a day since last sync)
 */
export function shouldSyncUsage(user: any): boolean {
  const lastSync = user.unsafeMetadata?.lastUsageSync;
  if (!lastSync) return true;
  
  const lastSyncDate = new Date(lastSync);
  const daysSinceSync = (Date.now() - lastSyncDate.getTime()) / (1000 * 60 * 60 * 24);
  
  return daysSinceSync > 1; // Sync if it's been more than a day
}
