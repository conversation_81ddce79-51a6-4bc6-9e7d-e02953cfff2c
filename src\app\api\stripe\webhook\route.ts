import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook received at:', new Date().toISOString());
  
  try {
    // Initialize Stripe inside the handler to avoid build-time issues
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-07-30.basil',
    });
    
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    console.log('📊 Request details:');
    console.log('- Body length:', body.length);
    console.log('- Has signature:', !!signature);
    console.log('- Has webhook secret:', !!webhookSecret);

    if (!signature) {
      console.error('❌ No stripe signature found');
      return NextResponse.json({ error: 'No signature' }, { status: 400 });
    }

    console.log('🔐 Verifying webhook signature...');
    
    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    
    console.log('✅ Webhook signature verified. Event type:', event.type);
    console.log('📋 Event ID:', event.id);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const clerkUserId = session.metadata?.clerkUserId;
        const priceId = session.metadata?.priceId;

        console.log('🎯 Processing checkout.session.completed for user:', clerkUserId);
        console.log('💰 Price ID:', priceId);

        if (clerkUserId && priceId) {
          // Determine plan type based on price ID
          const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID;
          const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID;
          
          console.log('🔧 Environment price IDs:');
          console.log('- Monthly:', monthlyPriceId);
          console.log('- Lifetime:', lifetimePriceId);
          console.log('- Received:', priceId);
          
          let planType: 'monthly' | 'lifetime';
          if (priceId === monthlyPriceId) {
            planType = 'monthly';
          } else if (priceId === lifetimePriceId) {
            planType = 'lifetime';
          } else {
            console.error('❌ Unknown price ID:', priceId);
            planType = 'monthly'; // fallback
          }

          console.log('📋 Plan type determined:', planType);

          try {
            const clerk = await clerkClient();
            const updateResult = await clerk.users.updateUserMetadata(clerkUserId, {
              unsafeMetadata: {
                stripeCustomerId: session.customer as string,
                subscriptionStatus: 'active',
                planType: planType,
                lastPaymentDate: new Date().toISOString(),
                webhookProcessed: event.id,
              },
            });

            console.log('✅ Updated user metadata for checkout completion');
            console.log('📋 Update result:', updateResult ? 'Success' : 'Failed');
          } catch (clerkError) {
            console.error('❌ Failed to update Clerk metadata:', clerkError);
            throw clerkError;
          }
        } else {
          console.error('❌ Missing clerkUserId or priceId in session metadata');
          console.error('- clerkUserId:', clerkUserId);
          console.error('- priceId:', priceId);
          console.error('- session.metadata:', session.metadata);
        }
        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        const customer = await stripe.customers.retrieve(subscription.customer as string);

        if (customer && !customer.deleted) {
          const clerkUserId = customer.metadata?.clerkUserId;
          if (clerkUserId) {
            console.log('📝 Updating subscription status for user:', clerkUserId);
            console.log('🔄 Subscription status:', subscription.status);
            console.log('🎯 Trial end:', subscription.trial_end);
            
            const clerk = await clerkClient();
            await clerk.users.updateUserMetadata(clerkUserId, {
              unsafeMetadata: {
                subscriptionId: subscription.id,
                subscriptionStatus: subscription.status, // Can be 'trialing', 'active', etc.
                currentPeriodEnd: (subscription as any).current_period_end ? new Date((subscription as any).current_period_end * 1000).toISOString() : null,
                trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
                planType: 'monthly',
              },
            });
            
            console.log('✅ Updated subscription metadata with trial info');
          }
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        const customer = await stripe.customers.retrieve(subscription.customer as string);

        if (customer && !customer.deleted) {
          const clerkUserId = customer.metadata?.clerkUserId;
          if (clerkUserId) {
            const clerk = await clerkClient();
            await clerk.users.updateUserMetadata(clerkUserId, {
              unsafeMetadata: {
                subscriptionStatus: 'cancelled',
                subscriptionId: null,
                currentPeriodEnd: null,
              },
            });
          }
        }
        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        const customer = await stripe.customers.retrieve(invoice.customer as string);

        if (customer && !customer.deleted) {
          const clerkUserId = customer.metadata?.clerkUserId;
          if (clerkUserId) {
            const clerk = await clerkClient();
            await clerk.users.updateUserMetadata(clerkUserId, {
              unsafeMetadata: {
                lastPaymentDate: new Date().toISOString(),
                subscriptionStatus: 'active',
              },
            });
          }
        }
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        const customer = await stripe.customers.retrieve(invoice.customer as string);

        if (customer && !customer.deleted) {
          const clerkUserId = customer.metadata?.clerkUserId;
          if (clerkUserId) {
            const clerk = await clerkClient();
            await clerk.users.updateUserMetadata(clerkUserId, {
              unsafeMetadata: {
                subscriptionStatus: 'past_due',
                lastFailedPayment: new Date().toISOString(),
              },
            });
          }
        }
        break;
      }

      default:
        console.log(`⚠️ Unhandled event type: ${event.type}`);
    }

    console.log('✅ Webhook processing completed successfully');
    return NextResponse.json({ received: true, eventType: event.type, eventId: event.id });
  } catch (error) {
    console.error('❌ Webhook error:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({ error: 'Webhook failed', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 400 });
  }
}
