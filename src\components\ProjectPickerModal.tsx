"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';
import { SynthesisMode } from "@/lib/types";
import { 
  RANDOM_PODCAST_NAMES, 
  RANDOM_PERSONA_NAMES, 
  RANDOM_PODCAST_STYLES,
  AVAILABLE_VOICES,
  preferredDefaultVoiceApiNames,
  getRandomItem 
} from "@/lib/constants";
import { useSeparatedApiClient } from '@/services/SeparatedApiClient';
import Modal from './Modal';

interface ProjectSetupData {
  projectName: string;
  synthesisMode: SynthesisMode;
  speaker1Name: string;
  speaker2Name: string;
  projectStyle: string;
  voice1: string;
  voice2: string;
}

interface ProjectPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartNew: (setupData: ProjectSetupData) => void;
  onLoadExisting: (projectData: any) => void;
  onResumeLast?: () => void;
  lastProjectName?: string | null;
}

const ProjectPickerModal: React.FC<ProjectPickerModalProps> = ({
  isOpen,
  onClose,
  onStartNew,
  onLoadExisting,
  onResumeLast,
  lastProjectName,
}) => {
  const [view, setView] = useState<'picker' | 'loadProject' | 'newProject'>('picker');
  const [projects, setProjects] = useState<any[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState({
    isOpen: false,
    projectId: '',
    projectName: ''
  });
  const [setupData, setSetupData] = useState<ProjectSetupData>({
    projectName: getRandomItem(RANDOM_PODCAST_NAMES),
    synthesisMode: 'podcast',
    speaker1Name: getRandomItem(RANDOM_PERSONA_NAMES),
    speaker2Name: getRandomItem(RANDOM_PERSONA_NAMES),
    projectStyle: getRandomItem(RANDOM_PODCAST_STYLES),
    voice1: AVAILABLE_VOICES[0]?.apiName || 'zephyr',
    voice2: AVAILABLE_VOICES[1]?.apiName || 'puck',
  });

  const { user, isSignedIn } = useUser();
  const separatedApiClient = useSeparatedApiClient();

  console.log('🎤 ProjectPickerModal AVAILABLE_VOICES sample:', AVAILABLE_VOICES.slice(0, 5).map(v => ({
    displayName: v.displayName,
    apiName: v.apiName
  })));

  // Load projects when needed
  const loadProjects = useCallback(async () => {
    if (!separatedApiClient || !isSignedIn || !user) return;
    
    setIsLoadingProjects(true);
    try {
      const allProjects = await separatedApiClient.listProjects();
      setProjects(allProjects || []);
    } catch (err) {
      console.error('❌ Failed to load projects:', err);
      setProjects([]);
    } finally {
      setIsLoadingProjects(false);
    }
  }, [separatedApiClient, isSignedIn, user]);

  useEffect(() => {
    if (view === 'loadProject') {
      loadProjects();
    }
  }, [view, loadProjects]);

  const handleLoadProject = async (projectId: string) => {
    if (!separatedApiClient) return;
    
    try {
      console.log('🔄 Loading project:', projectId);
      const projectData = await separatedApiClient.loadProject(projectId);
      console.log('📦 Loaded project data:', projectData);
      
      // Also load audio files for this project
      let allAudioFiles: any[] = [];
      try {
        console.log(`🎵 Loading audio files for project ${projectId}...`);
        const audioList = await separatedApiClient.listAudio(projectId);
        console.log(`📁 Found ${audioList.length} audio files for project ${projectId}`);
        console.log(`📋 Raw audio list:`, audioList);

        // Convert audio metadata to the format expected by the UI
        allAudioFiles = audioList.map((audio: any) => {
          // Calculate total size from individual files if available
          const totalSize = audio.files?.reduce((sum: number, file: any) => sum + (file.size || 0), 0) || audio.totalSize || 0;

          // Only use duration if it's a valid, finite number
          const validDuration = audio.duration && isFinite(audio.duration) && audio.duration > 0 ? audio.duration : undefined;

          const audioFile = {
            id: audio.id,
            name: audio.name,
            url: `/api/projects/${projectId}/audio/${audio.id}/download?t=${Date.now()}`, // Add timestamp to prevent caching issues
            processedMimeType: 'audio/wav',
            size: totalSize, // Use calculated size from files or metadata
            duration: validDuration, // Only set duration if it's valid
            createdAt: audio.createdAt,
            voiceConfig: audio.voiceConfig,
            scriptContent: audio.scriptContent,
            scriptTopic: audio.scriptTopic,
            generationId: audio.id,
            ttsModel: audio.ttsModel,
            synthesisMode: audio.synthesisMode,
          };
          console.log(`🎵 Processed audio file: ${audioFile.name} (${audioFile.size} bytes)`);
          return audioFile;
        });
        
        console.log(`✅ Successfully processed ${allAudioFiles.length} audio files for project ${projectId}`);
      } catch (audioError) {
        console.error('❌ Failed to load audio files for project:', audioError);
        console.warn('📝 Project will load without audio files. This may be normal if no audio has been generated yet.');
        // Continue without audio files if loading fails
      }

      // Add audio files to the project data
      const projectDataWithAudio = {
        ...projectData,
        audioFiles: allAudioFiles
      };

      onClose(); // Close picker first
      onLoadExisting(projectDataWithAudio); // Pass the loaded project data with audio files
    } catch (err) {
      console.error('❌ Failed to load project:', err);
    }
  };

  const handleDeleteProject = async (projectId: string, projectName: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    // Open the confirmation modal instead of using browser confirm
    setDeleteConfirmModal({
      isOpen: true,
      projectId,
      projectName
    });
  };

  const confirmDeleteProject = () => {
    if (!separatedApiClient) return;
    
    const { projectId, projectName } = deleteConfirmModal;

    // Start the deletion process asynchronously
    (async () => {
      try {
        await separatedApiClient.deleteProject(projectId);
        // Add a small delay to ensure backend has fully processed the deletion
        await new Promise(resolve => setTimeout(resolve, 1000));
        await loadProjects(); // Refresh the list
      } catch (err) {
        console.error('❌ Failed to delete project:', err);
      }
    })();
    
    // Close the modal immediately
    setDeleteConfirmModal({
      isOpen: false,
      projectId: '',
      projectName: ''
    });
  };

  if (!isOpen) return null;

  const handleLoadExisting = () => {
    setView('loadProject');
  };

  const handleResumeLast = () => {
    onClose(); // Close picker first
    onResumeLast?.(); // Then resume last project
  };

  const handleStartNew = () => {
    setView('newProject');
  };

  const handleCreateProject = () => {
    console.log('🎤 ProjectPickerModal handleCreateProject called with setupData:', setupData);
    onClose(); // Close picker first
    onStartNew(setupData); // Then start new project with setup data
  };

  const handleBackToPicker = () => {
    setView('picker');
  };

  const randomizeSetup = () => {
    setSetupData({
      projectName: getRandomItem(RANDOM_PODCAST_NAMES),
      synthesisMode: setupData.synthesisMode, // Keep current mode
      speaker1Name: getRandomItem(RANDOM_PERSONA_NAMES),
      speaker2Name: getRandomItem(RANDOM_PERSONA_NAMES),
      projectStyle: getRandomItem(RANDOM_PODCAST_STYLES),
      voice1: setupData.voice1, // Keep current voices
      voice2: setupData.voice2,
    });
  };

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" onClick={onClose} />
      <div className="relative bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700 p-6 w-full max-w-4xl shadow-2xl">
        
        {/* X Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-slate-400 hover:text-white transition-colors z-10"
          aria-label="Close modal"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {view === 'picker' && (
          <>
            <div className="mb-4">
              <h3 className="text-2xl font-semibold text-white font-fredoka">Choose a project to get started</h3>
              <p className="text-slate-300 mt-1 text-sm">Load an existing project or start a new one. You can switch projects anytime.</p>
            </div>
            {lastProjectName && onResumeLast && (
              <button
                type="button"
                onClick={handleResumeLast}
                className="w-full mb-4 px-4 py-3 rounded-lg bg-blue-600/90 hover:bg-blue-600 text-white transition-colors text-left"
              >
                ▶ Resume: <span className="font-semibold">{lastProjectName}</span>
              </button>
            )}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                type="button"
                onClick={handleLoadExisting}
                className="px-4 py-3 rounded-lg border border-slate-600 bg-slate-700/50 hover:bg-slate-700 text-slate-100"
              >
                📥 Load Project
              </button>
              <button
                type="button"
                onClick={handleStartNew}
                className="px-4 py-3 rounded-lg border border-green-600 bg-green-600/20 hover:bg-green-600/30 text-green-300"
              >
                ✨ Start New
              </button>
            </div>
          </>
        )}

        {view === 'loadProject' && (
          <>
            <div className="mb-4">
              <h3 className="text-2xl font-semibold text-white font-fredoka">Load Project</h3>
              <p className="text-slate-300 mt-1 text-sm">Choose a project to load from your saved projects.</p>
            </div>

            <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
              {isLoadingProjects ? (
                <div className="text-center py-8 text-slate-400">Loading projects...</div>
              ) : projects.length === 0 ? (
                <div className="text-center py-8 text-slate-400">No saved projects found</div>
              ) : (
                <div className="space-y-3">
                  {projects.map((project) => (
                    <div
                      key={project.projectId}
                      className="bg-slate-700/50 border border-slate-600 rounded-lg p-4 hover:bg-slate-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div 
                          className="flex-1 cursor-pointer"
                          onClick={() => handleLoadProject(project.projectId)}
                        >
                          <div className="font-medium text-white">{project.projectName}</div>
                          <div className="text-sm text-slate-400">
                            Updated: {new Date(project.updatedAt).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-slate-300 mt-1">
                            {project.description || 'No description'}
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <button
                            type="button"
                            onClick={() => handleLoadProject(project.projectId)}
                            disabled={isLoadingProjects}
                            className="px-3 py-2 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/30 rounded text-blue-300 transition-colors"
                            title="Load project"
                          >
                            📥 Load
                          </button>
                          <button
                            type="button"
                            onClick={(e) => handleDeleteProject(project.projectId, project.projectName, e)}
                            disabled={isLoadingProjects}
                            className="px-3 py-2 bg-red-600/20 hover:bg-red-600/30 border border-red-500/30 rounded text-red-300 transition-colors"
                            title="Delete project"
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="mt-6 flex gap-3 justify-end">
              <button
                onClick={handleBackToPicker}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                ← Back
              </button>
            </div>
          </>
        )}

        {view === 'newProject' && (
          <>
            <div className="mb-4">
              <h3 className="text-2xl font-semibold text-white font-fredoka">Create New Project</h3>
              <p className="text-slate-300 mt-1 text-sm">Set up your project details to get started.</p>
            </div>

            <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
              {/* Randomize Button - Moved to top */}
              <button
                type="button"
                onClick={randomizeSetup}
                className="w-full bg-slate-700/50 hover:bg-slate-700 rounded-lg p-3 text-blue-400 transition-colors"
              >
                🎲 Randomize Names & Style
              </button>

              {/* Project Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Project Type:</label>
                <div className="flex gap-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="podcast-new"
                      name="content-type-new"
                      className="w-4 h-4 text-blue-500"
                      checked={setupData.synthesisMode === 'podcast'}
                      onChange={() => setSetupData(prev => ({ ...prev, synthesisMode: 'podcast' }))}
                    />
                    <label htmlFor="podcast-new" className="text-white flex items-center gap-2">
                      <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                      Podcast (2 Speakers)
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="monologue-new"
                      name="content-type-new"
                      className="w-4 h-4 text-blue-500"
                      checked={setupData.synthesisMode === 'monologue'}
                      onChange={() => setSetupData(prev => ({ ...prev, synthesisMode: 'monologue' }))}
                    />
                    <label htmlFor="monologue-new" className="text-white flex items-center gap-2">
                      <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Monologue (1 Speaker)
                    </label>
                  </div>
                </div>
              </div>

              {/* Project Name */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Project Name:</label>
                <input
                  type="text"
                  value={setupData.projectName}
                  onChange={(e) => setSetupData(prev => ({ ...prev, projectName: e.target.value }))}
                  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  placeholder="My Awesome Project"
                />
              </div>

              {/* Speaker Names and Voices */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {setupData.synthesisMode === 'podcast' ? 'Speaker 1 Name:' : 'Speaker Name:'}
                  </label>
                  <input
                    type="text"
                    value={setupData.speaker1Name}
                    onChange={(e) => setSetupData(prev => ({ ...prev, speaker1Name: e.target.value }))}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                    placeholder={setupData.synthesisMode === 'podcast' ? 'Host' : 'Narrator'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {setupData.synthesisMode === 'podcast' ? 'Voice for Speaker 1:' : 'Voice:'}
                  </label>
                  <select
                    value={setupData.voice1}
                    onChange={(e) => {
                      console.log('🎤 Voice 1 changed to:', e.target.value);
                      setSetupData(prev => ({ ...prev, voice1: e.target.value }));
                    }}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                  >
                    {AVAILABLE_VOICES.map(voice => (
                      <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                    ))}
                  </select>
                </div>
              </div>

              {setupData.synthesisMode === 'podcast' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Speaker 2 Name:</label>
                    <input
                      type="text"
                      value={setupData.speaker2Name}
                      onChange={(e) => setSetupData(prev => ({ ...prev, speaker2Name: e.target.value }))}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                      placeholder="Co-host"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Voice for Speaker 2:</label>
                    <select
                      value={setupData.voice2}
                      onChange={(e) => {
                        console.log('🎤 Voice 2 changed to:', e.target.value);
                        setSetupData(prev => ({ ...prev, voice2: e.target.value }));
                      }}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white"
                    >
                      {AVAILABLE_VOICES.map(voice => (
                        <option key={voice.apiName} value={voice.apiName}>{voice.displayName}</option>
                      ))}
                    </select>
                  </div>
                </div>
              )}

              {/* Project Style */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Project Style:</label>
                <textarea
                  value={setupData.projectStyle}
                  onChange={(e) => setSetupData(prev => ({ ...prev, projectStyle: e.target.value }))}
                  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white resize-vertical"
                  placeholder="Describe your project style... (e.g., Casual conversation, investigative journalism, comedy sketch, educational deep-dive, etc.)"
                  rows={3}
                />
              </div>
            </div>

            <div className="mt-6 flex gap-3 justify-end">
              <button
                onClick={handleBackToPicker}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={handleCreateProject}
                disabled={!setupData.projectName.trim()}
                className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              >
                Create Project
              </button>
            </div>
          </>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteConfirmModal.isOpen}
        onClose={() => setDeleteConfirmModal({ isOpen: false, projectId: '', projectName: '' })}
        title="Delete Project"
        message={`Are you sure you want to delete "${deleteConfirmModal.projectName}"? This will permanently delete all associated scripts and audio files. This action cannot be undone.`}
        type="warning"
        confirmText="Delete Project"
        onConfirm={confirmDeleteProject}
      />
    </div>
  );
};

export default ProjectPickerModal;
