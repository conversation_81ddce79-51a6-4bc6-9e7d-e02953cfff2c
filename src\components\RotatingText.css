/* RotatingText Component Styles */

.text-rotate {
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.text-rotate-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-rotate-lines {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.text-rotate-word {
  display: inline-block;
  white-space: nowrap;
}

.text-rotate-element {
  display: inline-block;
  transform-origin: center bottom;
}

.text-rotate-space {
  display: inline-block;
  width: 0.25em;
}

/* Custom styles for the landing page */
.hero-rotating-text {
  font-family: 'Fredoka One', sans-serif;
  background: linear-gradient(135deg, #3498DB, #82AAE3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: normal; /* Fredoka One is inherently bold */
  display: inline-block;
  min-height: 1.2em;
  vertical-align: top;
  letter-spacing: 0.03em;
  text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.hero-rotating-text .text-rotate-element {
  background: linear-gradient(135deg, #3498DB, #82AAE3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ensure proper spacing and alignment */
.hero-title-container {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;
  line-height: 1.1;
}

.hero-title-static {
  font-family: 'Fredoka One', sans-serif;
  display: inline-block;
  font-weight: normal; /* Fredoka One is inherently bold */
  letter-spacing: 0.03em;
}

/* Animation improvements */
.text-rotate-element {
  will-change: transform, opacity;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-rotating-text {
    min-height: 1em;
  }
  
  .hero-title-container {
    gap: 0.1em;
  }
}
