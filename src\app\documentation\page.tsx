import React from 'react';
import PageLayout from '@/components/PageLayout';

export default function DocumentationPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            Documentation
          </h1>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Getting Started</h2>
              <p className="text-gray-300 mb-4">
                Learn the basics of WordWave Studio and create your first audio content.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Setting up your account</li>
                <li>• Creating your first project</li>
                <li>• Understanding the interface</li>
                <li>• Basic script generation</li>
              </ul>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Script Generation</h2>
              <p className="text-gray-300 mb-4">
                Master the art of AI-powered script creation for engaging content.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Choosing the right AI model</li>
                <li>• Crafting effective prompts</li>
                <li>• Customizing content style</li>
                <li>• Managing script length</li>
              </ul>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Voice & Audio</h2>
              <p className="text-gray-300 mb-4">
                Configure voices and audio settings for professional results.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Selecting appropriate voices</li>
                <li>• Adjusting speech parameters</li>
                <li>• Multi-speaker setup</li>
                <li>• Audio quality optimization</li>
              </ul>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Project Management</h2>
              <p className="text-gray-300 mb-4">
                Organize and manage your audio projects efficiently.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Cloud storage integration</li>
                <li>• Project organization</li>
                <li>• Collaboration features</li>
                <li>• Export and sharing</li>
              </ul>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">API Reference</h2>
              <p className="text-gray-300 mb-4">
                Integrate WordWave Studio into your applications.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Authentication</li>
                <li>• Endpoints overview</li>
                <li>• Request/response formats</li>
                <li>• Rate limits and quotas</li>
              </ul>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-semibold text-white mb-4">Troubleshooting</h2>
              <p className="text-gray-300 mb-4">
                Common issues and their solutions.
              </p>
              <ul className="space-y-2 text-gray-300">
                <li>• Audio generation errors</li>
                <li>• Account and billing issues</li>
                <li>• Performance optimization</li>
                <li>• Browser compatibility</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
            <h2 className="text-2xl font-semibold text-white mb-4">Need More Help?</h2>
            <p className="text-gray-300 mb-6">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="space-x-4">
              <a 
                href="/help-center" 
                className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Visit Help Center
              </a>
              <a 
                href="/contact" 
                className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
