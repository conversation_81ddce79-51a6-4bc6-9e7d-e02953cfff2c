import { NextResponse } from 'next/server';
import Stripe from 'stripe';

export async function GET() {
  try {
    // Check if required environment variables exist
    const requiredEnvVars = {
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
      NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID: process.env.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID,
      NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID: process.env.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID,
    };

    const missingVars = Object.entries(requiredEnvVars)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missingVars.length > 0) {
      return NextResponse.json({
        status: 'error',
        message: 'Missing environment variables',
        missingVariables: missingVars,
        timestamp: new Date().toISOString(),
      }, { status: 500 });
    }

    // Test Stripe connection
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
    
    // Try to retrieve account info (this will fail if API key is invalid)
    const account = await stripe.accounts.retrieve();
    
    return NextResponse.json({
      status: 'healthy',
      message: 'Stripe configuration is valid',
      accountId: account.id,
      environment: account.charges_enabled ? 'live' : 'test',
      timestamp: new Date().toISOString(),
      configuredPrices: {
        monthly: requiredEnvVars.NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID,
        lifetime: requiredEnvVars.NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID,
      }
    });

  } catch (error) {
    console.error('Stripe health check failed:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Stripe configuration failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
