"use client";

import React from 'react';
import Waves from './Waves';
import Noise from './Noise';
import PageHeader from './PageHeader';
import Footer from './Footer';

interface PageLayoutProps {
  children: React.ReactNode;
  className?: string;
}

const PageLayout: React.FC<PageLayoutProps> = ({ children, className = '' }) => {
  return (
    <div className={`relative min-h-screen bg-slate-900 overflow-hidden ${className}`}>
      {/* Noise Background */}
      <Noise
        patternSize={250}
        patternScaleX={1}
        patternScaleY={1}
        patternRefreshInterval={2}
        patternAlpha={20}
      />

      {/* Waves Background */}
      <Waves
        lineColor="#1e293b"
        backgroundColor="transparent"
        waveSpeedX={0.02}
        waveSpeedY={0.01}
        waveAmpX={40}
        waveAmpY={20}
        friction={0.9}
        tension={0.01}
        maxCursorMove={120}
        xGap={12}
        yGap={36}
      />

      {/* <PERSON> Header */}
      <PageHeader />

      {/* Page Content */}
      <div className="relative z-10">
        {children}
        
        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default PageLayout;
