import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, ValidationError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
    }>
}

// POST /api/projects/[projectId]/scripts - Save script for project
export async function POST(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;
    const body = await request.json();
    const { scriptData } = body;

    if (!scriptData || !scriptData.content) {
      throw new ValidationError('Script content is required');
    }

    console.log(`📝 Saving script for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // Ensure script is linked to project
    const scriptWithProject = {
      ...scriptData,
      projectId,
      metadata: {
        ...scriptData.metadata,
        userId,
        updatedAt: new Date().toISOString()
      }
    };

    const savedScript = await cloudService.saveScript(scriptWithProject, userId);

    const response: ApiResponse = {
      success: true,
      data: savedScript,
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('❌ Failed to save script:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to save script',
        code: error.code || 'SAVE_SCRIPT_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

// GET /api/projects/[projectId]/scripts - List scripts for project
export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await context.params;

    console.log(`📋 Listing scripts for project ${projectId}, user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const scripts = await cloudService.listScripts(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: scripts,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to list scripts:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to list scripts',
        code: error.code || 'LIST_SCRIPTS_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
