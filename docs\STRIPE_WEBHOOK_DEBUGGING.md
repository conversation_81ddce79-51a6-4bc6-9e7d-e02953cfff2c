# Stripe Webhook Debugging Guide

This document outlines the steps to debug and resolve issues with Stripe webhooks in a Vercel production environment, specifically addressing `307 Temporary Redirect` errors.

## Problem: Stripe Webhooks Failing with 307 Errors

When <PERSON>e sends webhook events to your Vercel deployment, they may fail with a `307 Temporary Redirect` error. This indicates that the webhook URL is being redirected by Vercel's platform before it can be processed by your application.

This typically happens when there is a mismatch between the webhook URL configured in your Stripe dashboard and the canonical domain configured in your Vercel project.

## Root Cause

The issue stems from a conflict between Vercel's platform-level domain redirection and the URL Stripe uses for webhooks.

For example, if your Vercel project is configured to redirect all traffic from the apex domain (`yourdomain.com`) to the `www` subdomain (`www.yourdomain.com`), but your Stripe webhook is configured to send events to `https://yourdomain.com/api/stripe/webhook`, Vercel will issue a `307` redirect. Stripe will not follow this redirect and will mark the webhook delivery as failed.

## Solution

To resolve this, you must ensure that the Stripe webhook URL matches your canonical domain in Vercel.

### Step 1: Identify Your Canonical Domain in Vercel

1.  Go to your Vercel project dashboard.
2.  Navigate to the **Settings** tab and then to the **Domains** section.
3.  Observe the domain configurations. Vercel will typically show which domain redirects to another. For example, you might see `wordwave.studio` redirecting to `www.wordwave.studio`. The destination of the redirect is your canonical domain.

### Step 2: Update the Webhook URL in Stripe

1.  Go to your Stripe Dashboard.
2.  Navigate to the **Developers** section and then to **Webhooks**.
3.  Select the webhook endpoint that is failing.
4.  Click **Update details...**.
5.  Change the **Endpoint URL** to use your canonical domain.

    *   **Incorrect:** `https://wordwave.studio/api/stripe/webhook`
    *   **Correct:** `https://www.wordwave.studio/api/stripe/webhook`

6.  Save the changes.

### Step 3: Verify `vercel.json` Configuration

Your `vercel.json` should be configured to handle rewrites correctly without interfering with the platform-level redirect. A clean configuration for the Stripe webhook rewrite would be:

```json
{
  "rewrites": [
    {
      "source": "/api/stripe/webhook",
      "destination": "/api/stripe/webhook"
    }
  ]
}
```

By removing any `redirects` for the apex/www domain from `vercel.json`, you allow Vercel's platform settings to manage it, which is the recommended approach.
