.project-status-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  margin-bottom: 0;
  backdrop-filter: none;
  transition: all 0.3s ease;
}

.project-status-indicator.new {
  border-color: transparent;
  background: transparent;
}

.project-status-indicator.loaded {
  border-color: transparent;
  background: transparent;
}

.status-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.status-icon {
  font-size: 20px;
  line-height: 1;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-title {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
  line-height: 1.2;
}

.status-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

.project-id {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.project-id-label {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.project-id-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .project-status-indicator {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-main {
    width: 100%;
  }
  
  .project-id {
    align-self: flex-end;
  }
}

/* Animation for auto-saving state */
.project-status-indicator .status-icon {
  transition: transform 0.3s ease;
}

.project-status-indicator .status-subtitle {
  transition: color 0.3s ease;
}

/* Pulse animation when auto-saving */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.project-status-indicator .status-icon {
  animation: none;
}

/* Add pulse animation when auto-saving */
.project-status-indicator[data-auto-saving="true"] .status-icon {
  animation: pulse 1.5s ease-in-out infinite;
}
