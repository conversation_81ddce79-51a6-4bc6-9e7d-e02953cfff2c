# Hostinger Deployment Guide for WordWave Next.js

## Prerequisites
- Hostinger VPS or Premium Shared Hosting with Node.js support
- Your app is built and tested locally (`npm run build` should work)
- Environment variables configured

## Deployment Methods

### Method 1: VPS Hosting (Recommended)

#### 1. Set Up VPS
1. Purchase Hostinger VPS plan
2. Choose Ubuntu/CentOS operating system
3. Access via SSH

#### 2. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js (use Node.js 18+ for Next.js 15)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx -y
```

#### 3. Deploy Your App
```bash
# Clone your repository (or upload files)
git clone https://github.com/pjecuacion/WordWaveNextJs.git
cd WordWaveNextJs

# Install dependencies
npm install

# Create production environment file
nano .env.local
```

#### 4. Environment Variables
Create `.env.local` with your production values:
```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_key
CLERK_SECRET_KEY=your_clerk_secret

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
STRIPE_SECRET_KEY=your_stripe_secret
STRIPE_WEBHOOK_SECRET=your_webhook_secret
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=your_monthly_price
NEXT_PUBLIC_STRIPE_LIFETIME_PRICE_ID=your_lifetime_price

# Backblaze B2
B2_APPLICATION_KEY_ID=your_b2_key_id
B2_APPLICATION_KEY=your_b2_key
B2_BUCKET_NAME=your_bucket_name
B2_BUCKET_ID=your_bucket_id

# Database URL (if using)
DATABASE_URL=your_database_url

# App URL
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

#### 5. Build and Start
```bash
# Build the application
npm run build

# Start with PM2
pm2 start npm --name "wordwave" -- start
pm2 save
pm2 startup
```

#### 6. Configure Nginx
```bash
sudo nano /etc/nginx/sites-available/wordwave
```

Add this configuration:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/wordwave /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 7. SSL Certificate (Optional but Recommended)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### Method 2: Shared Hosting with Node.js

#### 1. Check Node.js Support
- Ensure your Hostinger plan supports Node.js
- Access File Manager or use FTP

#### 2. Upload Files
1. Build your app locally: `npm run build`
2. Upload all files except `node_modules`
3. Access terminal/SSH if available

#### 3. Install Dependencies
```bash
npm install --production
```

#### 4. Configure Start Script
Create `server.js` if needed:
```javascript
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const dev = process.env.NODE_ENV !== 'production'
const app = next({ dev })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  }).listen(process.env.PORT || 3000, (err) => {
    if (err) throw err
    console.log('> Ready on http://localhost:3000')
  })
})
```

## Important Configuration Updates

### 1. Update Next.js Config for Production
```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'standalone', // For better deployment
  experimental: {
    serverActions: {
      allowedOrigins: ['yourdomain.com', 'www.yourdomain.com']
    }
  },
  images: {
    domains: ['yourdomain.com'],
  },
};

export default nextConfig;
```

### 2. Update Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start -p ${PORT:-3000}",
    "lint": "next lint"
  }
}
```

## Domain Configuration

### 1. Point Domain to Hostinger
- Update nameservers to Hostinger's
- Or update A record to point to your VPS IP

### 2. Update Environment Variables
- Set `NEXT_PUBLIC_APP_URL` to your domain
- Update Clerk and Stripe URLs to match your domain

## Troubleshooting

### Common Issues:
1. **Port conflicts**: Ensure port 3000 is available or use different port
2. **Environment variables**: Double-check all required env vars are set
3. **File permissions**: Ensure proper permissions on uploaded files
4. **Memory limits**: Check if your hosting plan has sufficient resources

### Performance Optimization:
1. Enable gzip compression in Nginx
2. Set up CDN through Hostinger (if available)
3. Optimize images and assets
4. Configure caching headers

## Monitoring
```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs wordwave

# Restart application
pm2 restart wordwave
```

## Backup Strategy
1. Regular database backups
2. File system backups
3. Environment variable backups

## Security Checklist
- [ ] SSL certificate installed
- [ ] Environment variables secured
- [ ] Firewall configured
- [ ] Regular updates scheduled
- [ ] Webhook endpoints secured

## Support
- Hostinger support for hosting issues
- Check application logs for debugging
- Monitor resource usage
