# Email Service Testing Guide

This directory contains test files for verifying email service integrations in WordWave.

## Available Email Providers

### 1. Sender (Recommended)
- **Free tier**: 15,000 emails/month
- **Paid plans**: Starting at $8/month for 60,000 emails
- **File**: `test-sender-email.js`
- **Environment variable**: `SENDER_API_KEY`

### 2. Resend
- **Free tier**: 3,000 emails/month  
- **Paid plans**: Starting at $20/month for 50,000 emails
- **File**: `test-resend-email.js`
- **Environment variable**: `RESEND_API_KEY`

### 3. SendGrid
- **Free tier**: 100 emails/month
- **Paid plans**: Starting at $15/month for 40,000 emails
- **Environment variable**: `SENDGRID_API_KEY`

## Testing Scripts

### Test All Services
```bash
npm run test-email
```
This runs `test-all-email-services.js` which:
- Checks which email services are configured
- Shows the priority order
- Sends a test email using the highest priority service
- Displays a comparison of all providers

### Test Specific Services
```bash
# Test Sender specifically
npm run test-sender

# Test Resend specifically  
npm run test-resend
```

## Setup Instructions

### 1. Environment Variables
Add your API keys to `.env.local`:

```bash
# Recommended: Sender (15,000 free emails/month)
SENDER_API_KEY=sender_your_api_key_here

# Alternative: Resend (3,000 free emails/month)
RESEND_API_KEY=re_your_api_key_here

# Alternative: SendGrid (100 free emails/month)
SENDGRID_API_KEY=SG.your_api_key_here

# From email address (use your verified domain)
FROM_EMAIL=<EMAIL>
```

### 2. Priority Order
The EmailService automatically detects and uses providers in this order:
1. **Sender** (if `SENDER_API_KEY` exists) 
2. **Resend** (if `RESEND_API_KEY` exists)
3. **SendGrid** (if `SENDGRID_API_KEY` exists)
4. **Development mode** (logs only, no actual emails sent)

### 3. Domain Verification
For production use:
- **Sender**: Add and verify your domain in Settings → Domains
- **Resend**: Add and verify your domain in the Resend dashboard
- **SendGrid**: Complete sender authentication setup

## Why Sender is Recommended

| Feature | Sender | Resend | SendGrid |
|---------|--------|--------|----------|
| **Free emails/month** | **15,000** | 3,000 | 100 |
| **Cost efficiency** | **Best** | Good | Expensive |
| **Setup complexity** | **Simple** | Simple | Complex |
| **Features** | **Rich** | Basic | Rich |
| **Deliverability** | **99%** | 99% | 99% |

## Test Email Recipients

By default, test emails are sent to `<EMAIL>`. You can modify the recipient in each test file:

```javascript
// In test files, change this line:
to: '<EMAIL>'
```

## Troubleshooting

### Common Issues:
1. **"API key not found"**: Ensure environment variables are set correctly
2. **"Domain not verified"**: Complete domain verification in the provider dashboard
3. **"Rate limit exceeded"**: You've hit the monthly limit for your plan
4. **"Invalid from address"**: Use a verified domain email address

### Debug Mode:
The EmailService includes detailed logging. Check the console output when running tests to see:
- Which provider is being used
- API request details
- Response information
- Error details if something fails

## Integration Status

✅ **Sender**: Fully integrated with comprehensive error handling  
✅ **Resend**: Fully integrated with comprehensive error handling  
✅ **SendGrid**: Fully integrated with comprehensive error handling  
✅ **Automatic fallback**: Seamless switching between providers  
✅ **Development mode**: Safe testing without API keys  

## Next Steps

1. **Choose your provider**: We recommend Sender for the generous free tier
2. **Get API key**: Sign up and get your API key
3. **Set environment variables**: Add keys to `.env.local`
4. **Test integration**: Run `npm run test-email`
5. **Verify domain**: Complete domain verification for production
6. **Deploy**: Your email service is ready for production use

The email service is now ready to handle all your application's email needs with automatic provider detection and fallback support!
