import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ requestId: string }> }
) {
  try {
    console.log('📥 GDPR Download API - Starting request processing');
    
    const { userId } = await auth();
    console.log('🔐 Auth result - userId:', userId);
    
    if (!userId) {
      console.log('❌ No userId found - returning unauthorized');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const { requestId } = params;
    console.log('📋 Request ID:', requestId);
    
    if (!requestId) {
      console.log('❌ No request ID provided');
      return NextResponse.json({ error: 'Request ID is required' }, { status: 400 });
    }

    console.log(`📥 Download request for export: ${requestId} by user: ${userId}`);

    try {
      // Initialize cloud storage service
      console.log('🔧 Initializing cloud storage...');
      const cloudStorage = new ServerCloudStorageService({
        applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
        applicationKey: process.env.B2_APPLICATION_KEY!,
        bucketName: process.env.B2_BUCKET_NAME!,
      });

      await cloudStorage.initialize();
      console.log('✅ Cloud storage initialized');

      // The file is stored as exports/${userId}/${requestId}/data-export-${timestamp}.zip
      // Since we don't know the exact timestamp, we need to list files in the directory
      const exportPath = `exports/${userId}/${requestId}/`;
      console.log(`🔍 Looking for export files in: ${exportPath}`);

      try {
        // List files in the export directory
        const files = await (cloudStorage as any).listFiles(exportPath);
        console.log(`📋 Found ${files.length} files in export directory:`, files.map((f: any) => f.fileName));

        // Find the data-export file
        const exportFile = files.find((file: any) => 
          file.fileName.startsWith(`${exportPath}data-export-`) && 
          file.fileName.endsWith('.zip')
        );

        if (!exportFile) {
          console.log('❌ No export file found in directory');
          return NextResponse.json({ 
            error: 'Export file not found',
            message: 'The export file could not be located in storage. It may have expired or been deleted.',
            requestId,
            userId,
            searchPath: exportPath
          }, { status: 404 });
        }

        console.log(`✅ Found export file: ${exportFile.fileName}`);

        // Download the export file
        console.log(`📥 Downloading export file: ${exportFile.fileName}`);
        const fileBuffer = await cloudStorage.downloadFilePublic(exportFile.fileName);

        console.log(`📥 Successfully downloaded export file: ${exportFile.fileName} (${fileBuffer.length} bytes)`);

        // Set appropriate headers for ZIP file download
        const headers = new Headers();
        headers.set('Content-Type', 'application/zip');
        headers.set('Content-Disposition', `attachment; filename="gdpr-export-${requestId}.zip"`);
        headers.set('Content-Length', fileBuffer.length.toString());
        headers.set('Cache-Control', 'no-cache');

        console.log('📤 Sending file to user...');

        // Return the file as a download
        return new NextResponse(new Uint8Array(fileBuffer), {
          status: 200,
          headers,
        });

      } catch (listError) {
        console.error('💥 Failed to list or download export files:', listError);
        console.error('List error stack:', listError instanceof Error ? listError.stack : 'No stack trace');
        
        // Fallback: try direct path construction with current timestamp pattern
        const fallbackPath = `exports/${userId}/${requestId}/data-export-${requestId.split('_')[1]}.zip`;
        console.log(`🔄 Trying fallback path: ${fallbackPath}`);
        
        try {
          const fileBuffer = await cloudStorage.downloadFilePublic(fallbackPath);
          console.log(`✅ Found file at fallback path: ${fallbackPath}`);
          
          const headers = new Headers();
          headers.set('Content-Type', 'application/zip');
          headers.set('Content-Disposition', `attachment; filename="gdpr-export-${requestId}.zip"`);
          headers.set('Content-Length', fileBuffer.length.toString());
          headers.set('Cache-Control', 'no-cache');

          return new NextResponse(new Uint8Array(fileBuffer), {
            status: 200,
            headers,
          });
        } catch (fallbackError) {
          console.error('💥 Fallback path also failed:', fallbackError);
          return NextResponse.json({ 
            error: 'Export file not found',
            message: 'The export file could not be located in storage using any known path pattern.',
            requestId,
            userId,
            details: listError instanceof Error ? listError.message : String(listError)
          }, { status: 404 });
        }
      }

    } catch (downloadError) {
      console.error('💥 Failed to download export file:', downloadError);
      console.error('Download error stack:', downloadError instanceof Error ? downloadError.stack : 'No stack trace');
      return NextResponse.json({ 
        error: 'Failed to download export',
        message: 'The export file could not be retrieved from storage',
        details: downloadError instanceof Error ? downloadError.message : String(downloadError)
      }, { status: 500 });
    }

  } catch (error) {
    console.error('💥 CRITICAL ERROR in GDPR download API:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
