import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, SaveProjectRequest, ValidationError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

/**
 * POST /api/cloud-storage/projects
 * Save a project to cloud storage
 */
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'User ID is required', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { projectData, options }: SaveProjectRequest = body;
    
    if (!projectData) {
      throw new ValidationError('Project data is required');
    }

    if (!projectData.id || !projectData.configuration?.projectName) {
      throw new ValidationError('Project must have an ID and name');
    }

    console.log(`📤 Saving project ${projectData.id} for user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const cloudProject = await cloudService.saveProject(
      projectData,
      userId,
      (progress) => {
        // In a real implementation, you might want to use WebSockets
        // or Server-Sent Events to send progress updates
        console.log('Upload progress:', progress);
      }
    );

    const response: ApiResponse = {
      success: true,
      data: {
        projectId: cloudProject.projectId,
        cloudPath: cloudProject.cloudPath,
        fileCount: cloudProject.files.length,
        message: 'Project saved successfully'
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('❌ Failed to save project:', error);
    
    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to save project',
        code: error.code || 'SAVE_ERROR',
        details: error.details
      }
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

/**
 * GET /api/cloud-storage/projects
 * List projects for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'User ID is required', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    const tags = searchParams.getAll('tags');

    console.log(`📋 Listing projects for user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    const projects = await cloudService.listProjects(userId, limit);

    // Apply client-side filtering for search and tags if needed
    let filteredProjects = projects;

    if (search) {
      const searchLower = search.toLowerCase();
      filteredProjects = filteredProjects.filter(project =>
        project.projectName.toLowerCase().includes(searchLower) ||
        project.description?.toLowerCase().includes(searchLower)
      );
    }

    if (tags && tags.length > 0) {
      filteredProjects = filteredProjects.filter(project =>
        project.tags?.some(tag => tags.includes(tag))
      );
    }

    // Apply pagination
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedProjects = filteredProjects.slice(startIndex, endIndex);

    const response: ApiResponse = {
      success: true,
      data: {
        projects: paginatedProjects,
        total: filteredProjects.length,
        limit: limit,
        offset: offset
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to list projects:', error);
    
    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to list projects',
        code: error.code || 'LIST_ERROR',
        details: error.details
      }
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
