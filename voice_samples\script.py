#!/usr/bin/env python3
"""
Voice Sample Generator for Google AI Studio Text-to-Speech
Generates audio samples for all available voices using Google's Gemini API.
"""

from google import genai
from google.genai import types
import os
import wave
import time
from typing import Optional

def save_audio_to_wav(filename: str, audio_data: bytes, sample_rate: int = 24000) -> None:
    """
    Saves audio data to a WAV file.
    
    Args:
        filename: Output file path
        audio_data: Raw audio bytes
        sample_rate: Audio sample rate (default: 24000 Hz)
    """
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data)

def generate_voice_sample(text: str, voice_name: str, api_key: str) -> Optional[bytes]:
    """
    Generate audio sample using Google AI Studio TTS API.
    
    Args:
        text: Text to convert to speech
        voice_name: Name of the voice to use
        api_key: Google AI API key
        
    Returns:
        Audio data as bytes, or None if failed
    """
    try:
        # Configure the client
        client = genai.Client(api_key=api_key)
        
        # Generate audio using the new TTS API
        response = client.models.generate_content(
            model="gemini-2.5-flash-preview-tts",
            contents=text,
            config=types.GenerateContentConfig(
                response_modalities=["AUDIO"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(
                            voice_name=voice_name,
                        )
                    )
                ),
            )
        )
        
        # Extract audio data
        if (response.candidates and 
            len(response.candidates) > 0 and 
            response.candidates[0].content.parts and 
            len(response.candidates[0].content.parts) > 0 and
            response.candidates[0].content.parts[0].inline_data):
            
            audio_data = response.candidates[0].content.parts[0].inline_data.data
            return audio_data
        else:
            print(f"No audio data returned for voice: {voice_name}")
            return None
            
    except Exception as e:
        print(f"Error generating audio for {voice_name}: {e}")
        return None

def main():
    """Main function to generate voice samples."""
    
    # Multiple API keys to rotate through (helps with rate limits)
    api_keys = [
        "AIzaSyBfSjhxL8PFGdEv559068_LX9nH4iERVsY",
        "AIzaSyDpLIqIqGalTr25oZGIZI6urhC4qR-VNho"
    ]
    
    # Fallback to environment variable if needed
    env_key = os.getenv('GEMINI_API_KEY')
    if env_key and env_key not in api_keys:
        api_keys.append(env_key)
    
    if not api_keys:
        print("Error: No API keys available.")
        print("You can get your API key from: https://makersuite.google.com/app/apikey")
        return
    
    current_key_index = 0
    print(f"Using {len(api_keys)} API key(s) for rate limit management")
    
    # List of available voices (as of 2025)
    voices = [
        "Achernar", "Achird", "Algenib", "Alnilam", "Aoede", "Autonoe", "Callirrhoe", "Charon",
        "Despina", "Enceladus", "Erinome", "Fenrir", "Gacrux", "Iapetus", "Kore", "Laomedeia",
        "Leda", "Orus", "Puck", "Pulcherrima", "Rasalgethi", "Sadachbia", "Sadaltager", "Schedar",
        "Sulafat", "Umbriel", "Vindemiatrix", "Zephyr", "Zubenelgenubi"
    ]
    
    # Text to be spoken
    test_text = "Hello, this is a sample of my voice. I hope you like how I sound!"
    
    print("Starting voice sample generation...")
    print(f"Total voices to process: {len(voices)}")
    
    # Create output directory
    output_dir = "voice_samples"
    os.makedirs(output_dir, exist_ok=True)
    
    successful_generations = 0
    failed_generations = 0
    
    for i, voice_name in enumerate(voices, 1):
        print(f"[{i}/{len(voices)}] Processing voice: {voice_name}")
        
        # Check if file already exists
        filename = f"{voice_name.lower()}_sample.wav"
        filepath = os.path.join(output_dir, filename)
        
        if os.path.exists(filepath):
            print(f"  ⏭️  Skipping {voice_name} (file already exists)")
            successful_generations += 1
            continue
        
        # Generate audio sample using current API key
        current_api_key = api_keys[current_key_index]
        print(f"  🔑 Using API key #{current_key_index + 1}: ...{current_api_key[-6:]}")
        audio_data = generate_voice_sample(test_text, voice_name, current_api_key)
        
        # Rotate to next API key for next request
        current_key_index = (current_key_index + 1) % len(api_keys)
        
        if audio_data:
            # Save to file
            try:
                save_audio_to_wav(filepath, audio_data)
                print(f"  ✓ Successfully saved: {filepath}")
                successful_generations += 1
            except Exception as e:
                print(f"  ✗ Failed to save {filename}: {e}")
                failed_generations += 1
        else:
            print(f"  ✗ Failed to generate audio for {voice_name}")
            failed_generations += 1
        
        # Add a delay to avoid rate limiting 
        # With multiple API keys, we can reduce the delay since each key has its own limit
        delay = 21 // len(api_keys)  # Divide delay by number of keys
        time.sleep(max(delay, 2))  # Minimum 2 seconds between requests
    
    # Summary
    print("\n" + "="*50)
    print("Voice Sample Generation Complete!")
    print(f"Successful: {successful_generations}")
    print(f"Failed: {failed_generations}")
    print(f"Output directory: {os.path.abspath(output_dir)}")
    print("="*50)

if __name__ == "__main__":
    main()