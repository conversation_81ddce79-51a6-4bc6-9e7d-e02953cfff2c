import React from 'react';
import PageLayout from '@/components/PageLayout';

export default function HowItWorksPage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
            How It Works
          </h1>
          
          <div className="space-y-12">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-1/3">
                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto md:mx-0">
                  1
                </div>
              </div>
              <div className="md:w-2/3">
                <h2 className="text-2xl font-semibold text-white mb-4">Define Your Content</h2>
                <p className="text-gray-300 leading-relaxed">
                  Start by specifying your podcast topic, style, and target audience. 
                  Choose between solo monologues or multi-speaker conversations. 
                  Set up personas for your speakers with unique characteristics and speaking styles.
                </p>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row-reverse items-center gap-8">
              <div className="md:w-1/3">
                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto md:mx-0">
                  2
                </div>
              </div>
              <div className="md:w-2/3">
                <h2 className="text-2xl font-semibold text-white mb-4">AI Script Generation</h2>
                <p className="text-gray-300 leading-relaxed">
                  Our advanced AI models generate professional scripts based on your specifications. 
                  The AI creates engaging dialogue, natural conversation flow, and compelling content 
                  that matches your desired style and tone.
                </p>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-1/3">
                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto md:mx-0">
                  3
                </div>
              </div>
              <div className="md:w-2/3">
                <h2 className="text-2xl font-semibold text-white mb-4">Voice Synthesis</h2>
                <p className="text-gray-300 leading-relaxed">
                  Convert your generated script into high-quality audio using state-of-the-art 
                  text-to-speech technology. Choose from multiple voices and adjust speaking 
                  parameters to match your personas perfectly.
                </p>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row-reverse items-center gap-8">
              <div className="md:w-1/3">
                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto md:mx-0">
                  4
                </div>
              </div>
              <div className="md:w-2/3">
                <h2 className="text-2xl font-semibold text-white mb-4">Export & Publish</h2>
                <p className="text-gray-300 leading-relaxed">
                  Download your completed audio content in professional formats ready for publishing. 
                  Save projects to the cloud for future editing and share with your team or 
                  upload directly to your favorite podcast platform.
                </p>
              </div>
            </div>
          </div>
          
          <div className="text-center mt-16">
            <a 
              href="/" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get Started Now
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
