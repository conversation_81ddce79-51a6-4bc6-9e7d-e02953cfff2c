# Voice Sample Generator

This directory contains scripts to generate audio samples for different TTS (Text-to-Speech) services.

## Available Scripts

### 1. Google AI Studio Script (`script.py`)
- **Status**: Framework only (TTS not yet implemented in Gemini API)
- **Purpose**: Template for when Google adds TTS support to Gemini

### 2. ElevenLabs Script (`elevenlabs_script.py`) ⭐ **RECOMMENDED**
- **Status**: Fully functional
- **Purpose**: Generate samples from all ElevenLabs voices

## Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Choose your TTS service and get API key:**

   ### For ElevenLabs (Recommended):
   - Visit [ElevenLabs](https://elevenlabs.io/)
   - Sign up for an account
   - Get your API key from the profile page
   
   ### For Google AI Studio:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key

3. **Set your API key as environment variable:**
   
   **Windows (PowerShell):**
   ```powershell
   # For ElevenLabs
   $env:ELEVENLABS_API_KEY="your_api_key_here"
   
   # For Google AI Studio  
   $env:GEMINI_API_KEY="your_api_key_here"
   ```
   
   **Linux/Mac:**
   ```bash
   # For ElevenLabs
   export ELEVENLABS_API_KEY="your_api_key_here"
   
   # For Google AI Studio
   export GEMINI_API_KEY="your_api_key_here"
   ```

## Usage

### ElevenLabs Script (Recommended):
```bash
python elevenlabs_script.py
```

### Google AI Studio Script:
```bash
python script.py
```

## Output

### ElevenLabs:
- Creates `elevenlabs_voice_samples/` directory
- Saves MP3 files: `adam_sample.mp3`, `bella_sample.mp3`, etc.
- Downloads samples from all available voices in your account

### Google AI Studio:
- Creates `voice_samples/` directory
- Currently only shows placeholder output (no actual audio generation)

## Rate Limits & Costs

### ElevenLabs:
- Free tier: 10,000 characters/month
- Each voice sample uses ~60 characters
- Includes 1-second delay between requests to respect rate limits

### Google AI Studio:
- Pricing varies by model and usage
- Check current pricing at [Google Gemini API Pricing](https://ai.google.dev/gemini-api/docs/pricing)

## Alternative TTS Services

If you need other TTS options, consider:

1. **OpenAI TTS API**
   - High quality, multiple voices
   - Commercial license included

2. **Google Cloud Text-to-Speech**
   - Enterprise-grade, many languages
   - Pay-per-use pricing

3. **Azure Cognitive Services Speech**
   - Microsoft's TTS service
   - Good integration with other Azure services

4. **Amazon Polly**
   - AWS TTS service
   - Neural and standard voices available
