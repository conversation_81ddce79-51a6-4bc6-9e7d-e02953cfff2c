{"name": "wordwave-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "update-models": "node scripts/update-models.js", "test-email": "node test-all-email-services.js", "test-sender": "node test-sender-email.js", "test-resend": "node test-resend-email.js"}, "dependencies": {"@clerk/nextjs": "^6.22.0", "@google/genai": "^1.6.0", "@google/generative-ai": "^0.24.1", "@sendgrid/mail": "^8.1.5", "@stripe/stripe-js": "^7.8.0", "@types/jszip": "^3.4.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.11.0", "backblaze-b2": "^1.7.1", "framer-motion": "^12.18.1", "jszip": "^3.10.1", "lucide-react": "^0.536.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.7.0", "stripe": "^18.4.0", "three": "^0.177.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/backblaze-b2": "^1.5.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.177.0", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}