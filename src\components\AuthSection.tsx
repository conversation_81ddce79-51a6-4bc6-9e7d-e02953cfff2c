'use client';

import React from 'react';
import { SignInButton, SignUpButton, useUser } from '@clerk/nextjs';

export const AuthSection: React.FC = () => {
  const { isSignedIn } = useUser();

  if (!isSignedIn) {
    return (
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white mb-4">Get Started</h3>
          <p className="text-gray-300 mb-6">
            Sign in to start creating AI-powered audio content
          </p>
          <div className="flex gap-4 justify-center">
            <SignInButton mode="modal">
              <button type="button" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                Sign In
              </button>
            </SignInButton>
            <SignUpButton mode="modal">
              <button type="button" className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                Sign Up
              </button>
            </SignUpButton>
          </div>
        </div>
      </div>
    );
  }

  // For authenticated users, we no longer show the API configuration here
  // as it's now moved to the AccountMenu dropdown
  return null;
};
