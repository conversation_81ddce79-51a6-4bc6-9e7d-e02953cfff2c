// Component for managing model configurations
'use client';

import { useState } from 'react';
import { useModels } from '../hooks/useModels';
import { updateModelsConfig } from '../services/ModelService';

interface ModelManagerProps {
  apiKey?: string;
}

export default function ModelManager({ apiKey }: ModelManagerProps) {
  const [useAPIModels, setUseAPIModels] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<string>('');

  const { 
    scriptModels, 
    ttsModels, 
    isLoading, 
    error, 
    lastUpdated, 
    refreshModels 
  } = useModels({
    useAPI: useAPIModels,
    apiKey,
    autoRefresh: false,
  });

  const handleUpdateConfig = async () => {
    if (!apiKey) {
      setUpdateStatus('API key is required to fetch latest models');
      return;
    }

    try {
      setIsUpdating(true);
      setUpdateStatus('Fetching latest models from Google API...');
      
      await updateModelsConfig(apiKey);
      setUpdateStatus('Models config updated successfully! Check console for the updated JSON.');
      
      // Refresh the current models display
      await refreshModels();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update models';
      setUpdateStatus(`Error: ${errorMessage}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="bg-slate-800 rounded-lg p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Model Configuration</h3>
        <div className="text-xs text-gray-400">
          Last updated: {lastUpdated ? new Date(lastUpdated).toLocaleString() : 'Unknown'}
        </div>
      </div>

      {/* Toggle between config file and API */}
      <div className="flex items-center space-x-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={useAPIModels}
            onChange={(e) => setUseAPIModels(e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-gray-300">
            Use live API models {!apiKey && '(requires API key)'}
          </span>
        </label>

        <button
          onClick={refreshModels}
          disabled={isLoading}
          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded text-sm text-white"
        >
          {isLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded p-3 text-red-300 text-sm">
          Error: {error}
        </div>
      )}

      {/* Current Models Display */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Script Models */}
        <div>
          <h4 className="font-medium text-white mb-3">Script Models ({scriptModels.length})</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {scriptModels.map((model) => (
              <div key={model.apiName} className="bg-slate-700/50 rounded p-3 text-sm">
                <div className="font-mono text-blue-300">{model.apiName}</div>
                {model.description && (
                  <div className="text-xs text-gray-400 mt-1">{model.description}</div>
                )}
                {model.category && (
                  <div className={`inline-block px-2 py-1 rounded text-xs mt-2 ${
                    model.category === 'premium' ? 'bg-purple-900/50 text-purple-300' :
                    model.category === 'lite' ? 'bg-green-900/50 text-green-300' :
                    'bg-blue-900/50 text-blue-300'
                  }`}>
                    {model.category}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* TTS Models */}
        <div>
          <h4 className="font-medium text-white mb-3">TTS Models ({ttsModels.length})</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {ttsModels.map((model) => (
              <div key={model.apiName} className="bg-slate-700/50 rounded p-3 text-sm">
                <div className="font-mono text-green-300">{model.apiName}</div>
                {model.description && (
                  <div className="text-xs text-gray-400 mt-1">{model.description}</div>
                )}
                {model.category && (
                  <div className={`inline-block px-2 py-1 rounded text-xs mt-2 ${
                    model.category === 'premium' ? 'bg-purple-900/50 text-purple-300' :
                    model.category === 'lite' ? 'bg-green-900/50 text-green-300' :
                    'bg-blue-900/50 text-blue-300'
                  }`}>
                    {model.category}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Update Configuration */}
      <div className="border-t border-slate-600 pt-4">
        <h4 className="font-medium text-white mb-3">Update Configuration</h4>
        <div className="space-y-3">
          <p className="text-sm text-gray-400">
            Fetch the latest model list from Google's API and update the local configuration.
            This will log the updated JSON to the console.
          </p>
          
          <button
            onClick={handleUpdateConfig}
            disabled={isUpdating || !apiKey}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded text-white text-sm"
          >
            {isUpdating ? 'Updating...' : 'Update from API'}
          </button>

          {updateStatus && (
            <div className={`text-sm p-2 rounded ${
              updateStatus.startsWith('Error') 
                ? 'bg-red-900/20 text-red-300 border border-red-500'
                : 'bg-green-900/20 text-green-300 border border-green-500'
            }`}>
              {updateStatus}
            </div>
          )}
        </div>

        <div className="mt-4 p-3 bg-slate-700/30 rounded text-xs text-gray-400">
          <p className="font-medium mb-1">Instructions:</p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Click "Update from API" to fetch the latest models</li>
            <li>Check the browser console for the updated JSON configuration</li>
            <li>Copy the JSON and update <code>src/config/models.json</code></li>
            <li>Restart the development server to use the new models</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
