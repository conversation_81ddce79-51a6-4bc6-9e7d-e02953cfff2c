import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { BackgroundJobQueue } from '@/lib/BackgroundJobQueue';

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const jobId = searchParams.get('jobId');
    const type = searchParams.get('type'); // 'data-export' or 'account-deletion'

    const jobQueue = BackgroundJobQueue.getInstance();
    
    if (jobId) {
      // Get specific job status
      const job = jobQueue.getJobStatus(jobId);
      
      if (!job) {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }

      if (job.userId !== userId) {
        return NextResponse.json({ error: 'Unauthorized access to job' }, { status: 403 });
      }
      
      return NextResponse.json({
        success: true,
        job: {
          id: job.id,
          type: job.type,
          status: job.status,
          createdAt: job.createdAt,
          startedAt: job.startedAt,
          completedAt: job.completedAt,
          error: job.error,
          retryCount: job.retryCount
        }
      });
    } else {
      // Get all jobs for the user
      let userJobs = jobQueue.getUserJobs(userId);
      
      // Filter by type if specified
      if (type) {
        userJobs = userJobs.filter(job => job.type === type);
      }
      
      return NextResponse.json({
        success: true,
        jobs: userJobs.map(job => ({
          id: job.id,
          type: job.type,
          status: job.status,
          createdAt: job.createdAt,
          startedAt: job.startedAt,
          completedAt: job.completedAt,
          error: job.error,
          retryCount: job.retryCount
        })),
        stats: jobQueue.getQueueStats()
      });
    }

  } catch (error) {
    console.error('Error getting job status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
