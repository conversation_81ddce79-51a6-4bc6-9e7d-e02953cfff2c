#!/bin/bash

# WordWave Hostinger Deployment Script
# This script helps deploy your Next.js app to Hostinger VPS

echo "🚀 WordWave Deployment Script for Hostinger"
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Make sure you're in the project root directory."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building the application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

echo "✅ Build successful!"

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "⚠️  PM2 not found. Installing PM2..."
    npm install -g pm2
fi

# Stop existing process if running
echo "🛑 Stopping existing process..."
pm2 stop wordwave 2>/dev/null || true

# Start the application with PM2
echo "🚀 Starting application with PM2..."
pm2 start npm --name "wordwave" -- start

# Save PM2 process list
pm2 save

echo "✅ Deployment complete!"
echo ""
echo "📊 Application Status:"
pm2 status

echo ""
echo "🔍 To check logs: pm2 logs wordwave"
echo "🔄 To restart: pm2 restart wordwave"
echo "🛑 To stop: pm2 stop wordwave"
