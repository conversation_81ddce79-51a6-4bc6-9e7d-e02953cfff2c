// Test script to check B2 bucket contents using B2 API directly
const https = require('https');

async function testB2Bucket() {
  try {
    console.log('🧪 Testing B2 bucket contents...');
    
    const cloudService = new ServerCloudStorageService({
      applicationKeyId: process.env.B2_APPLICATION_KEY_ID,
      applicationKey: process.env.B2_APPLICATION_KEY,
      bucketName: process.env.B2_BUCKET_NAME
    });

    await cloudService.initialize();
    
    // List all files in the bucket
    const userId = 'user_2ydPQl0gh8x5iVgRR5fYJ3zYFtm'; // Replace with actual user ID
    const projects = await cloudService.listProjects(userId);
    
    console.log(`📋 Found ${projects.length} projects:`);
    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.name} (ID: ${project.id})`);
      console.log(`   Created: ${project.createdAt}`);
      console.log(`   Files: ${project.fileCount}`);
      console.log(`   Size: ${project.totalSize} bytes`);
    });

    // Check for audio files in each project
    for (const project of projects) {
      try {
        console.log(`\n🎵 Checking audio files for project: ${project.name}`);
        const audioList = await cloudService.listAudio(project.id, userId);
        console.log(`   Found ${audioList.length} audio files:`);
        
        audioList.forEach((audio, index) => {
          console.log(`   ${index + 1}. ${audio.name}`);
          console.log(`      ID: ${audio.id}`);
          console.log(`      Files: ${audio.files?.length || 0}`);
          console.log(`      Generated: ${audio.generatedAt}`);
        });
      } catch (error) {
        console.log(`   ❌ Error listing audio: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

testB2Bucket();
