// Enhanced Voice Selector Component with filtering and search
import React, { useState, useMemo } from 'react';
import { useVoices } from '../hooks/useVoices';
import { VoiceInfo } from '../services/ModelService';

interface VoiceSelectorProps {
  value?: string;
  onChange: (voiceApiName: string) => void;
  placeholder?: string;
  showFilters?: boolean;
  disabled?: boolean;
  className?: string;
}

export const VoiceSelector: React.FC<VoiceSelectorProps> = ({
  value,
  onChange,
  placeholder = "Select a voice...",
  showFilters = false,
  disabled = false,
  className = "",
}) => {
  const { voices, isLoading, error } = useVoices();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [genderFilter, setGenderFilter] = useState<string>('');
  const [ageFilter, setAgeFilter] = useState<string>('');

  // Filter voices based on search and filters
  const filteredVoices = useMemo(() => {
    return voices.filter(voice => {
      const matchesSearch = !searchTerm || 
        voice.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        voice.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = !categoryFilter || voice.category === categoryFilter;
      const matchesGender = !genderFilter || voice.gender === genderFilter;
      const matchesAge = !ageFilter || voice.ageRange === ageFilter;

      return matchesSearch && matchesCategory && matchesGender && matchesAge;
    });
  }, [voices, searchTerm, categoryFilter, genderFilter, ageFilter]);

  // Get unique filter values
  const categories = useMemo(() => [...new Set(voices.map(v => v.category).filter(Boolean))], [voices]);
  const genders = useMemo(() => [...new Set(voices.map(v => v.gender).filter(Boolean))], [voices]);
  const ageRanges = useMemo(() => [...new Set(voices.map(v => v.ageRange).filter(Boolean))], [voices]);

  const selectedVoice = voices.find(v => v.apiName === value);

  if (isLoading) {
    return (
      <select disabled className={`${className} opacity-50`}>
        <option>Loading voices...</option>
      </select>
    );
  }

  if (error) {
    return (
      <select disabled className={`${className} border-red-500`}>
        <option>Error loading voices</option>
      </select>
    );
  }

  return (
    <div className="voice-selector">
      {showFilters && (
        <div className="voice-filters mb-4 space-y-2">
          <input
            type="text"
            placeholder="Search voices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category ? category.charAt(0).toUpperCase() + category.slice(1) : 'Unknown'}
                </option>
              ))}
            </select>

            <select
              value={genderFilter}
              onChange={(e) => setGenderFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">All Genders</option>
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : 'Unknown'}
                </option>
              ))}
            </select>

            <select
              value={ageFilter}
              onChange={(e) => setAgeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">All Ages</option>
              {ageRanges.map(age => (
                <option key={age} value={age}>
                  {age ? age.charAt(0).toUpperCase() + age.slice(1) : 'Unknown'}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      <select
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={className}
      >
        <option value="">{placeholder}</option>
        {filteredVoices.map(voice => (
          <option key={voice.apiName} value={voice.apiName}>
            {voice.name} {voice.category === 'premium' ? '⭐' : ''}
            {voice.description ? ` - ${voice.description.slice(0, 50)}...` : ''}
          </option>
        ))}
      </select>

      {selectedVoice && (
        <div className="voice-preview mt-2 p-3 bg-gray-50 rounded-md text-sm">
          <div className="font-medium">{selectedVoice.name}</div>
          {selectedVoice.description && (
            <div className="text-gray-600 mt-1">{selectedVoice.description}</div>
          )}
          <div className="flex gap-2 mt-2">
            <span className={`px-2 py-1 rounded text-xs ${
              selectedVoice.category === 'premium' 
                ? 'bg-yellow-100 text-yellow-800' 
                : 'bg-blue-100 text-blue-800'
            }`}>
              {selectedVoice.category}
            </span>
            <span className="px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
              {selectedVoice.gender}
            </span>
            <span className="px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
              {selectedVoice.ageRange}
            </span>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500 mt-1">
        Showing {filteredVoices.length} of {voices.length} voices
      </div>
    </div>
  );
};

export default VoiceSelector;
