# Banner System Documentation

## Overview
The banner system provides a configurable way to display announcements, promotions, maintenance notices, and other important messages to users across the entire WordWave Studio application.

## Configuration File
The banner is configured via `public/banner-config.json`. This file is publicly accessible and can be updated without code changes.

Example configurations can be found in the `banner-examples/` directory.

### Configuration Options

```json
{
  "enabled": true,                    // Show/hide the banner
  "type": "info",                     // Banner type (affects styling)
  "title": "🎉 New Feature Alert!",   // Optional title text
  "message": "Your announcement message here",
  "ctaText": "Try Now",              // Optional call-to-action button text
  "ctaLink": "/app",                 // Optional CTA link
  "dismissible": true,               // Allow users to dismiss the banner
  "showUntil": "2025-10-15",        // Optional expiration date (YYYY-MM-DD)
  "priority": "normal"               // Animation priority
}
```

### Banner Types
- `"info"` - Blue background for general information
- `"success"` - Green background for positive news
- `"warning"` - Yellow background for important notices
- `"error"` - Red background for urgent issues
- `"promo"` - Purple gradient for promotions/marketing
- `"maintenance"` - Orange background for maintenance notices

### Priority Levels
- `"low"` - No special animation
- `"normal"` - No special animation
- `"high"` - Bounce animation
- `"urgent"` - Pulse animation

## Usage Examples

### Basic Announcement
```json
{
  "enabled": true,
  "type": "info",
  "message": "New voice models are now available in the studio!",
  "dismissible": true,
  "priority": "normal"
}
```

### Promotion with CTA
```json
{
  "enabled": true,
  "type": "promo",
  "title": "🚀 Limited Time Offer",
  "message": "Get 50% off your first month with code LAUNCH50",
  "ctaText": "Claim Offer",
  "ctaLink": "/pricing",
  "dismissible": true,
  "showUntil": "2025-12-31",
  "priority": "high"
}
```

### Maintenance Notice
```json
{
  "enabled": true,
  "type": "maintenance",
  "title": "⚠️ Scheduled Maintenance",
  "message": "System will be down for maintenance on Dec 15, 2025 from 2-4 AM EST",
  "dismissible": false,
  "showUntil": "2025-12-15",
  "priority": "urgent"
}
```

### Emergency Alert
```json
{
  "enabled": true,
  "type": "error",
  "title": "🚨 Service Issue",
  "message": "We're experiencing high load. Some features may be slower than usual.",
  "dismissible": false,
  "priority": "urgent"
}
```

## Features

### Automatic Expiration
Set a `showUntil` date to automatically hide the banner after a specific date.

### User Dismissal
When `dismissible` is true, users can close the banner. Their choice is remembered in localStorage using a unique key based on the banner type and message content.

### Responsive Design
The banner automatically adapts to different screen sizes:
- On mobile: Title and message stack vertically
- On desktop: All elements are in a single row
- CTA button text is optimized for smaller screens

### Accessibility
- Proper ARIA labels for dismiss button
- High contrast colors for readability
- Keyboard navigation support

## Technical Implementation

### Component Location
- Main component: `src/components/Banner.tsx`
- Config file: `public/banner-config.json`
- Example configs: `banner-examples/` directory

### Integration Points
The banner is automatically included in:
1. Main app layout (`src/app/layout.tsx`)
2. Page layout component (`src/components/PageLayout.tsx`) 
3. Landing page (`src/components/LandingPage.tsx`)

### Local Storage Keys
Dismissed banners are stored in localStorage as:
```
dismissedBanners: ["info-Your announcement message here", ...]
```

## Best Practices

1. **Keep messages concise** - Banner space is limited, especially on mobile
2. **Use appropriate types** - Match the banner type to the message urgency
3. **Set expiration dates** - Avoid showing outdated promotional content
4. **Test on mobile** - Ensure the banner looks good on all screen sizes
5. **Use CTAs sparingly** - Only include when there's a clear action to take
6. **Consider dismissibility** - Emergency notices should not be dismissible

## Updating the Banner

1. Edit `public/banner-config.json`
2. To use an example: Copy content from `banner-examples/` folder to `public/banner-config.json`
3. The changes will take effect immediately for new page loads
4. Users who previously dismissed the banner will see it again if the message content changes
5. No code deployment required

## Troubleshooting

### Banner not showing
- Check that `"enabled": true` in the config
- Verify the JSON syntax is valid
- Check browser console for any errors
- Ensure the `showUntil` date hasn't passed

### Banner shows for dismissed users
- This happens when the message content changes
- This is intentional to ensure important updates are seen
- Users can dismiss the updated banner again

### Styling issues
- The banner uses Tailwind CSS classes
- Custom styling can be added by modifying the component
- Consider testing across different themes if added
