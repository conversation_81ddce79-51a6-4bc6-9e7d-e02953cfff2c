@echo off
REM Interactive model updater with API key input
REM More user-friendly version

echo.
echo ========================================
echo WordWave Models & Voices Updater
echo ========================================
echo.

echo This script will update your models.json file with the latest
echo models, TTS models, and voices from Google's Gemini API.
echo.

REM Prompt for API key
echo Please enter your Google AI Studio API key:
echo (You can get one from: https://aistudio.google.com/app/apikey)
echo.
set /p api_key="API Key: "

if "%api_key%"=="" (
    echo.
    echo Error: No API key provided!
    pause
    exit /b 1
)

echo.
echo Updating models with your API key...
echo Please wait, this may take a few moments...
echo.

REM Run the update
npm run update-models %api_key%

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ SUCCESS! 
    echo ========================================
    echo.
    echo Your models and voices have been updated successfully!
    echo.
    echo What would you like to do next?
    echo.
    echo 1. Restart development server now
    echo 2. Just exit (remember to restart server manually)
    echo.
    set /p choice="Enter your choice (1 or 2): "
    
    if "%choice%"=="1" (
        echo.
        echo Restarting development server...
        echo.
        REM Kill existing node processes
        taskkill /F /IM node.exe >nul 2>&1
        timeout /t 2 >nul
        
        REM Start dev server
        echo Starting fresh server...
        npm run dev
    ) else (
        echo.
        echo Remember to run 'npm run dev' to restart your server!
        echo.
        pause
    )
) else (
    echo.
    echo ========================================
    echo ❌ ERROR
    echo ========================================
    echo.
    echo Something went wrong. Please check:
    echo.
    echo • Your API key is correct
    echo • You have internet connection  
    echo • Your Google AI quota is not exceeded
    echo.
    echo You can also try running manually:
    echo npm run update-models YOUR_API_KEY
    echo.
    pause
    exit /b %errorlevel%
)
