import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApiResponse, NotFoundError, ValidationError } from '@/lib/types';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

interface RouteContext {
    params: Promise<{
        projectId: string;
    }>
}

/**
 * GET /api/cloud-storage/projects/[projectId]
 * Load a specific project from cloud storage
 */
export async function GET(
  request: NextRequest,
  { params }: RouteContext
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'User ID is required', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await params;
    if (!projectId) {
      throw new ValidationError('Project ID is required');
    }

    const { searchParams } = new URL(request.url);
    const includeAudio = searchParams.get('includeAudio') === 'true';

    console.log(`📥 Loading project ${projectId} for user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // Load project from cloud storage
    const projectData = await cloudService.loadProject(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: projectData,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to load project:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to load project',
        code: error.code || 'LOAD_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}

/**
 * DELETE /api/cloud-storage/projects/[projectId]
 * Delete a specific project from cloud storage
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteContext
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'User ID is required', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    const { projectId } = await params;
    if (!projectId) {
      throw new ValidationError('Project ID is required');
    }

    const body = await request.json();
    const { confirmDelete } = body;

    if (!confirmDelete) {
      throw new ValidationError('Delete confirmation is required');
    }

    console.log(`🗑️ Deleting project ${projectId} for user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // Delete project from cloud storage
    await cloudService.deleteProject(projectId, userId);

    const response: ApiResponse = {
      success: true,
      data: {
        message: 'Project deleted successfully',
        projectId: projectId,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Failed to delete project:', error);

    const response: ApiResponse = {
      success: false,
      error: {
        message: error.message || 'Failed to delete project',
        code: error.code || 'DELETE_ERROR',
        details: error.details,
      },
    };

    const statusCode = error.statusCode || 500;
    return NextResponse.json(response, { status: statusCode });
  }
}
