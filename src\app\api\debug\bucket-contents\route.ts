import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ServerCloudStorageService } from '@/lib/CloudStorageService';

// Initialize cloud storage service
const cloudService = new ServerCloudStorageService({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID!,
  applicationKey: process.env.B2_APPLICATION_KEY!,
  bucketName: process.env.B2_BUCKET_NAME!
});

// GET /api/debug/bucket-contents - Debug endpoint to check bucket contents
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: { message: 'Unauthorized', code: 'UNAUTHORIZED' } },
        { status: 401 }
      );
    }

    console.log(`🔍 Debug: Checking bucket contents for user ${userId}`);

    // Initialize cloud storage service
    await cloudService.initialize();

    // List projects
    const projects = await cloudService.listProjects(userId);
    console.log(`📋 Found ${projects.length} projects`);

    const bucketContents: {
      projects: any[];
      totalProjects: number;
      userId: string;
    } = {
      projects: [],
      totalProjects: projects.length,
      userId
    };

    // Check each project for audio files
    for (const project of projects) {
      try {
        const audioList = await cloudService.listAudio(project.projectId, userId);
        console.log(`🎵 Project ${project.projectName}: ${audioList.length} audio files`);

        const projectInfo = {
          ...project,
          audioFiles: audioList.map(audio => ({
            id: audio.id,
            name: audio.name,
            fileCount: audio.files?.length || 0,
            generatedAt: audio.generatedAt,
            files: audio.files || []
          }))
        };

        bucketContents.projects.push(projectInfo as any);
      } catch (audioError) {
        console.warn(`⚠️ Failed to list audio for project ${project.projectName}:`, audioError);
        bucketContents.projects.push({
          ...project,
          audioFiles: [],
          error: audioError instanceof Error ? audioError.message : 'Unknown error'
        } as any);
      }
    }

    return NextResponse.json({
      success: true,
      data: bucketContents,
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('❌ Debug bucket contents failed:', error);

    return NextResponse.json({
      success: false,
      error: {
        message: error.message || 'Failed to check bucket contents',
        code: error.code || 'DEBUG_ERROR',
        details: error.details,
      },
    }, { status: 500 });
  }
}
