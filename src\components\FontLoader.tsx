'use client';

import { useEffect } from 'react';

export default function FontLoader() {
  useEffect(() => {
    // Create and append Google Fonts link
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Fredoka+One&family=Roboto:wght@400;500;700&display=swap';
    link.rel = 'stylesheet';
    
    // Check if the link is already added
    const existingLink = document.querySelector(`link[href="${link.href}"]`);
    if (!existingLink) {
      document.head.appendChild(link);
    }

    // Cleanup function
    return () => {
      const linkToRemove = document.querySelector(`link[href="${link.href}"]`);
      if (linkToRemove) {
        document.head.removeChild(linkToRemove);
      }
    };
  }, []);

  return null; // This component doesn't render anything
}
