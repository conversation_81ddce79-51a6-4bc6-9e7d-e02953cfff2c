'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Check } from 'lucide-react';
import { 
  getCookieConsent, 
  saveCookieConsent, 
  applyCookiePreferences,
  getDefaultPreferences,
  getAllAcceptedPreferences,
  needsConsentRenewal,
  type CookiePreferences 
} from '@/utils/gdprUtils';

const CookieConsent: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>(getDefaultPreferences());

  useEffect(() => {
    // Check if user has already made a choice
    const consent = getCookieConsent();
    if (!consent || needsConsentRenewal()) {
      setIsVisible(true);
      if (consent) {
        setPreferences(consent.preferences);
      }
    } else {
      setPreferences(consent.preferences);
      // Apply the saved preferences
      applyCookiePreferences(consent.preferences);
    }
  }, []);

  const handleAcceptAll = () => {
    const allAccepted = getAllAcceptedPreferences();
    setPreferences(allAccepted);
    saveCookieConsent(allAccepted);
    applyCookiePreferences(allAccepted);
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    const onlyNecessary = getDefaultPreferences();
    setPreferences(onlyNecessary);
    saveCookieConsent(onlyNecessary);
    applyCookiePreferences(onlyNecessary);
    setIsVisible(false);
  };

  const handleSavePreferences = () => {
    saveCookieConsent(preferences);
    applyCookiePreferences(preferences);
    setIsVisible(false);
    setShowSettings(false);
  };

  const handlePreferenceChange = (key: keyof CookiePreferences) => {
    if (key === 'necessary') return; // Necessary cookies can't be disabled
    
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 pointer-events-none">
      <div className="w-full max-w-2xl pointer-events-auto">
        <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden">
          {!showSettings ? (
            // Main consent banner
            <div className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <Cookie className="w-8 h-8 text-blue-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    We value your privacy
                  </h3>
                  <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                    We use cookies and similar technologies to enhance your experience, 
                    analyze usage patterns, and provide personalized content. You can 
                    customize your preferences or accept all cookies to continue.
                  </p>
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={handleAcceptAll}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                      Accept All
                    </button>
                    <button
                      onClick={handleRejectAll}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                      Reject All
                    </button>
                    <button
                      onClick={() => setShowSettings(true)}
                      className="bg-transparent hover:bg-gray-700 text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm font-medium transition-colors border border-gray-600 hover:border-gray-500"
                    >
                      <Settings className="w-4 h-4 inline mr-2" />
                      Customize
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Settings panel
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">
                  Cookie Preferences
                </h3>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-4 mb-6">
                {/* Necessary Cookies */}
                <div className="flex items-start justify-between p-4 bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-white">Necessary Cookies</h4>
                      <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
                        Required
                      </span>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Essential for the website to function properly. These cannot be disabled.
                    </p>
                  </div>
                  <div className="ml-4">
                    <div className="w-12 h-6 bg-green-600 rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  </div>
                </div>

                {/* Analytics Cookies */}
                <div className="flex items-start justify-between p-4 bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-white mb-2">Analytics Cookies</h4>
                    <p className="text-gray-400 text-sm">
                      Help us understand how visitors interact with our website.
                    </p>
                  </div>
                  <div className="ml-4">
                    <button
                      onClick={() => handlePreferenceChange('analytics')}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        preferences.analytics ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <div
                        className={`w-5 h-5 bg-white rounded-full transition-transform ${
                          preferences.analytics ? 'translate-x-6' : 'translate-x-0.5'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Marketing Cookies */}
                <div className="flex items-start justify-between p-4 bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-white mb-2">Marketing Cookies</h4>
                    <p className="text-gray-400 text-sm">
                      Used to track visitors and display relevant ads and marketing campaigns.
                    </p>
                  </div>
                  <div className="ml-4">
                    <button
                      onClick={() => handlePreferenceChange('marketing')}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        preferences.marketing ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <div
                        className={`w-5 h-5 bg-white rounded-full transition-transform ${
                          preferences.marketing ? 'translate-x-6' : 'translate-x-0.5'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Functional Cookies */}
                <div className="flex items-start justify-between p-4 bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-white mb-2">Functional Cookies</h4>
                    <p className="text-gray-400 text-sm">
                      Enable enhanced functionality and personalization features.
                    </p>
                  </div>
                  <div className="ml-4">
                    <button
                      onClick={() => handlePreferenceChange('functional')}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        preferences.functional ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <div
                        className={`w-5 h-5 bg-white rounded-full transition-transform ${
                          preferences.functional ? 'translate-x-6' : 'translate-x-0.5'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={handleSavePreferences}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Save Preferences
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Accept All
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
