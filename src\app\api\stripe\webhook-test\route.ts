import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook test endpoint hit');
  
  const headers = Object.fromEntries(request.headers.entries());
  const body = await request.text();
  
  console.log('Headers:', headers);
  console.log('Body:', body);
  
  return NextResponse.json({ 
    success: true, 
    message: 'Webhook test received',
    timestamp: new Date().toISOString(),
    headers: headers,
    bodyLength: body.length
  });
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Webhook test endpoint is working',
    timestamp: new Date().toISOString()
  });
}
