/**
 * Background Job Queue Service for GDPR Data Export
 * Handles queuing and processing of data export requests
 */

import { GDPRDataExportService, DataExportRequest } from './GDPRDataExportService';

export interface QueuedJob {
  id: string;
  type: 'data-export' | 'account-deletion';
  userId: string;
  requestId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  retryCount?: number;
  maxRetries?: number;
  result?: {
    filePath?: string;
    fileName?: string;
    fileSize?: number;
    [key: string]: any;
  };
}

export class BackgroundJobQueue {
  private static instance: BackgroundJobQueue;
  private jobs: Map<string, QueuedJob> = new Map();
  private isProcessing = false;
  private exportService: GDPRDataExportService;

  private constructor() {
    this.exportService = new GDPRDataExportService();
  }

  static getInstance(): BackgroundJobQueue {
    if (!BackgroundJobQueue.instance) {
      BackgroundJobQueue.instance = new BackgroundJobQueue();
    }
    return BackgroundJobQueue.instance;
  }

  /**
   * Queue a data export job
   */
  async queueDataExport(userId: string, requestId: string): Promise<QueuedJob> {
    const job: QueuedJob = {
      id: `export_${requestId}`,
      type: 'data-export',
      userId,
      requestId,
      status: 'queued',
      createdAt: new Date().toISOString(),
      maxRetries: 3,
      retryCount: 0
    };

    this.jobs.set(job.id, job);
    console.log(`📋 Queued data export job: ${job.id} for user: ${userId}`);

    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return job;
  }

  /**
   * Queue an account deletion job
   */
  async queueAccountDeletion(userId: string, requestId: string): Promise<QueuedJob> {
    const job: QueuedJob = {
      id: `delete_${requestId}`,
      type: 'account-deletion',
      userId,
      requestId,
      status: 'queued',
      createdAt: new Date().toISOString(),
      maxRetries: 2,
      retryCount: 0
    };

    this.jobs.set(job.id, job);
    console.log(`📋 Queued account deletion job: ${job.id} for user: ${userId}`);

    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return job;
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): QueuedJob | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Get all jobs for a user
   */
  getUserJobs(userId: string): QueuedJob[] {
    return Array.from(this.jobs.values()).filter(job => job.userId === userId);
  }

  /**
   * Process the job queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    console.log('🔄 Starting background job processing...');

    while (true) {
      // Find next queued job
      const queuedJob = Array.from(this.jobs.values()).find(job => job.status === 'queued');
      
      if (!queuedJob) {
        // No more jobs to process
        break;
      }

      try {
        // Update job status
        queuedJob.status = 'processing';
        queuedJob.startedAt = new Date().toISOString();
        
        console.log(`⚡ Processing job: ${queuedJob.id} (${queuedJob.type})`);

        // Process based on job type
        if (queuedJob.type === 'data-export') {
          await this.processDataExportJob(queuedJob);
        } else if (queuedJob.type === 'account-deletion') {
          await this.processAccountDeletionJob(queuedJob);
        }

        // Mark as completed
        queuedJob.status = 'completed';
        queuedJob.completedAt = new Date().toISOString();
        
        console.log(`✅ Job completed: ${queuedJob.id}`);

      } catch (error) {
        console.error(`❌ Job failed: ${queuedJob.id}`, error);
        
        // Handle retry logic
        queuedJob.retryCount = (queuedJob.retryCount || 0) + 1;
        
        if (queuedJob.retryCount >= (queuedJob.maxRetries || 3)) {
          queuedJob.status = 'failed';
          queuedJob.error = error instanceof Error ? error.message : 'Unknown error';
          queuedJob.completedAt = new Date().toISOString();
        } else {
          // Retry after delay
          queuedJob.status = 'queued';
          console.log(`🔄 Retrying job: ${queuedJob.id} (attempt ${queuedJob.retryCount + 1})`);
          
          // Add delay before retry
          await new Promise(resolve => setTimeout(resolve, 5000 * queuedJob.retryCount!));
        }
      }

      // Small delay between jobs
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.isProcessing = false;
    console.log('✅ Background job processing completed');
  }

  /**
   * Process a data export job
   */
  private async processDataExportJob(job: QueuedJob): Promise<void> {
    console.log(`📊 Processing data export for user: ${job.userId}`);
    
    const result = await this.exportService.processDataExport(job.userId, job.requestId);
    
    if (result.status === 'failed') {
      throw new Error(result.errorMessage || 'Data export failed');
    }
    
    // Store the result data including file path
    job.result = {
      filePath: result.filePath!, // The B2 file path for direct download
      fileName: `gdpr-export-${job.requestId}.zip`,
      downloadUrl: result.downloadUrl,
      exportId: result.id,
      expiresAt: result.expiresAt
    };
    
    console.log(`✅ Data export completed for user: ${job.userId}`);
  }

  /**
   * Process an account deletion job - Send notification to admin for manual processing
   */
  private async processAccountDeletionJob(job: QueuedJob): Promise<void> {
    console.log(`� Processing account deletion notification for user: ${job.userId}`);
    
    try {
      // Import email service
      const { EmailService } = await import('./EmailService');
      const emailService = EmailService.getInstance();
      
      // Get user details from Clerk
      const { clerkClient } = await import('@clerk/nextjs/server');
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(job.userId);
      
      const userEmail = user.emailAddresses.find(email => 
        email.id === user.primaryEmailAddressId
      )?.emailAddress || user.emailAddresses[0]?.emailAddress || 'No email';
      
      const userName = user.firstName || user.lastName || 'Unknown User';
      const stripeCustomerId = (user.unsafeMetadata?.stripeCustomerId as string) || 'No Stripe ID';
      
      // Send admin notification email
      await this.sendAdminDeletionNotification(
        emailService,
        job.userId,
        userEmail,
        userName,
        stripeCustomerId,
        job.id
      );
      
      // Send user confirmation that request was received
      if (userEmail !== 'No email') {
        await this.sendUserDeletionRequestConfirmation(
          emailService,
          userEmail,
          userName,
          job.id
        );
      }
      
      job.result = {
        message: 'Account deletion request sent to admin for manual processing',
        requestId: job.id,
        userEmail,
        userName,
        notifiedAt: new Date().toISOString()
      };
      
      console.log(`✅ Account deletion notification sent for user: ${job.userId}`);
      
    } catch (error) {
      console.error(`❌ Account deletion notification failed for user: ${job.userId}:`, error);
      throw error;
    }
  }

  /**
   * Send admin notification email for account deletion request
   */
  private async sendAdminDeletionNotification(
    emailService: any,
    userId: string,
    userEmail: string,
    userName: string,
    stripeCustomerId: string,
    requestId: string
  ): Promise<void> {
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    
    const subject = `🚨 GDPR Account Deletion Request - ${userName} (${userEmail})`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
          .user-details { background: #e9ecef; padding: 15px; border-radius: 4px; font-family: monospace; }
          .actions { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 GDPR Account Deletion Request</h1>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          </div>
          
          <div class="warning">
            <strong>⚠️ URGENT: Manual Processing Required</strong><br>
            A user has requested complete account deletion under GDPR Article 17 (Right to Erasure).
            This requires manual verification and processing.
          </div>
          
          <h3>👤 User Details:</h3>
          <div class="user-details">
            User ID: ${userId}<br>
            Name: ${userName}<br>
            Email: ${userEmail}<br>
            Stripe Customer ID: ${stripeCustomerId}<br>
            Request ID: ${requestId}
          </div>
          
          <h3>🔍 Before Processing - Check:</h3>
          <ul>
            <li><strong>Stripe Subscription Status:</strong> Active? Lifetime? Recent payment?</li>
            <li><strong>Account Activity:</strong> Recent login? Recent usage?</li>
            <li><strong>Support History:</strong> Any recent tickets or issues?</li>
            <li><strong>Accidental Request:</strong> Consider reaching out to confirm</li>
          </ul>
          
          <div class="actions">
            <h3>📋 Manual Deletion Process:</h3>
            <ol>
              <li><strong>Verify Request:</strong> Contact user if needed to confirm</li>
              <li><strong>Handle Billing:</strong> 
                <ul>
                  <li>Recent subscriptions: Consider refund</li>
                  <li>Lifetime: Offer refund or keep billing separate</li>
                  <li>Cancel/modify in Stripe dashboard</li>
                </ul>
              </li>
              <li><strong>Delete Data:</strong> 
                <ul>
                  <li>Remove all projects from B2 storage</li>
                  <li>Delete user from Clerk dashboard</li>
                  <li>Clean up any export files</li>
                </ul>
              </li>
              <li><strong>Confirm:</strong> Send deletion confirmation email to user</li>
            </ol>
          </div>
          
          <h3>🔗 Quick Links:</h3>
          <ul>
            <li><a href="https://dashboard.stripe.com/customers/${stripeCustomerId}">Stripe Customer Dashboard</a></li>
            <li><a href="https://dashboard.clerk.com">Clerk User Management</a></li>
            <li><a href="https://secure.backblaze.com/b2_buckets.htm">Backblaze B2 Console</a></li>
          </ul>
          
          <p><strong>⏰ GDPR Compliance:</strong> Process within 30 days of request.</p>
        </div>
      </body>
      </html>
    `;
    
    await emailService.sendEmailPublic({
      to: adminEmail,
      subject,
      html,
      text: `GDPR Account Deletion Request - Manual Processing Required
      
User: ${userName} (${userEmail})
User ID: ${userId}
Stripe ID: ${stripeCustomerId}
Request ID: ${requestId}
Submitted: ${new Date().toLocaleString()}

Please process this deletion request manually by checking Stripe, Clerk, and B2 storage.
Respond within 30 days for GDPR compliance.`
    });
  }

  /**
   * Send user confirmation that deletion request was received
   */
  private async sendUserDeletionRequestConfirmation(
    emailService: any,
    userEmail: string,
    userName: string,
    requestId: string
  ): Promise<void> {
    const subject = 'WordWave Studio - Account Deletion Request Received';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .content { background: white; padding: 20px; }
          .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin: 20px 0; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Account Deletion Request Received</h1>
          </div>
          
          <div class="content">
            <p>Hello ${userName || 'there'},</p>
            
            <p>We have received your request to delete your WordWave Studio account under GDPR Article 17 (Right to Erasure).</p>
            
            <div class="info">
              <strong>📋 Request Details:</strong><br>
              Request ID: ${requestId}<br>
              Submitted: ${new Date().toLocaleString()}<br>
              Status: Pending manual review
            </div>
            
            <h3>⏰ What Happens Next:</h3>
            <ul>
              <li><strong>Manual Review:</strong> Our team will review your request within 1-2 business days</li>
              <li><strong>Subscription Check:</strong> We'll verify your billing status and handle appropriately</li>
              <li><strong>Confirmation:</strong> We may contact you to confirm this wasn't accidental</li>
              <li><strong>Processing:</strong> Complete deletion within 30 days (GDPR requirement)</li>
            </ul>
            
            <h3>🔄 Changed Your Mind?</h3>
            <p>If you submitted this request by accident, please contact us immediately at <a href="mailto:<EMAIL>"><EMAIL></a> with your request ID: <strong>${requestId}</strong></p>
            
            <h3>💰 Billing Information:</h3>
            <p>If you have an active subscription, we'll review your billing status and handle refunds or cancellations according to our policy and the timing of your request.</p>
            
            <div class="footer">
              <p>Best regards,<br>
              <strong>WordWave Studio Team</strong></p>
              
              <p style="margin-top: 20px; font-size: 12px;">
                This email confirms receipt of your GDPR deletion request.<br>
                WordWave Studio • <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
    
    await emailService.sendEmailPublic({
      to: userEmail,
      subject,
      html,
      text: `Account Deletion Request Received - WordWave Studio

Hello ${userName || 'there'},

We have received your request to delete your WordWave Studio account under GDPR Article 17.

Request Details:
- Request ID: ${requestId}
- Submitted: ${new Date().toLocaleString()}
- Status: Pending manual review

What Happens Next:
1. Manual Review: Our team will review within 1-2 business days
2. Subscription Check: We'll verify billing and handle appropriately
3. Confirmation: We may contact you to confirm this wasn't accidental
4. Processing: Complete deletion within 30 days (GDPR requirement)

Changed Your Mind?
If this was accidental, contact <EMAIL> immediately with request ID: ${requestId}

Thank you,
WordWave Studio Team`
    });
  }

  /**
   * Clean up old completed jobs (call this periodically)
   */
  cleanupOldJobs(olderThanDays: number = 30): void {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    
    for (const [jobId, job] of this.jobs.entries()) {
      if (job.completedAt && new Date(job.completedAt) < cutoffDate) {
        this.jobs.delete(jobId);
        console.log(`🧹 Cleaned up old job: ${jobId}`);
      }
    }
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): {
    total: number;
    queued: number;
    processing: number;
    completed: number;
    failed: number;
  } {
    const jobs = Array.from(this.jobs.values());
    
    return {
      total: jobs.length,
      queued: jobs.filter(j => j.status === 'queued').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length
    };
  }
}
