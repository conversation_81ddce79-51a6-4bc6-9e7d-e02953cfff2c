# GDPR Automated Data Export - Configuration Guide

## Overview
The automated GDPR data export system has been successfully implemented with the following features:

- ✅ **Background Job Processing**: Data exports are processed asynchronously
- ✅ **Email Notifications**: Users receive email when exports are ready
- ✅ **Secure Downloads**: Files are stored in B2 and downloaded securely
- ✅ **Admin Monitoring**: Dashboard to monitor export jobs and system health
- ✅ **File Expiration**: Exports automatically expire after 7 days

## Required Environment Variables

Add these to your `.env.local` file:

```bash
# Email Service (choose one)
RESEND_API_KEY=your_resend_api_key_here
# OR
SENDGRID_API_KEY=your_sendgrid_api_key_here

# B2 Storage (already configured)
B2_APPLICATION_KEY_ID=your_b2_key_id
B2_APPLICATION_KEY=your_b2_application_key
B2_BUCKET_NAME=your_bucket_name

# App URL for download links
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

## Email Service Setup

### Option 1: Sender (Recommended - 15,000 free emails/month)
1. Sign up at [sender.net](https://www.sender.net/)
2. Create an API key in Settings → API & Webhooks
3. Add `SENDER_API_KEY=sender_xxx` to your environment variables
4. The EmailService.ts automatically detects and uses Sender when available

### Option 2: Resend (3,000 free emails/month)
1. Sign up at [resend.com](https://resend.com)
2. Create an API key
3. Add `RESEND_API_KEY=re_xxx` to your environment variables
4. EmailService.ts will use Resend as fallback if Sender is not configured

### Option 3: SendGrid
1. Sign up at [sendgrid.com](https://sendgrid.com)
2. Create an API key
3. Add `SENDGRID_API_KEY=SG.xxx` to your environment variables
4. Install SendGrid: `npm install @sendgrid/mail`
5. EmailService.ts will use SendGrid as last fallback

**Note:** The EmailService now supports multiple providers with automatic fallback:
1. Sender (if SENDER_API_KEY exists) - **Recommended**
2. Resend (if RESEND_API_KEY exists)
3. SendGrid (if SENDGRID_API_KEY exists)
4. Development mode (logs only)

## Admin Access Control

The admin monitoring dashboard currently allows any authenticated user. To restrict access:

1. Add admin role checking in `/api/gdpr/admin/stats/route.ts`:

```typescript
// Add this function to check admin role
async function checkAdminRole(userId: string): Promise<boolean> {
  // Implement your admin role checking logic
  // This could check a database, Clerk metadata, etc.
  const user = await clerkClient.users.getUser(userId);
  return user.publicMetadata?.role === 'admin';
}

// Then use it in the GET handler:
const isAdmin = await checkAdminRole(userId);
if (!isAdmin) {
  return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
}
```

## Testing the System

1. **Request Export**: Visit `/privacy-dashboard` and click "Request Data Export"
2. **Check Status**: The page will show real-time status updates
3. **Download**: Once complete, click the download link
4. **Admin Monitor**: Visit `/privacy-dashboard` (implement admin route) to see job queue

## File Structure Overview

```
src/
├── lib/
│   ├── GDPRDataExportService.ts     # Core export processing
│   ├── BackgroundJobQueue.ts        # Job queue management
│   ├── EmailService.ts              # Email notifications
│   └── CloudStorageService.ts       # B2 file operations
├── app/api/gdpr/
│   ├── export/route.ts              # Start export API
│   ├── status/route.ts              # Check status API
│   ├── download/[requestId]/route.ts # Download file API
│   └── admin/stats/route.ts         # Admin monitoring API
└── components/
    ├── GDPRDataRights.tsx           # User interface
    └── GDPRJobMonitor.tsx           # Admin dashboard
```

## What Data is Exported

The system collects and exports:

1. **User Account Data** (from Clerk)
   - Profile information
   - Email, phone, metadata
   - Authentication details

2. **Project Data** (from B2 Storage)
   - All user projects and files
   - Metadata and settings
   - Audio files and transcripts

3. **Subscription Data** (from Stripe)
   - Billing history
   - Subscription details
   - Payment methods

4. **Usage Analytics**
   - Feature usage statistics
   - Performance metrics

5. **Support Communications**
   - Support tickets and responses
   - Chat history

## Security Features

- **User Verification**: Only authenticated users can request their own data
- **Secure Storage**: Files stored in private B2 bucket
- **Automatic Expiration**: Downloads expire after 7 days
- **Audit Trail**: All requests logged with timestamps
- **Background Processing**: Prevents timeout issues

## Monitoring and Maintenance

- **Job Queue Cleanup**: Old jobs are automatically cleaned up after 30 days
- **File Cleanup**: Export files should be cleaned up after expiration (add cron job)
- **Error Handling**: Failed jobs are retried with exponential backoff
- **Email Notifications**: Users notified of completion and expiration

## Next Steps

1. Configure email service (Resend recommended)
2. Set up admin role checking
3. Test the complete workflow
4. Add file cleanup cron job
5. Monitor system performance

The system is production-ready once email configuration is complete!
