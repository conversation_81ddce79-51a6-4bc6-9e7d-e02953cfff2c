// Cloud Storage Components for WordWave Studio - Next.js Version
import React, { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';
import { useApiClient } from '../services/ApiClient';
import Modal from './Modal';
import './CloudStorage.css';

interface CloudStorageProps {
  onProjectLoaded?: (projectData: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface SaveProjectButtonProps {
  projectName: string;
  topic: string;
  scriptLinks: string;
  synthesisMode: 'monologue' | 'podcast';
  selectedScriptModel: string;
  selectedTtsModel: string;
  voice1?: string;
  voice2?: string;
  speaker1Name?: string;
  speaker2Name?: string;
  projectStyle?: string;
  wordCount?: number;
  inputText: string;
  audioFiles: any[];
  onSaveComplete?: (projectId: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}



export const CloudStorage: React.FC<CloudStorageProps> = ({
  onProjectLoaded,
  onError,
  className = ''
}) => {
  const { user, isSignedIn } = useUser();
  const apiClient = useApiClient();
  const [projects, setProjects] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState({
    isOpen: false,
    projectId: '',
    projectName: ''
  });

  // Skip health check to avoid rate limiting - assume healthy if signed in
  useEffect(() => {
    if (isSignedIn) {
      setIsHealthy(true);
      setError(null);
    }
  }, [isSignedIn]);

  // Load projects list
  const loadProjects = useCallback(async () => {
    if (!isSignedIn || !isHealthy) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await apiClient.listProjects();
      setProjects(result.projects || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load projects';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [isSignedIn, isHealthy, apiClient, onError]);

  // Load projects when service is ready - only run once when isHealthy becomes true
  useEffect(() => {
    if (isHealthy && isSignedIn) {
      loadProjects();
    }
  }, [isHealthy, isSignedIn]); // Removed loadProjects from dependencies to prevent infinite loop

  const handleLoadProject = async (projectId: string) => {
    if (!isSignedIn) return;

    setIsLoading(true);
    setError(null);

    try {
      const projectData = await apiClient.loadProject(projectId);
      onProjectLoaded?.(projectData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load project';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!isSignedIn) return;
    
    // Find the project name
    const project = projects.find(p => p.projectId === projectId);
    const projectName = project?.projectName || 'Unknown Project';
    
    // Open the confirmation modal instead of using browser confirm
    setDeleteConfirmModal({
      isOpen: true,
      projectId,
      projectName
    });
  };

  const confirmDeleteProject = () => {
    if (!isSignedIn || !apiClient) return;
    
    const { projectId, projectName } = deleteConfirmModal;

    // Start the deletion process asynchronously
    (async () => {
      setIsLoading(true);
      setError(null);

      try {
        await apiClient.deleteProject(projectId);
        await loadProjects(); // Refresh the list
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to delete project';
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    })();
    
    // Close the modal immediately
    setDeleteConfirmModal({
      isOpen: false,
      projectId: '',
      projectName: ''
    });
  };

  if (!isSignedIn) {
    return (
      <div className={`cloud-storage-section ${className}`}>
        <p>Please sign in to access cloud storage features.</p>
      </div>
    );
  }

  if (isHealthy === null) {
    return (
      <div className={`cloud-storage-section ${className}`}>
        <p>Checking cloud storage availability...</p>
      </div>
    );
  }

  if (isHealthy === false) {
    return (
      <div className={`cloud-storage-section ${className}`}>
        <div className="error-message">
          <p>⚠️ Cloud storage is currently unavailable</p>
          <p>Please check your Backblaze B2 credentials in .env.local</p>
          {error && <p>Error: {error}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className={`cloud-storage-section ${className}`}>
      <div className="cloud-storage-header">
        <h3>☁️ Cloud Storage</h3>
        <button
          type="button"
          onClick={loadProjects}
          disabled={isLoading}
          className="refresh-button"
        >
          🔄 Refresh
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>⚠️ {error}</p>
        </div>
      )}

      {isLoading && (
        <div className="loading-message">
          <p>Loading projects...</p>
        </div>
      )}

      {projects.length === 0 && !isLoading && (
        <div className="empty-state">
          <p>No saved projects found. Save your first project to see it here!</p>
        </div>
      )}

      {projects.length > 0 && (
        <div className="projects-list">
          {projects.map((project) => (
            <div key={project.projectId} className="project-item">
              <div className="project-info">
                <h4>{project.projectName}</h4>
                <p className="project-meta">
                  Updated: {new Date(project.updatedAt).toLocaleDateString()}
                </p>
                <p className="project-stats">
                  {project.fileCount} files
                </p>
              </div>
              <div className="project-actions">
                <button
                  type="button"
                  onClick={() => handleLoadProject(project.projectId)}
                  disabled={isLoading}
                  className="load-button"
                >
                  📥 Load
                </button>
                <button
                  type="button"
                  onClick={() => handleDeleteProject(project.projectId)}
                  disabled={isLoading}
                  className="delete-button"
                >
                  🗑️ Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteConfirmModal.isOpen}
        onClose={() => setDeleteConfirmModal({ isOpen: false, projectId: '', projectName: '' })}
        title="Delete Project"
        message={`Are you sure you want to delete "${deleteConfirmModal.projectName}"? This action cannot be undone.`}
        type="warning"
        confirmText="Delete Project"
        onConfirm={confirmDeleteProject}
      />
    </div>
  );
};

export const SaveProjectButton: React.FC<SaveProjectButtonProps> = ({
  projectName,
  topic,
  scriptLinks,
  synthesisMode,
  selectedScriptModel,
  selectedTtsModel,
  voice1 = 'zephyr',
  voice2 = 'puck',
  speaker1Name = 'Speaker 1',
  speaker2Name = 'Speaker 2',
  projectStyle = 'Casual conversation',
  wordCount = 500,
  inputText,
  audioFiles,
  onSaveComplete,
  onError,
  disabled = false,
  className = ''
}) => {
  const { user, isSignedIn } = useUser();
  const apiClient = useApiClient();
  const [isSaving, setIsSaving] = useState(false);
  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);

  // Skip health check to avoid rate limiting - assume healthy if signed in
  useEffect(() => {
    if (isSignedIn) {
      setIsHealthy(true);
    }
  }, [isSignedIn]);

  const handleSaveProject = async () => {
    if (!isSignedIn || !user || !projectName.trim() || !isHealthy) {
      onError?.('Please ensure you have a project name, are signed in, and cloud storage is available');
      return;
    }

    setIsSaving(true);

    try {
      // Create a simple project data structure
      const projectData = {
        id: `proj_${Date.now()}`,
        configuration: {
          projectName,
          topic,
          scriptLinks,
          synthesisMode,
          selectedScriptModel,
          selectedTtsModel,
          voice1,
          voice2,
          defaultVoice1: voice1,
          defaultVoice2: voice2,
          speaker1Name,
          speaker2Name,
          projectStyle,
          wordCount,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        script: inputText ? {
          content: inputText,
          generatedAt: new Date().toISOString(),
          model: selectedScriptModel,
        } : undefined,
        audioFiles: audioFiles || [],
        metadata: {
          version: '1.0.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: user.id,
          totalSize: 0,
          fileCount: audioFiles?.length || 0,
        },
      };

      const result = await apiClient.saveProject(projectData, {
        includeAudio: true,
        compress: true
      });

      onSaveComplete?.(result.projectId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save project';
      onError?.(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const canSave = isSignedIn && isHealthy && projectName.trim() && !disabled;



  if (!isSignedIn) {
    return (
      <div className={`save-project-container ${className}`}>
        <button type="button" disabled className="save-project-button">
          🔒 Sign in to Save
        </button>
      </div>
    );
  }

  if (isHealthy === false) {
    return (
      <div className={`save-project-container ${className}`}>
        <button type="button" disabled className="save-project-button">
          ⚠️ Cloud Storage Unavailable
        </button>
      </div>
    );
  }

  return (
    <div className={`save-project-container ${className}`}>
      <button
        type="button"
        onClick={handleSaveProject}
        disabled={!canSave || isSaving}
        className="save-project-button"
      >
        {isSaving ? '☁️ Saving...' : '💾 Save to Cloud'}
      </button>

    </div>
  );
};
