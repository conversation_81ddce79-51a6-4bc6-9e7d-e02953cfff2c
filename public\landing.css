/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #EAEAEA;
    background-color: #1B263B; /* Fallback background color */
    overflow-x: hidden;
}

/* Ensure background components are properly positioned */
#waves-background-root,
#noise-overlay-root {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

#waves-background-root {
    z-index: -2;
}

#noise-overlay-root {
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(128, 152, 172, 0.2);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h1 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.5rem;
    color: #E0E7FF;
    font-weight: normal; /* Fredoka One is inherently bold */
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a {
    color: #CFD8DC;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #82AAE3;
}

.cta-button-nav {
    background: #3498DB !important;
    color: white !important;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.cta-button-nav:hover {
    background: #2980B9 !important;
    transform: translateY(-1px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #CFD8DC;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    position: relative;
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-family: 'Fredoka One', sans-serif;
    font-size: 3.5rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    line-height: 1.1;
    margin-bottom: 24px;
    color: #E0E7FF;
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25),
                 0 0 15px rgba(130, 170, 227, 0.15);
}

.highlight {
    background: linear-gradient(135deg, #3498DB, #82AAE3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #B0C4DE;
    margin-bottom: 32px;
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: 16px;
    margin-bottom: 40px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.cta-button.primary {
    background: linear-gradient(135deg, #3498DB, #2980B9);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.cta-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.cta-button.secondary {
    background: transparent;
    color: #82AAE3;
    border: 2px solid #82AAE3;
}

.cta-button.secondary:hover {
    background: rgba(130, 170, 227, 0.1);
    transform: translateY(-1px);
}

.cta-button.large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.hero-features {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(44, 62, 80, 0.8);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid rgba(128, 152, 172, 0.3);
    font-size: 0.9rem;
    color: #CFD8DC;
}

.feature-badge .icon {
    font-size: 1.2rem;
}

/* Demo Player */
.demo-player {
    background: rgba(44, 62, 80, 0.95);
    border-radius: 16px;
    border: 1px solid rgba(128, 152, 172, 0.4);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.player-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: rgba(27, 38, 59, 0.9);
    border-bottom: 1px solid rgba(128, 152, 172, 0.3);
}

.player-controls {
    display: flex;
    gap: 8px;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-dot.red { background: #E74C3C; }
.control-dot.yellow { background: #F39C12; }
.control-dot.green { background: #27AE60; }

.player-title {
    font-weight: 600;
    color: #E8F4FD;
    font-size: 0.95rem;
}

.player-content {
    padding: 32px 24px;
}

.audio-demo-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.demo-info {
    text-align: center;
}

.demo-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #E8F4FD;
    margin: 0 0 8px 0;
}

.demo-description {
    font-size: 0.9rem;
    color: #B0C4DE;
    margin: 0;
}

.audio-player-container {
    display: flex;
    justify-content: center;
    margin: 8px 0;
}

/* Use the same audio player styling as the main app */
.demo-player .audio-player {
    width: 100%;
    height: 45px;
    max-width: 320px;
}

.demo-player .audio-player::-webkit-media-controls-panel {
    background-color: rgba(69, 90, 100, 0.8);
}

.demo-player .audio-player::-webkit-media-controls-play-button,
.demo-player .audio-player::-webkit-media-controls-timeline,
.demo-player .audio-player::-webkit-media-controls-current-time-display,
.demo-player .audio-player::-webkit-media-controls-time-remaining-display,
.demo-player .audio-player::-webkit-media-controls-mute-button,
.demo-player .audio-player::-webkit-media-controls-volume-slider,
.demo-player .audio-player::-webkit-media-controls-overflow-button {
    filter: brightness(0) invert(1); /* Makes controls white */
}

.waveform {
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 2px;
    height: 60px;
    margin: 16px 0;
    padding: 0 20px;
}

.wave-bar {
    width: 3px;
    background: linear-gradient(to top, #3498DB, #5DADE2, #85C1E9);
    border-radius: 2px;
    animation: wave-pulse 2.5s ease-in-out infinite;
    box-shadow: 0 0 4px rgba(52, 152, 219, 0.3);
}

.wave-bar:nth-child(odd) {
    animation-delay: 0.1s;
}

.wave-bar:nth-child(even) {
    animation-delay: 0.2s;
}

.wave-bar:nth-child(3n) {
    animation-delay: 0.3s;
}

@keyframes wave-pulse {
    0%, 100% {
        opacity: 0.5;
        transform: scaleY(0.8);
    }
    50% {
        opacity: 1;
        transform: scaleY(1.1);
    }
}

.speaker-dialogue {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.dialogue-line {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px 16px;
    border-radius: 8px;
    background: rgba(27, 38, 59, 0.6);
    border-left: 3px solid;
}

.dialogue-line.speaker-1 {
    border-left-color: #3498DB;
}

.dialogue-line.speaker-2 {
    border-left-color: #E67E22;
}

.speaker-tag {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.speaker-1 .speaker-tag {
    color: #5DADE2;
}

.speaker-2 .speaker-tag {
    color: #F39C12;
}

.dialogue-text {
    font-size: 0.9rem;
    color: #CFD8DC;
    font-style: italic;
    line-height: 1.4;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 2.5rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 16px;
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.section-header p {
    font-size: 1.2rem;
    color: #B0C4DE;
}

/* Features Section */
.features {
    padding: 80px 0;
    position: relative;
    z-index: 1;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: rgba(44, 62, 80, 0.8);
    border: 1px solid rgba(128, 152, 172, 0.3);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(130, 170, 227, 0.5);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
    display: block;
}

.feature-card h3 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.4rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 16px;
    letter-spacing: 0.02em;
}

.feature-card p {
    color: #B0C4DE;
    margin-bottom: 20px;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    color: #CFD8DC;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.feature-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27AE60;
    font-weight: bold;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0;
    background: rgba(27, 38, 59, 0.3);
    position: relative;
    z-index: 1;
}

.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    max-width: 900px;
    margin: 0 auto;
}

.step {
    text-align: center;
    flex: 1;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498DB, #2980B9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.step h3 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.3rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 12px;
    letter-spacing: 0.02em;
}

.step p {
    color: #B0C4DE;
    line-height: 1.6;
}

.step-arrow {
    font-size: 2rem;
    color: #82AAE3;
    font-weight: bold;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    position: relative;
    z-index: 1;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.benefit-card {
    text-align: center;
    padding: 40px 20px;
    background: rgba(44, 62, 80, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(128, 152, 172, 0.2);
    transition: all 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-3px);
    border-color: rgba(130, 170, 227, 0.4);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.benefit-card h3 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.3rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 12px;
    letter-spacing: 0.02em;
}

.benefit-card p {
    color: #B0C4DE;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: rgba(27, 38, 59, 0.3);
    position: relative;
    z-index: 1;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: rgba(44, 62, 80, 0.8);
    border: 1px solid rgba(128, 152, 172, 0.3);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(130, 170, 227, 0.5);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: #3498DB;
    font-family: serif;
    line-height: 1;
}

.testimonial-content {
    margin-bottom: 20px;
}

.testimonial-content p {
    color: #B0C4DE;
    font-style: italic;
    line-height: 1.6;
    font-size: 1.1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498DB, #2980B9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.author-info h4 {
    color: #E0E7FF;
    margin-bottom: 4px;
    font-weight: 600;
}

.author-info span {
    color: #90A4AE;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: rgba(27, 38, 59, 0.5);
    text-align: center;
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 2.5rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 16px;
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.cta-content p {
    font-size: 1.2rem;
    color: #B0C4DE;
    margin-bottom: 32px;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.cta-note {
    font-size: 0.9rem;
    color: #90A4AE;
    font-style: italic;
}

/* Footer */
.footer {
    background: rgba(27, 38, 59, 0.8);
    border-top: 1px solid rgba(128, 152, 172, 0.2);
    padding: 60px 0 30px;
    position: relative;
    z-index: 1;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand h3 {
    font-family: 'Fredoka One', sans-serif;
    font-size: 1.5rem;
    font-weight: normal; /* Fredoka One is inherently bold */
    color: #E0E7FF;
    margin-bottom: 12px;
    letter-spacing: 0.03em;
    text-shadow: 0 0 8px rgba(130, 170, 227, 0.25);
}

.footer-brand p {
    color: #B0C4DE;
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-column h4 {
    color: #CFD8DC;
    margin-bottom: 16px;
    font-weight: 600;
}

.footer-column a {
    display: block;
    color: #90A4AE;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #82AAE3;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(128, 152, 172, 0.2);
    color: #90A4AE;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    /* Demo Player Mobile Styles */
    .demo-player {
        margin: 0 10px;
    }

    .player-content {
        padding: 24px 16px;
    }

    .demo-title {
        font-size: 1rem;
    }

    .demo-description {
        font-size: 0.85rem;
    }

    .waveform {
        height: 50px;
        padding: 0 10px;
    }

    .wave-bar {
        width: 2px;
    }

    .dialogue-line {
        padding: 10px 12px;
    }

    .dialogue-text {
        font-size: 0.85rem;
    }

    .steps-container {
        flex-direction: column;
        gap: 30px;
    }

    .step-arrow {
        transform: rotate(90deg);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    /* Demo Player Small Mobile Styles */
    .demo-player {
        margin: 0 5px;
        border-radius: 12px;
    }

    .player-header {
        padding: 12px 16px;
    }

    .player-content {
        padding: 20px 12px;
    }

    .audio-demo-section {
        gap: 16px;
    }

    .demo-title {
        font-size: 0.95rem;
    }

    .waveform {
        height: 40px;
        gap: 1px;
    }

    .dialogue-line {
        padding: 8px 10px;
    }

    .speaker-tag {
        font-size: 0.75rem;
    }

    .dialogue-text {
        font-size: 0.8rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }
}
