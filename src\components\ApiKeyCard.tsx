'use client';

import React, { useState } from 'react';
import { useApiKey } from '@/hooks/useApiKey';
import InfoBanner from './InfoBanner';
import ShinyText from '@/components/ShinyText';

export const ApiKeyCard: React.FC = () => {
  const { apiKey, setApiKey, clearApiKey } = useApiKey();
  const [isEditingApiKey, setIsEditingApiKey] = useState(false);
  const [tempApiKey, setTempApiKey] = useState('');

  const handleEditApiKey = () => {
    setTempApiKey(apiKey || '');
    setIsEditingApiKey(true);
  };

  const handleSaveApiKey = () => {
    if (tempApiKey.trim()) {
      setApiKey(tempApiKey.trim());
    }
    setIsEditingApiKey(false);
    setTempApiKey('');
  };

  const handleCancelApiKey = () => {
    setIsEditingApiKey(false);
    setTempApiKey('');
  };

  const handleClearApiKey = () => {
    clearApiKey();
    setIsEditingApiKey(false);
    setTempApiKey('');
  };

  return (
    <div className="bg-slate-800/40 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 flex items-center justify-center text-2xl">
            <span role="img" aria-label="api-key">🔑</span>
          </div>
          <h2 className="text-xl font-semibold text-white brightness-125 font-fredoka">
            <ShinyText text="1. API Configuration" />
          </h2>
        </div>
        <div className="flex flex-col items-end gap-1">
          <a 
            href="https://aistudio.google.com/apikey" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-400 hover:text-blue-300 underline font-medium transition-colors text-sm"
          >
            🚀 Get your free API key here!
          </a>
          <a 
            href="https://ai.google.dev/gemini-api/docs/rate-limits" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-400 hover:text-blue-300 underline font-medium transition-colors text-xs"
          >
            📊 Check free quotas & limits
          </a>
        </div>
      </div>

      <div className="space-y-4">
        {!apiKey && !isEditingApiKey && (
          <div className="text-center space-y-4">
            <InfoBanner
              type="info"
              icon="🚀"
              message=""
            >
              <p className="text-sm mb-2">
                <strong>Get Started:</strong> Configure your Google Gemini API key to unlock script generation and text-to-speech synthesis.
              </p>
              <p className="text-blue-300 text-xs">
                Visit <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">Google AI Studio</a> to get your free API key.
                <br />
                Check <a href="https://ai.google.dev/gemini-api/docs/rate-limits" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">rate limits & free quotas</a> to see how many tokens you get.
              </p>
            </InfoBanner>
            <button
              type="button"
              onClick={handleEditApiKey}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              🔑 Add Google Gemini API Key
            </button>
          </div>
        )}

        {apiKey && !isEditingApiKey && (
          <div className="space-y-4">
            <InfoBanner
              type="success"
              icon="✅"
              message=""
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm font-medium">API Key Configured</span>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={handleEditApiKey}
                    className="text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={handleClearApiKey}
                    className="text-red-400 hover:text-red-300 text-xs font-medium transition-colors"
                  >
                    Remove
                  </button>
                </div>
              </div>
              <div>
                <p className="text-gray-400 text-xs">
                  Key: {apiKey.substring(0, 8)}...{apiKey.substring(apiKey.length - 4)} (Length: {apiKey.length})
                </p>
              </div>
            </InfoBanner>
            
            <div className="bg-slate-700/30 rounded-lg p-3">
              <p className="text-gray-300 text-sm mb-2">
                💡 <strong>Tip:</strong> Your API key is safely stored locally and enables:
              </p>
              <ul className="text-gray-400 text-xs space-y-1 ml-4">
                <li>• AI-powered script generation</li>
                <li>• High-quality text-to-speech synthesis</li>
                <li>• 30+ natural-sounding voices</li>
                <li>• Multi-speaker conversations</li>
              </ul>
            </div>
          </div>
        )}

        {isEditingApiKey && (
          <div className="space-y-4">
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <p className="text-blue-200 text-sm mb-2">
                🔐 <strong>API Key Setup:</strong>
              </p>
              <ol className="text-blue-300 text-xs space-y-1 ml-4">
                <li>1. Visit <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">Google AI Studio</a></li>
                <li>2. Create a new API key</li>
                <li>3. Copy and paste it below</li>
              </ol>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Google Gemini API Key:
              </label>
              <input
                type="password"
                value={tempApiKey}
                onChange={(e) => setTempApiKey(e.target.value)}
                placeholder="Enter your Gemini API key (starts with AIza...)"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleSaveApiKey}
                disabled={!tempApiKey.trim()}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-2 rounded-lg transition-colors text-sm font-medium"
              >
                💾 Save
              </button>
              <button
                type="button"
                onClick={handleCancelApiKey}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg transition-colors text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
